{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/agency/apollo-agency-website/src/app/services/seo/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useLanguage } from '@/contexts/LanguageContext';\nimport Link from 'next/link';\n\nexport default function SEOPage() {\n  const { t, isRTL } = useLanguage();\n\n  return (\n    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : ''}`}>\n      {/* Hero Section */}\n      <section className=\"bg-gradient-to-br from-green-900 via-green-800 to-blue-600 text-white py-20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-4xl mx-auto text-center\">\n            <div className=\"w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6\">\n              <span className=\"text-3xl\">🔍</span>\n            </div>\n            <h1 className=\"text-4xl md:text-6xl font-bold mb-6\">\n              {t('services.seo')}\n            </h1>\n            <p className=\"text-xl md:text-2xl mb-8 opacity-90\">\n              Improve your website's visibility and rank higher in search results\n            </p>\n            <Link\n              href=\"/contact\"\n              className=\"inline-block bg-gradient-to-r from-yellow-400 to-orange-500 text-gray-900 font-bold px-8 py-4 rounded-full hover:from-yellow-300 hover:to-orange-400 transition-all duration-300 transform hover:scale-105\"\n            >\n              Start SEO Audit\n            </Link>\n          </div>\n        </div>\n      </section>\n\n      {/* SEO Services */}\n      <section className=\"py-20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-6xl mx-auto\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-6\">\n                Comprehensive SEO Services\n              </h2>\n              <p className=\"text-xl text-gray-600\">\n                Everything you need to dominate search engine results\n              </p>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8\">\n              {[\n                {\n                  icon: '🔎',\n                  title: 'Keyword Research & Analysis',\n                  description: 'Identify high-value keywords that your target audience is searching for.',\n                  features: ['Competitor keyword analysis', 'Search volume research', 'Keyword difficulty assessment', 'Long-tail keyword opportunities']\n                },\n                {\n                  icon: '⚙️',\n                  title: 'Technical SEO Audit',\n                  description: 'Comprehensive technical analysis to identify and fix SEO issues.',\n                  features: ['Site speed optimization', 'Mobile responsiveness check', 'URL structure analysis', 'Schema markup implementation']\n                },\n                {\n                  icon: '📝',\n                  title: 'On-Page SEO Optimization',\n                  description: 'Optimize your website content and structure for better rankings.',\n                  features: ['Title tag optimization', 'Meta description writing', 'Header tag structure', 'Internal linking strategy']\n                },\n                {\n                  icon: '🔗',\n                  title: 'Link Building Strategy',\n                  description: 'Build high-quality backlinks to increase your domain authority.',\n                  features: ['Guest posting opportunities', 'Resource page outreach', 'Broken link building', 'Local citation building']\n                },\n                {\n                  icon: '📍',\n                  title: 'Local SEO Services',\n                  description: 'Optimize your business for local search results and Google My Business.',\n                  features: ['Google My Business optimization', 'Local citation management', 'Review management', 'Local keyword targeting']\n                },\n                {\n                  icon: '📊',\n                  title: 'SEO Analytics & Reporting',\n                  description: 'Track your SEO performance with detailed analytics and reports.',\n                  features: ['Ranking position tracking', 'Organic traffic analysis', 'Conversion tracking', 'Monthly SEO reports']\n                }\n              ].map((service, index) => (\n                <div key={index} className=\"bg-white rounded-xl p-8 shadow-lg hover:shadow-xl transition-all duration-300\">\n                  <div className=\"text-4xl mb-4\">{service.icon}</div>\n                  <h3 className=\"text-2xl font-bold text-gray-900 mb-3\">{service.title}</h3>\n                  <p className=\"text-gray-600 mb-4\">{service.description}</p>\n                  <ul className=\"space-y-2\">\n                    {service.features.map((feature, featureIndex) => (\n                      <li key={featureIndex} className=\"flex items-center text-gray-600\">\n                        <span className=\"text-green-600 mr-2\">✓</span>\n                        {feature}\n                      </li>\n                    ))}\n                  </ul>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Why Choose Us */}\n      <section className=\"py-20 bg-white\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-4xl mx-auto text-center\">\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-16\">\n              Why Choose Our SEO Services?\n            </h2>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n              {[\n                { \n                  icon: '🏆', \n                  title: 'Proven Results', \n                  description: 'We have helped hundreds of businesses achieve top rankings and increase organic traffic.' \n                },\n                { \n                  icon: '🔬', \n                  title: 'Data-Driven Approach', \n                  description: 'Our strategies are based on thorough research and analysis, not guesswork.' \n                },\n                { \n                  icon: '🤝', \n                  title: 'Transparent Reporting', \n                  description: 'Regular updates and detailed reports keep you informed about your SEO progress.' \n                }\n              ].map((item, index) => (\n                <div key={index} className=\"text-center\">\n                  <div className=\"text-5xl mb-4\">{item.icon}</div>\n                  <h3 className=\"text-xl font-bold text-gray-900 mb-3\">{item.title}</h3>\n                  <p className=\"text-gray-600\">{item.description}</p>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"bg-gradient-to-r from-green-600 to-blue-500 text-white py-20\">\n        <div className=\"container mx-auto px-4 text-center\">\n          <h2 className=\"text-3xl md:text-4xl font-bold mb-6\">\n            Ready to Improve Your Search Rankings?\n          </h2>\n          <p className=\"text-xl mb-8 opacity-90 max-w-2xl mx-auto\">\n            Get a free SEO audit and discover how we can help your website rank higher in search results.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Link\n              href=\"/contact\"\n              className=\"bg-white text-green-600 font-bold px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 transform hover:scale-105\"\n            >\n              Get Free SEO Audit\n            </Link>\n            <Link\n              href=\"/case-studies\"\n              className=\"border-2 border-white text-white font-bold px-8 py-4 rounded-full hover:bg-white hover:text-green-600 transition-all duration-300\"\n            >\n              View Case Studies\n            </Link>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;IAE/B,qBACE,6LAAC;QAAI,WAAW,CAAC,wBAAwB,EAAE,QAAQ,QAAQ,IAAI;;0BAE7D,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAAW;;;;;;;;;;;0CAE7B,6LAAC;gCAAG,WAAU;0CACX,EAAE;;;;;;0CAEL,6LAAC;gCAAE,WAAU;0CAAsC;;;;;;0CAGnD,6LAAC,+JAAA,CAAA,UAAI;gCA<PERSON>,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;0BAQP,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAGlE,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAKvC,6LAAC;gCAAI,WAAU;0CACZ;oCACC;wCACE,MAAM;wCACN,OAAO;wCACP,aAAa;wCACb,UAAU;4CAAC;4CAA+B;4CAA0B;4CAAiC;yCAAkC;oCACzI;oCACA;wCACE,MAAM;wCACN,OAAO;wCACP,aAAa;wCACb,UAAU;4CAAC;4CAA2B;4CAA+B;4CAA0B;yCAA+B;oCAChI;oCACA;wCACE,MAAM;wCACN,OAAO;wCACP,aAAa;wCACb,UAAU;4CAAC;4CAA0B;4CAA4B;4CAAwB;yCAA4B;oCACvH;oCACA;wCACE,MAAM;wCACN,OAAO;wCACP,aAAa;wCACb,UAAU;4CAAC;4CAA+B;4CAA0B;4CAAwB;yCAA0B;oCACxH;oCACA;wCACE,MAAM;wCACN,OAAO;wCACP,aAAa;wCACb,UAAU;4CAAC;4CAAmC;4CAA6B;4CAAqB;yCAA0B;oCAC5H;oCACA;wCACE,MAAM;wCACN,OAAO;wCACP,aAAa;wCACb,UAAU;4CAAC;4CAA6B;4CAA4B;4CAAuB;yCAAsB;oCACnH;iCACD,CAAC,GAAG,CAAC,CAAC,SAAS,sBACd,6LAAC;wCAAgB,WAAU;;0DACzB,6LAAC;gDAAI,WAAU;0DAAiB,QAAQ,IAAI;;;;;;0DAC5C,6LAAC;gDAAG,WAAU;0DAAyC,QAAQ,KAAK;;;;;;0DACpE,6LAAC;gDAAE,WAAU;0DAAsB,QAAQ,WAAW;;;;;;0DACtD,6LAAC;gDAAG,WAAU;0DACX,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BAC9B,6LAAC;wDAAsB,WAAU;;0EAC/B,6LAAC;gEAAK,WAAU;0EAAsB;;;;;;4DACrC;;uDAFM;;;;;;;;;;;uCANL;;;;;;;;;;;;;;;;;;;;;;;;;;0BAoBpB,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAqD;;;;;;0CAInE,6LAAC;gCAAI,WAAU;0CACZ;oCACC;wCACE,MAAM;wCACN,OAAO;wCACP,aAAa;oCACf;oCACA;wCACE,MAAM;wCACN,OAAO;wCACP,aAAa;oCACf;oCACA;wCACE,MAAM;wCACN,OAAO;wCACP,aAAa;oCACf;iCACD,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,6LAAC;wCAAgB,WAAU;;0DACzB,6LAAC;gDAAI,WAAU;0DAAiB,KAAK,IAAI;;;;;;0DACzC,6LAAC;gDAAG,WAAU;0DAAwC,KAAK,KAAK;;;;;;0DAChE,6LAAC;gDAAE,WAAU;0DAAiB,KAAK,WAAW;;;;;;;uCAHtC;;;;;;;;;;;;;;;;;;;;;;;;;;0BAYpB,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAsC;;;;;;sCAGpD,6LAAC;4BAAE,WAAU;sCAA4C;;;;;;sCAGzD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GAnKwB;;QACD,sIAAA,CAAA,cAAW;;;KADV", "debugId": null}}]}