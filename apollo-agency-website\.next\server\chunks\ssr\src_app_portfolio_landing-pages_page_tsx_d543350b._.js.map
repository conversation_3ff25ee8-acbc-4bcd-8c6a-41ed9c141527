{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/agency/apollo-agency-website/src/app/portfolio/landing-pages/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useLanguage } from '@/contexts/LanguageContext';\nimport Link from 'next/link';\n\nexport default function LandingPagesPortfolioPage() {\n  const { t, isRTL } = useLanguage();\n\n  const projects = [\n    {\n      id: 1,\n      title: 'TechCorp Product Launch',\n      description: 'High-converting landing page for new software product launch',\n      metrics: ['45% conversion rate', '10,000+ leads generated', '300% ROI increase'],\n      features: ['Responsive Design', 'A/B Testing', 'Lead Capture Forms', 'Analytics Integration'],\n      image: '💻',\n      category: 'Product Launch'\n    },\n    {\n      id: 2,\n      title: 'E-commerce Sales Page',\n      description: 'Optimized sales page for online fashion retailer',\n      metrics: ['60% increase in sales', '35% higher conversion', '200% more traffic'],\n      features: ['Mobile Optimized', 'Fast Loading', 'Social Proof', 'Payment Integration'],\n      image: '🛍️',\n      category: 'E-commerce'\n    },\n    {\n      id: 3,\n      title: 'Healthcare Service Landing',\n      description: 'Professional landing page for medical consultation service',\n      metrics: ['500% increase in appointments', '40% conversion rate', '1000+ new patients'],\n      features: ['Trust Signals', 'Appointment Booking', 'Patient Testimonials', 'HIPAA Compliant'],\n      image: '🏥',\n      category: 'Healthcare'\n    },\n    {\n      id: 4,\n      title: 'Event Registration Page',\n      description: 'Registration landing page for business conference',\n      metrics: ['2000+ registrations', '50% conversion rate', 'Sold out event'],\n      features: ['Event Details', 'Speaker Profiles', 'Registration Form', 'Payment Processing'],\n      image: '🎯',\n      category: 'Event Marketing'\n    }\n  ];\n\n  return (\n    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : ''}`}>\n      {/* Hero Section */}\n      <section className=\"bg-gradient-to-br from-blue-900 via-indigo-800 to-purple-600 text-white py-20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-4xl mx-auto text-center\">\n            <div className=\"w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6\">\n              <span className=\"text-3xl\">🚀</span>\n            </div>\n            <h1 className=\"text-4xl md:text-6xl font-bold mb-6\">\n              {t('portfolio.categories.landingPages')} Portfolio\n            </h1>\n            <p className=\"text-xl md:text-2xl mb-8 opacity-90\">\n              High-converting landing pages that turn visitors into customers\n            </p>\n            <Link\n              href=\"/contact\"\n              className=\"inline-block bg-gradient-to-r from-blue-500 to-purple-500 text-white font-bold px-8 py-4 rounded-full hover:from-blue-400 hover:to-purple-400 transition-all duration-300 transform hover:scale-105\"\n            >\n              Create Your Landing Page\n            </Link>\n          </div>\n        </div>\n      </section>\n\n      {/* Projects Grid */}\n      <section className=\"py-20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-6xl mx-auto\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-6\">\n                High-Converting Landing Pages\n              </h2>\n              <p className=\"text-xl text-gray-600\">\n                Pages designed to convert visitors into customers\n              </p>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8\">\n              {projects.map((project) => (\n                <div key={project.id} className=\"bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300\">\n                  {/* Project Header */}\n                  <div className=\"bg-gradient-to-r from-blue-100 to-purple-100 p-8 text-center\">\n                    <div className=\"text-6xl mb-4\">{project.image}</div>\n                    <span className=\"bg-white/80 text-blue-600 px-3 py-1 rounded-full text-sm font-medium\">\n                      {project.category}\n                    </span>\n                  </div>\n\n                  {/* Project Content */}\n                  <div className=\"p-8\">\n                    <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">{project.title}</h3>\n                    <p className=\"text-gray-600 mb-4\">{project.description}</p>\n                    \n                    <div className=\"mb-6\">\n                      <h4 className=\"font-semibold text-gray-900 mb-3\">Key Metrics:</h4>\n                      <ul className=\"space-y-2\">\n                        {project.metrics.map((metric, index) => (\n                          <li key={index} className=\"flex items-center text-green-600\">\n                            <span className=\"mr-2\">📈</span>\n                            {metric}\n                          </li>\n                        ))}\n                      </ul>\n                    </div>\n\n                    <div className=\"mb-6\">\n                      <h4 className=\"font-semibold text-gray-900 mb-2\">Features:</h4>\n                      <div className=\"flex flex-wrap gap-2\">\n                        {project.features.map((feature, index) => (\n                          <span key={index} className=\"bg-blue-100 text-blue-600 px-3 py-1 rounded-full text-sm\">\n                            {feature}\n                          </span>\n                        ))}\n                      </div>\n                    </div>\n\n                    <Link\n                      href={`/case-studies/${project.id}`}\n                      className=\"inline-flex items-center bg-gradient-to-r from-blue-600 to-purple-500 text-white font-bold px-6 py-3 rounded-lg hover:from-blue-700 hover:to-purple-600 transition-all duration-300\"\n                    >\n                      View Full Case Study\n                      <svg className={`w-4 h-4 ${isRTL ? 'mr-2 rotate-180' : 'ml-2'}`} fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                      </svg>\n                    </Link>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Landing Page Features */}\n      <section className=\"py-20 bg-white\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-4xl mx-auto\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-6\">\n                What Makes Our Landing Pages Convert\n              </h2>\n              <p className=\"text-xl text-gray-600\">\n                Every element is designed with conversion in mind\n              </p>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n              {[\n                { icon: '🎯', title: 'Clear Value Proposition', description: 'Compelling headlines that communicate benefits' },\n                { icon: '📱', title: 'Mobile Responsive', description: 'Perfect experience on all devices' },\n                { icon: '⚡', title: 'Fast Loading', description: 'Optimized for speed and performance' },\n                { icon: '🔒', title: 'Trust Signals', description: 'Testimonials, reviews, and security badges' },\n                { icon: '📊', title: 'A/B Tested', description: 'Data-driven optimization for best results' },\n                { icon: '🎨', title: 'Conversion Focused', description: 'Every element designed to drive action' }\n              ].map((feature, index) => (\n                <div key={index} className=\"text-center p-6 rounded-lg hover:bg-gray-50 transition-colors duration-300\">\n                  <div className=\"text-4xl mb-4\">{feature.icon}</div>\n                  <h3 className=\"text-xl font-bold text-gray-900 mb-3\">{feature.title}</h3>\n                  <p className=\"text-gray-600\">{feature.description}</p>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Conversion Optimization Process */}\n      <section className=\"py-20 bg-gray-50\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-4xl mx-auto\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-6\">\n                Our Landing Page Development Process\n              </h2>\n              <p className=\"text-xl text-gray-600\">\n                Systematic approach to creating high-converting pages\n              </p>\n            </div>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n              {[\n                { step: '01', title: 'Research', description: 'Understanding your audience and goals' },\n                { step: '02', title: 'Design', description: 'Creating conversion-focused layouts' },\n                { step: '03', title: 'Develop', description: 'Building fast, responsive pages' },\n                { step: '04', title: 'Optimize', description: 'Testing and improving performance' }\n              ].map((item, index) => (\n                <div key={index} className=\"text-center\">\n                  <div className=\"w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-500 text-white rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4\">\n                    {item.step}\n                  </div>\n                  <h3 className=\"text-xl font-bold text-gray-900 mb-3\">{item.title}</h3>\n                  <p className=\"text-gray-600\">{item.description}</p>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"bg-gradient-to-r from-blue-600 to-purple-500 text-white py-20\">\n        <div className=\"container mx-auto px-4 text-center\">\n          <h2 className=\"text-3xl md:text-4xl font-bold mb-6\">\n            Ready to Boost Your Conversion Rates?\n          </h2>\n          <p className=\"text-xl mb-8 opacity-90 max-w-2xl mx-auto\">\n            Let's create a landing page that turns your traffic into customers and grows your business.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Link\n              href=\"/contact\"\n              className=\"bg-white text-blue-600 font-bold px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 transform hover:scale-105\"\n            >\n              Get High-Converting Landing Page\n            </Link>\n            <Link\n              href=\"/portfolio\"\n              className=\"border-2 border-white text-white font-bold px-8 py-4 rounded-full hover:bg-white hover:text-blue-600 transition-all duration-300\"\n            >\n              View All Projects\n            </Link>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,cAAW,AAAD;IAE/B,MAAM,WAAW;QACf;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,SAAS;gBAAC;gBAAuB;gBAA2B;aAAoB;YAChF,UAAU;gBAAC;gBAAqB;gBAAe;gBAAsB;aAAwB;YAC7F,OAAO;YACP,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,SAAS;gBAAC;gBAAyB;gBAAyB;aAAoB;YAChF,UAAU;gBAAC;gBAAoB;gBAAgB;gBAAgB;aAAsB;YACrF,OAAO;YACP,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,SAAS;gBAAC;gBAAiC;gBAAuB;aAAqB;YACvF,UAAU;gBAAC;gBAAiB;gBAAuB;gBAAwB;aAAkB;YAC7F,OAAO;YACP,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,SAAS;gBAAC;gBAAuB;gBAAuB;aAAiB;YACzE,UAAU;gBAAC;gBAAiB;gBAAoB;gBAAqB;aAAqB;YAC1F,OAAO;YACP,UAAU;QACZ;KACD;IAED,qBACE,8OAAC;QAAI,WAAW,CAAC,wBAAwB,EAAE,QAAQ,QAAQ,IAAI;;0BAE7D,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CAAW;;;;;;;;;;;0CAE7B,8OAAC;gCAAG,WAAU;;oCACX,EAAE;oCAAqC;;;;;;;0CAE1C,8OAAC;gCAAE,WAAU;0CAAsC;;;;;;0CAGnD,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;0BAQP,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAGlE,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAKvC,8OAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;wCAAqB,WAAU;;0DAE9B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAiB,QAAQ,KAAK;;;;;;kEAC7C,8OAAC;wDAAK,WAAU;kEACb,QAAQ,QAAQ;;;;;;;;;;;;0DAKrB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAyC,QAAQ,KAAK;;;;;;kEACpE,8OAAC;wDAAE,WAAU;kEAAsB,QAAQ,WAAW;;;;;;kEAEtD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAmC;;;;;;0EACjD,8OAAC;gEAAG,WAAU;0EACX,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAC5B,8OAAC;wEAAe,WAAU;;0FACxB,8OAAC;gFAAK,WAAU;0FAAO;;;;;;4EACtB;;uEAFM;;;;;;;;;;;;;;;;kEAQf,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAmC;;;;;;0EACjD,8OAAC;gEAAI,WAAU;0EACZ,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC9B,8OAAC;wEAAiB,WAAU;kFACzB;uEADQ;;;;;;;;;;;;;;;;kEAOjB,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAM,CAAC,cAAc,EAAE,QAAQ,EAAE,EAAE;wDACnC,WAAU;;4DACX;0EAEC,8OAAC;gEAAI,WAAW,CAAC,QAAQ,EAAE,QAAQ,oBAAoB,QAAQ;gEAAE,MAAK;gEAAO,QAAO;gEAAe,SAAQ;0EACzG,cAAA,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;uCA3CnE,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;0BAuD9B,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAGlE,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAKvC,8OAAC;gCAAI,WAAU;0CACZ;oCACC;wCAAE,MAAM;wCAAM,OAAO;wCAA2B,aAAa;oCAAiD;oCAC9G;wCAAE,MAAM;wCAAM,OAAO;wCAAqB,aAAa;oCAAoC;oCAC3F;wCAAE,MAAM;wCAAK,OAAO;wCAAgB,aAAa;oCAAsC;oCACvF;wCAAE,MAAM;wCAAM,OAAO;wCAAiB,aAAa;oCAA6C;oCAChG;wCAAE,MAAM;wCAAM,OAAO;wCAAc,aAAa;oCAA4C;oCAC5F;wCAAE,MAAM;wCAAM,OAAO;wCAAsB,aAAa;oCAAyC;iCAClG,CAAC,GAAG,CAAC,CAAC,SAAS,sBACd,8OAAC;wCAAgB,WAAU;;0DACzB,8OAAC;gDAAI,WAAU;0DAAiB,QAAQ,IAAI;;;;;;0DAC5C,8OAAC;gDAAG,WAAU;0DAAwC,QAAQ,KAAK;;;;;;0DACnE,8OAAC;gDAAE,WAAU;0DAAiB,QAAQ,WAAW;;;;;;;uCAHzC;;;;;;;;;;;;;;;;;;;;;;;;;;0BAYpB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAGlE,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAKvC,8OAAC;gCAAI,WAAU;0CACZ;oCACC;wCAAE,MAAM;wCAAM,OAAO;wCAAY,aAAa;oCAAwC;oCACtF;wCAAE,MAAM;wCAAM,OAAO;wCAAU,aAAa;oCAAsC;oCAClF;wCAAE,MAAM;wCAAM,OAAO;wCAAW,aAAa;oCAAkC;oCAC/E;wCAAE,MAAM;wCAAM,OAAO;wCAAY,aAAa;oCAAoC;iCACnF,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,8OAAC;wCAAgB,WAAU;;0DACzB,8OAAC;gDAAI,WAAU;0DACZ,KAAK,IAAI;;;;;;0DAEZ,8OAAC;gDAAG,WAAU;0DAAwC,KAAK,KAAK;;;;;;0DAChE,8OAAC;gDAAE,WAAU;0DAAiB,KAAK,WAAW;;;;;;;uCALtC;;;;;;;;;;;;;;;;;;;;;;;;;;0BAcpB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAsC;;;;;;sCAGpD,8OAAC;4BAAE,WAAU;sCAA4C;;;;;;sCAGzD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}]}