{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/agency/apollo-agency-website/src/app/services/branding/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useLanguage } from '@/contexts/LanguageContext';\nimport Link from 'next/link';\n\nexport default function BrandingPage() {\n  const { t, isRTL } = useLanguage();\n\n  return (\n    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : ''}`}>\n      {/* Hero Section */}\n      <section className=\"bg-gradient-to-br from-purple-900 via-pink-800 to-red-600 text-white py-20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-4xl mx-auto text-center\">\n            <div className=\"w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6\">\n              <span className=\"text-3xl\">🎯</span>\n            </div>\n            <h1 className=\"text-4xl md:text-6xl font-bold mb-6\">\n              {t('services.branding')}\n            </h1>\n            <p className=\"text-xl md:text-2xl mb-8 opacity-90\">\n              Build a powerful brand identity that resonates with your audience\n            </p>\n            <Link\n              href=\"/contact\"\n              className=\"inline-block bg-gradient-to-r from-yellow-400 to-orange-500 text-gray-900 font-bold px-8 py-4 rounded-full hover:from-yellow-300 hover:to-orange-400 transition-all duration-300 transform hover:scale-105\"\n            >\n              Start Your Brand Journey\n            </Link>\n          </div>\n        </div>\n      </section>\n\n      {/* Branding Services */}\n      <section className=\"py-20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-6xl mx-auto\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-6\">\n                Complete Brand Development Services\n              </h2>\n              <p className=\"text-xl text-gray-600\">\n                From strategy to visual identity, we create brands that make an impact\n              </p>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n              {[\n                {\n                  icon: '🧠',\n                  title: 'Brand Strategy',\n                  description: 'Define your brand positioning, values, and unique value proposition.',\n                  features: ['Brand positioning', 'Target audience analysis', 'Competitive research', 'Brand messaging']\n                },\n                {\n                  icon: '🎨',\n                  title: 'Visual Identity',\n                  description: 'Create a cohesive visual system that represents your brand.',\n                  features: ['Logo design', 'Color palette', 'Typography selection', 'Visual guidelines']\n                },\n                {\n                  icon: '📖',\n                  title: 'Brand Guidelines',\n                  description: 'Comprehensive guidelines to maintain brand consistency.',\n                  features: ['Usage guidelines', 'Do\\'s and don\\'ts', 'Application examples', 'Brand standards']\n                },\n                {\n                  icon: '💬',\n                  title: 'Brand Messaging',\n                  description: 'Develop compelling messaging that connects with your audience.',\n                  features: ['Brand voice', 'Tone of voice', 'Key messages', 'Tagline development']\n                },\n                {\n                  icon: '📦',\n                  title: 'Brand Applications',\n                  description: 'Apply your brand across all touchpoints and materials.',\n                  features: ['Business cards', 'Letterheads', 'Marketing materials', 'Digital applications']\n                },\n                {\n                  icon: '🔄',\n                  title: 'Brand Refresh',\n                  description: 'Modernize and revitalize your existing brand identity.',\n                  features: ['Brand audit', 'Modernization', 'Evolution strategy', 'Implementation plan']\n                }\n              ].map((service, index) => (\n                <div key={index} className=\"bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300\">\n                  <div className=\"text-4xl mb-4\">{service.icon}</div>\n                  <h3 className=\"text-xl font-bold text-gray-900 mb-3\">{service.title}</h3>\n                  <p className=\"text-gray-600 mb-4\">{service.description}</p>\n                  <ul className=\"space-y-2\">\n                    {service.features.map((feature, featureIndex) => (\n                      <li key={featureIndex} className=\"flex items-center text-gray-600 text-sm\">\n                        <span className=\"text-purple-600 mr-2\">✓</span>\n                        {feature}\n                      </li>\n                    ))}\n                  </ul>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Process */}\n      <section className=\"py-20 bg-white\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-4xl mx-auto\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-6\">\n                Our Branding Process\n              </h2>\n              <p className=\"text-xl text-gray-600\">\n                A systematic approach to building powerful brands\n              </p>\n            </div>\n            \n            <div className=\"space-y-8\">\n              {[\n                { \n                  step: '01', \n                  title: 'Discovery & Research', \n                  description: 'We dive deep into your business, industry, and target audience to understand your unique challenges and opportunities.' \n                },\n                { \n                  step: '02', \n                  title: 'Strategy Development', \n                  description: 'Based on our research, we develop a comprehensive brand strategy that defines your positioning and messaging.' \n                },\n                { \n                  step: '03', \n                  title: 'Visual Identity Creation', \n                  description: 'Our designers create a visual identity that brings your brand strategy to life through logos, colors, and typography.' \n                },\n                { \n                  step: '04', \n                  title: 'Implementation & Launch', \n                  description: 'We help you implement your new brand across all touchpoints and launch it effectively to your audience.' \n                }\n              ].map((item, index) => (\n                <div key={index} className=\"flex items-start space-x-6\">\n                  <div className=\"w-16 h-16 bg-gradient-to-r from-purple-600 to-pink-500 text-white rounded-full flex items-center justify-center text-xl font-bold flex-shrink-0\">\n                    {item.step}\n                  </div>\n                  <div>\n                    <h3 className=\"text-2xl font-bold text-gray-900 mb-3\">{item.title}</h3>\n                    <p className=\"text-gray-600 text-lg\">{item.description}</p>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"bg-gradient-to-r from-purple-600 to-pink-500 text-white py-20\">\n        <div className=\"container mx-auto px-4 text-center\">\n          <h2 className=\"text-3xl md:text-4xl font-bold mb-6\">\n            Ready to Build a Powerful Brand?\n          </h2>\n          <p className=\"text-xl mb-8 opacity-90 max-w-2xl mx-auto\">\n            Let's create a brand identity that sets you apart from the competition and connects with your audience.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Link\n              href=\"/contact\"\n              className=\"bg-white text-purple-600 font-bold px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 transform hover:scale-105\"\n            >\n              Start Your Brand Project\n            </Link>\n            <Link\n              href=\"/portfolio/branding\"\n              className=\"border-2 border-white text-white font-bold px-8 py-4 rounded-full hover:bg-white hover:text-purple-600 transition-all duration-300\"\n            >\n              View Brand Portfolio\n            </Link>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;IAE/B,qBACE,6LAAC;QAAI,WAAW,CAAC,wBAAwB,EAAE,QAAQ,QAAQ,IAAI;;0BAE7D,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAAW;;;;;;;;;;;0CAE7B,6LAAC;gCAAG,WAAU;0CACX,EAAE;;;;;;0CAEL,6LAAC;gCAAE,WAAU;0CAAsC;;;;;;0CAGnD,6LAAC,+JAAA,CAAA,UAAI;gCA<PERSON>,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;0BAQP,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAGlE,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAKvC,6LAAC;gCAAI,WAAU;0CACZ;oCACC;wCACE,MAAM;wCACN,OAAO;wCACP,aAAa;wCACb,UAAU;4CAAC;4CAAqB;4CAA4B;4CAAwB;yCAAkB;oCACxG;oCACA;wCACE,MAAM;wCACN,OAAO;wCACP,aAAa;wCACb,UAAU;4CAAC;4CAAe;4CAAiB;4CAAwB;yCAAoB;oCACzF;oCACA;wCACE,MAAM;wCACN,OAAO;wCACP,aAAa;wCACb,UAAU;4CAAC;4CAAoB;4CAAqB;4CAAwB;yCAAkB;oCAChG;oCACA;wCACE,MAAM;wCACN,OAAO;wCACP,aAAa;wCACb,UAAU;4CAAC;4CAAe;4CAAiB;4CAAgB;yCAAsB;oCACnF;oCACA;wCACE,MAAM;wCACN,OAAO;wCACP,aAAa;wCACb,UAAU;4CAAC;4CAAkB;4CAAe;4CAAuB;yCAAuB;oCAC5F;oCACA;wCACE,MAAM;wCACN,OAAO;wCACP,aAAa;wCACb,UAAU;4CAAC;4CAAe;4CAAiB;4CAAsB;yCAAsB;oCACzF;iCACD,CAAC,GAAG,CAAC,CAAC,SAAS,sBACd,6LAAC;wCAAgB,WAAU;;0DACzB,6LAAC;gDAAI,WAAU;0DAAiB,QAAQ,IAAI;;;;;;0DAC5C,6LAAC;gDAAG,WAAU;0DAAwC,QAAQ,KAAK;;;;;;0DACnE,6LAAC;gDAAE,WAAU;0DAAsB,QAAQ,WAAW;;;;;;0DACtD,6LAAC;gDAAG,WAAU;0DACX,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BAC9B,6LAAC;wDAAsB,WAAU;;0EAC/B,6LAAC;gEAAK,WAAU;0EAAuB;;;;;;4DACtC;;uDAFM;;;;;;;;;;;uCANL;;;;;;;;;;;;;;;;;;;;;;;;;;0BAoBpB,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAGlE,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAKvC,6LAAC;gCAAI,WAAU;0CACZ;oCACC;wCACE,MAAM;wCACN,OAAO;wCACP,aAAa;oCACf;oCACA;wCACE,MAAM;wCACN,OAAO;wCACP,aAAa;oCACf;oCACA;wCACE,MAAM;wCACN,OAAO;wCACP,aAAa;oCACf;oCACA;wCACE,MAAM;wCACN,OAAO;wCACP,aAAa;oCACf;iCACD,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,6LAAC;wCAAgB,WAAU;;0DACzB,6LAAC;gDAAI,WAAU;0DACZ,KAAK,IAAI;;;;;;0DAEZ,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAyC,KAAK,KAAK;;;;;;kEACjE,6LAAC;wDAAE,WAAU;kEAAyB,KAAK,WAAW;;;;;;;;;;;;;uCANhD;;;;;;;;;;;;;;;;;;;;;;;;;;0BAgBpB,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAsC;;;;;;sCAGpD,6LAAC;4BAAE,WAAU;sCAA4C;;;;;;sCAGzD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GAjLwB;;QACD,sIAAA,CAAA,cAAW;;;KADV", "debugId": null}}]}