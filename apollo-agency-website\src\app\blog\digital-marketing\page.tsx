'use client';

import { useLanguage } from '@/contexts/LanguageContext';
import Link from 'next/link';

export default function DigitalMarketingBlogPage() {
  const { t, isRTL } = useLanguage();

  const posts = [
    {
      id: 1,
      title: 'Digital Marketing Trends That Will Dominate 2024',
      excerpt: 'Discover the emerging digital marketing trends and technologies that will shape the industry this year.',
      author: '<PERSON>',
      date: '2024-01-22',
      readTime: '12 min read',
      category: 'Trends',
      image: '📈'
    },
    {
      id: 2,
      title: 'Building a Complete Digital Marketing Funnel',
      excerpt: 'Step-by-step guide to creating a digital marketing funnel that converts prospects into customers.',
      author: '<PERSON>',
      date: '2024-01-16',
      readTime: '10 min read',
      category: 'Strategy',
      image: '🎯'
    },
    {
      id: 3,
      title: 'Email Marketing Automation: Best Practices for 2024',
      excerpt: 'Master email marketing automation to nurture leads and drive conversions at scale.',
      author: '<PERSON>',
      date: '2024-01-11',
      readTime: '8 min read',
      category: 'Email Marketing',
      image: '📧'
    },
    {
      id: 4,
      title: 'Content Marketing ROI: How to Measure What Matters',
      excerpt: 'Learn how to track and measure the ROI of your content marketing efforts effectively.',
      author: '<PERSON>',
      date: '2024-01-06',
      readTime: '9 min read',
      category: 'Content Marketing',
      image: '📊'
    },
    {
      id: 5,
      title: 'Omnichannel Marketing: Creating Seamless Customer Experiences',
      excerpt: 'Strategies for creating consistent and engaging customer experiences across all digital touchpoints.',
      author: 'Amanda Foster',
      date: '2024-01-01',
      readTime: '11 min read',
      category: 'Omnichannel',
      image: '🌐'
    },
    {
      id: 6,
      title: 'Marketing Attribution: Understanding Your Customer Journey',
      excerpt: 'Complete guide to marketing attribution models and how to track the customer journey effectively.',
      author: 'Ryan Mitchell',
      date: '2023-12-26',
      readTime: '13 min read',
      category: 'Analytics',
      image: '🔍'
    }
  ];

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : ''}`}>
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-indigo-900 via-purple-800 to-blue-600 text-white py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6">
              <span className="text-3xl">💻</span>
            </div>
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Digital Marketing Blog
            </h1>
            <p className="text-xl md:text-2xl mb-8 opacity-90">
              Comprehensive insights and strategies for digital marketing success
            </p>
            <Link
              href="/contact"
              className="inline-block bg-gradient-to-r from-indigo-500 to-purple-500 text-white font-bold px-8 py-4 rounded-full hover:from-indigo-400 hover:to-purple-400 transition-all duration-300 transform hover:scale-105"
            >
              Get Digital Marketing Help
            </Link>
          </div>
        </div>
      </section>

      {/* Blog Posts Grid */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Latest Digital Marketing Articles
              </h2>
              <p className="text-xl text-gray-600">
                Expert insights on digital marketing strategies and trends
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {posts.map((post) => (
                <article key={post.id} className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300">
                  <div className="bg-gradient-to-r from-indigo-100 to-purple-100 p-8 text-center">
                    <div className="text-6xl mb-4">{post.image}</div>
                    <span className="bg-white/80 text-indigo-600 px-3 py-1 rounded-full text-sm font-medium">
                      {post.category}
                    </span>
                  </div>

                  <div className="p-6">
                    <h3 className="text-xl font-bold text-gray-900 mb-3 line-clamp-2">
                      {post.title}
                    </h3>
                    <p className="text-gray-600 mb-4 line-clamp-3">
                      {post.excerpt}
                    </p>
                    
                    <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                      <span>{post.author}</span>
                      <span>{post.readTime}</span>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500">{post.date}</span>
                      <Link
                        href={`/blog/digital-marketing/${post.id}`}
                        className="inline-flex items-center text-indigo-600 hover:text-indigo-700 font-medium"
                      >
                        Read More
                        <svg className={`w-4 h-4 ${isRTL ? 'mr-1 rotate-180' : 'ml-1'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                      </Link>
                    </div>
                  </div>
                </article>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-gradient-to-r from-indigo-600 to-purple-500 text-white py-20">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Ready to Transform Your Digital Marketing?
          </h2>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Let our digital marketing experts help you create strategies that drive real business results.
          </p>
          <Link
            href="/contact"
            className="bg-white text-indigo-600 font-bold px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 transform hover:scale-105"
          >
            Start Digital Marketing
          </Link>
        </div>
      </section>
    </div>
  );
}
