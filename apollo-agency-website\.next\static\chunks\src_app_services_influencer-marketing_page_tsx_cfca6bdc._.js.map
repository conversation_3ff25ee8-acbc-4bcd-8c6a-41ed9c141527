{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/agency/apollo-agency-website/src/app/services/influencer-marketing/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useLanguage } from '@/contexts/LanguageContext';\nimport Link from 'next/link';\n\nexport default function InfluencerMarketingPage() {\n  const { t, isRTL } = useLanguage();\n\n  return (\n    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : ''}`}>\n      {/* Hero Section */}\n      <section className=\"bg-gradient-to-br from-pink-900 via-purple-800 to-indigo-600 text-white py-20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-4xl mx-auto text-center\">\n            <div className=\"w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6\">\n              <span className=\"text-3xl\">🤝</span>\n            </div>\n            <h1 className=\"text-4xl md:text-6xl font-bold mb-6\">\n              {t('services.influencerMarketing')}\n            </h1>\n            <p className=\"text-xl md:text-2xl mb-8 opacity-90\">\n              Connect with your audience through authentic influencer partnerships\n            </p>\n            <Link\n              href=\"/contact\"\n              className=\"inline-block bg-gradient-to-r from-pink-500 to-purple-500 text-white font-bold px-8 py-4 rounded-full hover:from-pink-400 hover:to-purple-400 transition-all duration-300 transform hover:scale-105\"\n            >\n              Start Influencer Campaign\n            </Link>\n          </div>\n        </div>\n      </section>\n\n      {/* Services Overview */}\n      <section className=\"py-20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-6xl mx-auto\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-6\">\n                Complete Influencer Marketing Solutions\n              </h2>\n              <p className=\"text-xl text-gray-600\">\n                From strategy to execution, we manage every aspect of your influencer campaigns\n              </p>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n              {[\n                {\n                  icon: '🔍',\n                  title: 'Influencer Discovery',\n                  description: 'Find the perfect influencers for your brand and audience.',\n                  features: ['Audience analysis', 'Engagement rate review', 'Brand alignment check', 'Performance history']\n                },\n                {\n                  icon: '🤝',\n                  title: 'Campaign Management',\n                  description: 'End-to-end campaign management from planning to execution.',\n                  features: ['Campaign strategy', 'Content guidelines', 'Timeline management', 'Performance tracking']\n                },\n                {\n                  icon: '📊',\n                  title: 'Performance Analytics',\n                  description: 'Detailed analytics and reporting on campaign performance.',\n                  features: ['Reach & impressions', 'Engagement metrics', 'ROI analysis', 'Audience insights']\n                },\n                {\n                  icon: '💰',\n                  title: 'Budget Optimization',\n                  description: 'Maximize your ROI with strategic budget allocation.',\n                  features: ['Cost negotiation', 'Budget planning', 'Performance optimization', 'ROI maximization']\n                },\n                {\n                  icon: '📱',\n                  title: 'Multi-Platform Campaigns',\n                  description: 'Campaigns across Instagram, TikTok, YouTube, and more.',\n                  features: ['Platform strategy', 'Content adaptation', 'Cross-platform sync', 'Audience targeting']\n                },\n                {\n                  icon: '🎯',\n                  title: 'Brand Safety',\n                  description: 'Ensure your brand is represented safely and authentically.',\n                  features: ['Content approval', 'Brand guidelines', 'Risk assessment', 'Compliance monitoring']\n                }\n              ].map((service, index) => (\n                <div key={index} className=\"bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300\">\n                  <div className=\"text-4xl mb-4\">{service.icon}</div>\n                  <h3 className=\"text-xl font-bold text-gray-900 mb-3\">{service.title}</h3>\n                  <p className=\"text-gray-600 mb-4\">{service.description}</p>\n                  <ul className=\"space-y-2\">\n                    {service.features.map((feature, featureIndex) => (\n                      <li key={featureIndex} className=\"flex items-center text-gray-600 text-sm\">\n                        <span className=\"text-pink-600 mr-2\">✓</span>\n                        {feature}\n                      </li>\n                    ))}\n                  </ul>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Influencer Types */}\n      <section className=\"py-20 bg-white\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-4xl mx-auto\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-6\">\n                Types of Influencer Partnerships\n              </h2>\n              <p className=\"text-xl text-gray-600\">\n                We work with influencers of all sizes to match your goals and budget\n              </p>\n            </div>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n              {[\n                { \n                  type: 'Nano Influencers', \n                  followers: '1K - 10K',\n                  description: 'High engagement rates and authentic connections',\n                  icon: '👥'\n                },\n                { \n                  type: 'Micro Influencers', \n                  followers: '10K - 100K',\n                  description: 'Niche expertise with engaged communities',\n                  icon: '📱'\n                },\n                { \n                  type: 'Macro Influencers', \n                  followers: '100K - 1M',\n                  description: 'Broad reach with professional content',\n                  icon: '🌟'\n                },\n                { \n                  type: 'Mega Influencers', \n                  followers: '1M+',\n                  description: 'Maximum reach and brand awareness',\n                  icon: '🚀'\n                }\n              ].map((influencer, index) => (\n                <div key={index} className=\"text-center p-6 bg-gray-50 rounded-lg\">\n                  <div className=\"text-4xl mb-4\">{influencer.icon}</div>\n                  <h3 className=\"text-lg font-bold text-gray-900 mb-2\">{influencer.type}</h3>\n                  <p className=\"text-pink-600 font-semibold mb-3\">{influencer.followers}</p>\n                  <p className=\"text-gray-600 text-sm\">{influencer.description}</p>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Campaign Process */}\n      <section className=\"py-20 bg-gray-50\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-4xl mx-auto\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-6\">\n                Our Influencer Marketing Process\n              </h2>\n              <p className=\"text-xl text-gray-600\">\n                Strategic approach to influencer marketing that delivers results\n              </p>\n            </div>\n            \n            <div className=\"space-y-8\">\n              {[\n                { \n                  step: '01', \n                  title: 'Strategy Development', \n                  description: 'Define campaign goals, target audience, and key performance indicators.' \n                },\n                { \n                  step: '02', \n                  title: 'Influencer Research', \n                  description: 'Identify and vet influencers that align with your brand values and audience.' \n                },\n                { \n                  step: '03', \n                  title: 'Campaign Execution', \n                  description: 'Manage partnerships, content creation, and campaign timeline.' \n                },\n                { \n                  step: '04', \n                  title: 'Performance Analysis', \n                  description: 'Track results, analyze performance, and optimize for future campaigns.' \n                }\n              ].map((item, index) => (\n                <div key={index} className=\"flex items-start space-x-6\">\n                  <div className=\"w-16 h-16 bg-gradient-to-r from-pink-600 to-purple-500 text-white rounded-full flex items-center justify-center text-xl font-bold flex-shrink-0\">\n                    {item.step}\n                  </div>\n                  <div>\n                    <h3 className=\"text-2xl font-bold text-gray-900 mb-3\">{item.title}</h3>\n                    <p className=\"text-gray-600 text-lg\">{item.description}</p>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Success Metrics */}\n      <section className=\"py-20 bg-white\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-4xl mx-auto text-center\">\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-6\">\n              Campaign Success Metrics\n            </h2>\n            <p className=\"text-xl text-gray-600 mb-12\">\n              We track what matters most to your business\n            </p>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n              <div className=\"text-center\">\n                <div className=\"text-4xl md:text-5xl font-bold text-pink-600 mb-2\">500%</div>\n                <div className=\"text-gray-600\">Average ROI Increase</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-4xl md:text-5xl font-bold text-pink-600 mb-2\">10M+</div>\n                <div className=\"text-gray-600\">Total Reach Generated</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-4xl md:text-5xl font-bold text-pink-600 mb-2\">85%</div>\n                <div className=\"text-gray-600\">Campaign Success Rate</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-4xl md:text-5xl font-bold text-pink-600 mb-2\">200+</div>\n                <div className=\"text-gray-600\">Influencer Partners</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"bg-gradient-to-r from-pink-600 to-purple-500 text-white py-20\">\n        <div className=\"container mx-auto px-4 text-center\">\n          <h2 className=\"text-3xl md:text-4xl font-bold mb-6\">\n            Ready to Launch Your Influencer Campaign?\n          </h2>\n          <p className=\"text-xl mb-8 opacity-90 max-w-2xl mx-auto\">\n            Connect with your audience through authentic influencer partnerships that drive real results.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Link\n              href=\"/contact\"\n              className=\"bg-white text-pink-600 font-bold px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 transform hover:scale-105\"\n            >\n              Start Influencer Campaign\n            </Link>\n            <Link\n              href=\"/portfolio\"\n              className=\"border-2 border-white text-white font-bold px-8 py-4 rounded-full hover:bg-white hover:text-pink-600 transition-all duration-300\"\n            >\n              View Campaign Examples\n            </Link>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;IAE/B,qBACE,6LAAC;QAAI,WAAW,CAAC,wBAAwB,EAAE,QAAQ,QAAQ,IAAI;;0BAE7D,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAAW;;;;;;;;;;;0CAE7B,6LAAC;gCAAG,WAAU;0CACX,EAAE;;;;;;0CAEL,6LAAC;gCAAE,WAAU;0CAAsC;;;;;;0CAGnD,6LAAC,+JAAA,CAAA,UAAI;gCA<PERSON>,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;0BAQP,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAGlE,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAKvC,6LAAC;gCAAI,WAAU;0CACZ;oCACC;wCACE,MAAM;wCACN,OAAO;wCACP,aAAa;wCACb,UAAU;4CAAC;4CAAqB;4CAA0B;4CAAyB;yCAAsB;oCAC3G;oCACA;wCACE,MAAM;wCACN,OAAO;wCACP,aAAa;wCACb,UAAU;4CAAC;4CAAqB;4CAAsB;4CAAuB;yCAAuB;oCACtG;oCACA;wCACE,MAAM;wCACN,OAAO;wCACP,aAAa;wCACb,UAAU;4CAAC;4CAAuB;4CAAsB;4CAAgB;yCAAoB;oCAC9F;oCACA;wCACE,MAAM;wCACN,OAAO;wCACP,aAAa;wCACb,UAAU;4CAAC;4CAAoB;4CAAmB;4CAA4B;yCAAmB;oCACnG;oCACA;wCACE,MAAM;wCACN,OAAO;wCACP,aAAa;wCACb,UAAU;4CAAC;4CAAqB;4CAAsB;4CAAuB;yCAAqB;oCACpG;oCACA;wCACE,MAAM;wCACN,OAAO;wCACP,aAAa;wCACb,UAAU;4CAAC;4CAAoB;4CAAoB;4CAAmB;yCAAwB;oCAChG;iCACD,CAAC,GAAG,CAAC,CAAC,SAAS,sBACd,6LAAC;wCAAgB,WAAU;;0DACzB,6LAAC;gDAAI,WAAU;0DAAiB,QAAQ,IAAI;;;;;;0DAC5C,6LAAC;gDAAG,WAAU;0DAAwC,QAAQ,KAAK;;;;;;0DACnE,6LAAC;gDAAE,WAAU;0DAAsB,QAAQ,WAAW;;;;;;0DACtD,6LAAC;gDAAG,WAAU;0DACX,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BAC9B,6LAAC;wDAAsB,WAAU;;0EAC/B,6LAAC;gEAAK,WAAU;0EAAqB;;;;;;4DACpC;;uDAFM;;;;;;;;;;;uCANL;;;;;;;;;;;;;;;;;;;;;;;;;;0BAoBpB,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAGlE,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAKvC,6LAAC;gCAAI,WAAU;0CACZ;oCACC;wCACE,MAAM;wCACN,WAAW;wCACX,aAAa;wCACb,MAAM;oCACR;oCACA;wCACE,MAAM;wCACN,WAAW;wCACX,aAAa;wCACb,MAAM;oCACR;oCACA;wCACE,MAAM;wCACN,WAAW;wCACX,aAAa;wCACb,MAAM;oCACR;oCACA;wCACE,MAAM;wCACN,WAAW;wCACX,aAAa;wCACb,MAAM;oCACR;iCACD,CAAC,GAAG,CAAC,CAAC,YAAY,sBACjB,6LAAC;wCAAgB,WAAU;;0DACzB,6LAAC;gDAAI,WAAU;0DAAiB,WAAW,IAAI;;;;;;0DAC/C,6LAAC;gDAAG,WAAU;0DAAwC,WAAW,IAAI;;;;;;0DACrE,6LAAC;gDAAE,WAAU;0DAAoC,WAAW,SAAS;;;;;;0DACrE,6LAAC;gDAAE,WAAU;0DAAyB,WAAW,WAAW;;;;;;;uCAJpD;;;;;;;;;;;;;;;;;;;;;;;;;;0BAapB,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAGlE,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAKvC,6LAAC;gCAAI,WAAU;0CACZ;oCACC;wCACE,MAAM;wCACN,OAAO;wCACP,aAAa;oCACf;oCACA;wCACE,MAAM;wCACN,OAAO;wCACP,aAAa;oCACf;oCACA;wCACE,MAAM;wCACN,OAAO;wCACP,aAAa;oCACf;oCACA;wCACE,MAAM;wCACN,OAAO;wCACP,aAAa;oCACf;iCACD,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,6LAAC;wCAAgB,WAAU;;0DACzB,6LAAC;gDAAI,WAAU;0DACZ,KAAK,IAAI;;;;;;0DAEZ,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAyC,KAAK,KAAK;;;;;;kEACjE,6LAAC;wDAAE,WAAU;kEAAyB,KAAK,WAAW;;;;;;;;;;;;;uCANhD;;;;;;;;;;;;;;;;;;;;;;;;;;0BAgBpB,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,6LAAC;gCAAE,WAAU;0CAA8B;;;;;;0CAI3C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAoD;;;;;;0DACnE,6LAAC;gDAAI,WAAU;0DAAgB;;;;;;;;;;;;kDAEjC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAoD;;;;;;0DACnE,6LAAC;gDAAI,WAAU;0DAAgB;;;;;;;;;;;;kDAEjC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAoD;;;;;;0DACnE,6LAAC;gDAAI,WAAU;0DAAgB;;;;;;;;;;;;kDAEjC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAoD;;;;;;0DACnE,6LAAC;gDAAI,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQzC,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAsC;;;;;;sCAGpD,6LAAC;4BAAE,WAAU;sCAA4C;;;;;;sCAGzD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GAtQwB;;QACD,sIAAA,CAAA,cAAW;;;KADV", "debugId": null}}]}