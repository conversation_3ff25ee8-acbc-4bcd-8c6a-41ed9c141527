'use client';

import { useLanguage } from '@/contexts/LanguageContext';
import Link from 'next/link';

export default function AboutPage() {
  const { t, isRTL } = useLanguage();

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : ''}`}>
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-indigo-900 via-purple-800 to-pink-600 text-white py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              {t('header.about')}
            </h1>
            <p className="text-xl md:text-2xl mb-8 opacity-90">
              We are Serv Infinity - Your trusted partner in digital marketing success
            </p>
          </div>
        </div>
      </section>

      {/* About Content */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Who We Are
              </h2>
              <p className="text-xl text-gray-600 leading-relaxed">
                Serv Infinity is a leading digital marketing agency dedicated to helping businesses 
                achieve their online goals through innovative strategies and cutting-edge solutions. 
                With years of experience and a team of passionate experts, we transform your digital 
                presence and drive measurable results.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-12 mb-20">
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">Our Mission</h3>
                <p className="text-gray-600 leading-relaxed">
                  To empower businesses of all sizes with comprehensive digital marketing solutions 
                  that drive growth, increase visibility, and create lasting connections with their 
                  target audience.
                </p>
              </div>
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">Our Vision</h3>
                <p className="text-gray-600 leading-relaxed">
                  To be the most trusted digital marketing partner, known for delivering exceptional 
                  results and helping businesses thrive in the digital landscape.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Our Values
              </h2>
              <p className="text-xl text-gray-600">
                The principles that guide everything we do
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {[
                {
                  icon: '🎯',
                  title: 'Results-Driven',
                  description: 'We focus on delivering measurable results that impact your bottom line and drive business growth.'
                },
                {
                  icon: '🤝',
                  title: 'Client-Centric',
                  description: 'Your success is our success. We work as an extension of your team to achieve your goals.'
                },
                {
                  icon: '💡',
                  title: 'Innovation',
                  description: 'We stay ahead of digital trends and use cutting-edge strategies to keep you competitive.'
                },
                {
                  icon: '🔍',
                  title: 'Transparency',
                  description: 'We believe in open communication and provide clear reporting on all our activities.'
                },
                {
                  icon: '⚡',
                  title: 'Agility',
                  description: 'We adapt quickly to changes in the digital landscape and pivot strategies when needed.'
                },
                {
                  icon: '🏆',
                  title: 'Excellence',
                  description: 'We strive for excellence in everything we do and never settle for mediocre results.'
                }
              ].map((value, index) => (
                <div key={index} className="text-center p-6">
                  <div className="text-5xl mb-4">{value.icon}</div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{value.title}</h3>
                  <p className="text-gray-600">{value.description}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Our Expert Team
            </h2>
            <p className="text-xl text-gray-600 mb-12">
              Meet the passionate professionals behind your success
            </p>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {[
                {
                  name: 'Sarah Johnson',
                  role: 'Digital Marketing Director',
                  expertise: 'Strategy & Campaign Management'
                },
                {
                  name: 'Ahmed Al-Rashid',
                  role: 'SEO Specialist',
                  expertise: 'Search Engine Optimization'
                },
                {
                  name: 'Maria Rodriguez',
                  role: 'Creative Director',
                  expertise: 'Brand Design & Visual Identity'
                }
              ].map((member, index) => (
                <div key={index} className="bg-white rounded-xl p-6 shadow-lg">
                  <div className="w-24 h-24 bg-gradient-to-r from-purple-100 to-pink-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-3xl">👤</span>
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-2">{member.name}</h3>
                  <p className="text-purple-600 font-medium mb-2">{member.role}</p>
                  <p className="text-gray-600 text-sm">{member.expertise}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-gradient-to-r from-indigo-600 to-purple-500 text-white py-20">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Ready to Work With Us?
          </h2>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Let&apos;s discuss how we can help your business achieve its digital marketing goals.
          </p>
          <Link
            href="/contact"
            className="bg-white text-indigo-600 font-bold px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 transform hover:scale-105"
          >
            Get In Touch
          </Link>
        </div>
      </section>
    </div>
  );
}
