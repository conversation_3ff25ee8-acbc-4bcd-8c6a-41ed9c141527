{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/agency/apollo-agency-website/src/components/HeroSection.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { useState, useEffect } from 'react';\nimport { useLanguage } from '@/contexts/LanguageContext';\n\nconst HeroSection = () => {\n  const { t, isRTL } = useLanguage();\n  const [currentSlide, setCurrentSlide] = useState(0);\n\n  const slides = [\n    {\n      title: t('hero.title1'),\n      subtitle: t('hero.subtitle1'),\n      cta: t('hero.cta1'),\n      background: \"bg-gradient-to-br from-purple-900 via-purple-800 to-pink-600\"\n    },\n    {\n      title: t('hero.title2'),\n      subtitle: t('hero.subtitle2'),\n      cta: t('hero.cta2'),\n      background: \"bg-gradient-to-br from-blue-900 via-blue-800 to-purple-600\"\n    },\n    {\n      title: t('hero.title3'),\n      subtitle: t('hero.subtitle3'),\n      cta: t('hero.cta3'),\n      background: \"bg-gradient-to-br from-indigo-900 via-purple-800 to-pink-600\"\n    }\n  ];\n\n  useEffect(() => {\n    const timer = setInterval(() => {\n      setCurrentSlide((prev) => (prev + 1) % slides.length);\n    }, 5000);\n\n    return () => clearInterval(timer);\n  }, [slides.length]);\n\n  const nextSlide = () => {\n    setCurrentSlide((prev) => (prev + 1) % slides.length);\n  };\n\n  const prevSlide = () => {\n    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length);\n  };\n\n  return (\n    <section className=\"relative min-h-screen flex items-center justify-center overflow-hidden\">\n      {/* Background with animated gradient */}\n      <div className={`absolute inset-0 ${slides[currentSlide].background} transition-all duration-1000`}>\n        {/* Animated background shapes */}\n        <div className=\"absolute top-20 left-10 w-32 h-32 bg-white/10 rounded-full animate-pulse\"></div>\n        <div className=\"absolute top-40 right-20 w-24 h-24 bg-white/5 rounded-full animate-bounce\"></div>\n        <div className=\"absolute bottom-20 left-1/4 w-40 h-40 bg-white/5 rounded-full animate-pulse\"></div>\n        <div className=\"absolute bottom-40 right-1/3 w-20 h-20 bg-white/10 rounded-full animate-bounce\"></div>\n        \n        {/* Floating geometric shapes */}\n        <div className=\"absolute top-1/3 left-1/4 transform rotate-45\">\n          <div className=\"w-16 h-16 bg-gradient-to-r from-yellow-400 to-orange-500 opacity-20 animate-spin-slow\"></div>\n        </div>\n        <div className=\"absolute top-1/2 right-1/4 transform -rotate-12\">\n          <div className=\"w-12 h-12 bg-gradient-to-r from-pink-400 to-purple-500 opacity-30 animate-pulse\"></div>\n        </div>\n      </div>\n\n      {/* Content */}\n      <div className=\"relative z-10 container mx-auto px-4 text-center text-white\">\n        <div className=\"max-w-4xl mx-auto\">\n          {/* Main heading */}\n          <h1 className=\"text-4xl md:text-6xl lg:text-7xl font-bold mb-6 leading-tight\">\n            <span className=\"block opacity-0 animate-fade-in-up\" style={{ animationDelay: '0.2s', animationFillMode: 'forwards' }}>\n              {slides[currentSlide].title.split(' ').slice(0, 3).join(' ')}\n            </span>\n            <span className=\"block opacity-0 animate-fade-in-up\" style={{ animationDelay: '0.4s', animationFillMode: 'forwards' }}>\n              {slides[currentSlide].title.split(' ').slice(3).join(' ')}\n            </span>\n          </h1>\n\n          {/* Subtitle */}\n          <p className=\"text-lg md:text-xl lg:text-2xl mb-8 opacity-90 max-w-3xl mx-auto leading-relaxed opacity-0 animate-fade-in-up\" style={{ animationDelay: '0.6s', animationFillMode: 'forwards' }}>\n            {slides[currentSlide].subtitle}\n          </p>\n\n          {/* CTA Button */}\n          <div className=\"opacity-0 animate-fade-in-up\" style={{ animationDelay: '0.8s', animationFillMode: 'forwards' }}>\n            <Link \n              href=\"/contact\" \n              className=\"inline-block bg-gradient-to-r from-yellow-400 to-orange-500 text-gray-900 font-bold px-8 py-4 rounded-full text-lg hover:from-yellow-300 hover:to-orange-400 transition-all duration-300 transform hover:scale-105 hover:shadow-2xl\"\n            >\n              {slides[currentSlide].cta}\n            </Link>\n          </div>\n\n          {/* Stats or features */}\n          <div className=\"mt-16 grid grid-cols-1 md:grid-cols-3 gap-8 opacity-0 animate-fade-in-up\" style={{ animationDelay: '1s', animationFillMode: 'forwards' }}>\n            <div className=\"text-center\">\n              <div className=\"text-3xl md:text-4xl font-bold mb-2\">500+</div>\n              <div className=\"text-lg opacity-80\">{t('hero.stats.projects')}</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-3xl md:text-4xl font-bold mb-2\">200+</div>\n              <div className=\"text-lg opacity-80\">{t('hero.stats.clients')}</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-3xl md:text-4xl font-bold mb-2\">5+</div>\n              <div className=\"text-lg opacity-80\">{t('hero.stats.experience')}</div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Navigation arrows */}\n      <button \n        onClick={prevSlide}\n        className=\"absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/20 hover:bg-white/30 text-white p-3 rounded-full transition-all duration-300 z-20\"\n      >\n        <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 19l-7-7 7-7\" />\n        </svg>\n      </button>\n      \n      <button \n        onClick={nextSlide}\n        className=\"absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/20 hover:bg-white/30 text-white p-3 rounded-full transition-all duration-300 z-20\"\n      >\n        <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n        </svg>\n      </button>\n\n      {/* Slide indicators */}\n      <div className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 flex space-x-3 z-20\">\n        {slides.map((_, index) => (\n          <button\n            key={index}\n            onClick={() => setCurrentSlide(index)}\n            className={`w-3 h-3 rounded-full transition-all duration-300 ${\n              index === currentSlide ? 'bg-white' : 'bg-white/50'\n            }`}\n          />\n        ))}\n      </div>\n\n      {/* Scroll indicator */}\n      <div className=\"absolute bottom-4 left-1/2 transform -translate-x-1/2 animate-bounce\">\n        <svg className=\"w-6 h-6 text-white/70\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 14l-7 7m0 0l-7-7m7 7V3\" />\n        </svg>\n      </div>\n    </section>\n  );\n};\n\nexport default HeroSection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMA,MAAM,cAAc;;IAClB,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;IAC/B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,SAAS;QACb;YACE,OAAO,EAAE;YACT,UAAU,EAAE;YACZ,KAAK,EAAE;YACP,YAAY;QACd;QACA;YACE,OAAO,EAAE;YACT,UAAU,EAAE;YACZ,KAAK,EAAE;YACP,YAAY;QACd;QACA;YACE,OAAO,EAAE;YACT,UAAU,EAAE;YACZ,KAAK,EAAE;YACP,YAAY;QACd;KACD;IAED,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM,QAAQ;+CAAY;oBACxB;uDAAgB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,OAAO,MAAM;;gBACtD;8CAAG;YAEH;yCAAO,IAAM,cAAc;;QAC7B;gCAAG;QAAC,OAAO,MAAM;KAAC;IAElB,MAAM,YAAY;QAChB,gBAAgB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,OAAO,MAAM;IACtD;IAEA,MAAM,YAAY;QAChB,gBAAgB,CAAC,OAAS,CAAC,OAAO,IAAI,OAAO,MAAM,IAAI,OAAO,MAAM;IACtE;IAEA,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAW,CAAC,iBAAiB,EAAE,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,6BAA6B,CAAC;;kCAEhG,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCAGf,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;;;;;;;;;;kCAEjB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;0BAKnB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAG,WAAU;;8CACZ,6LAAC;oCAAK,WAAU;oCAAqC,OAAO;wCAAE,gBAAgB;wCAAQ,mBAAmB;oCAAW;8CACjH,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC;;;;;;8CAE1D,6LAAC;oCAAK,WAAU;oCAAqC,OAAO;wCAAE,gBAAgB;wCAAQ,mBAAmB;oCAAW;8CACjH,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC,GAAG,IAAI,CAAC;;;;;;;;;;;;sCAKzD,6LAAC;4BAAE,WAAU;4BAAgH,OAAO;gCAAE,gBAAgB;gCAAQ,mBAAmB;4BAAW;sCACzL,MAAM,CAAC,aAAa,CAAC,QAAQ;;;;;;sCAIhC,6LAAC;4BAAI,WAAU;4BAA+B,OAAO;gCAAE,gBAAgB;gCAAQ,mBAAmB;4BAAW;sCAC3G,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CAET,MAAM,CAAC,aAAa,CAAC,GAAG;;;;;;;;;;;sCAK7B,6LAAC;4BAAI,WAAU;4BAA2E,OAAO;gCAAE,gBAAgB;gCAAM,mBAAmB;4BAAW;;8CACrJ,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAsC;;;;;;sDACrD,6LAAC;4CAAI,WAAU;sDAAsB,EAAE;;;;;;;;;;;;8CAEzC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAsC;;;;;;sDACrD,6LAAC;4CAAI,WAAU;sDAAsB,EAAE;;;;;;;;;;;;8CAEzC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAsC;;;;;;sDACrD,6LAAC;4CAAI,WAAU;sDAAsB,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO/C,6LAAC;gBACC,SAAS;gBACT,WAAU;0BAEV,cAAA,6LAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BACjE,cAAA,6LAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;;;;;;0BAIzE,6LAAC;gBACC,SAAS;gBACT,WAAU;0BAEV,cAAA,6LAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BACjE,cAAA,6LAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;;;;;;0BAKzE,6LAAC;gBAAI,WAAU;0BACZ,OAAO,GAAG,CAAC,CAAC,GAAG,sBACd,6LAAC;wBAEC,SAAS,IAAM,gBAAgB;wBAC/B,WAAW,CAAC,iDAAiD,EAC3D,UAAU,eAAe,aAAa,eACtC;uBAJG;;;;;;;;;;0BAUX,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;oBAAwB,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BAC/E,cAAA,6LAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;AAK/E;GAlJM;;QACiB,sIAAA,CAAA,cAAW;;;KAD5B;uCAoJS", "debugId": null}}, {"offset": {"line": 426, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/agency/apollo-agency-website/src/components/ServicesSection.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { useLanguage } from '@/contexts/LanguageContext';\n\nconst ServicesSection = () => {\n  const { t, isRTL } = useLanguage();\n\n  const services = [\n    {\n      icon: \"🎯\",\n      title: t('services.brandDev.title'),\n      description: t('services.brandDev.desc'),\n      link: \"/services/branding\"\n    },\n    {\n      icon: \"✍️\",\n      title: t('services.content.title'),\n      description: t('services.content.desc'),\n      link: \"/services/content-creation\"\n    },\n    {\n      icon: \"👥\",\n      title: t('services.influencer.title'),\n      description: t('services.influencer.desc'),\n      link: \"/services/influencer-marketing\"\n    },\n    {\n      icon: \"📱\",\n      title: t('services.digital.title'),\n      description: t('services.digital.desc'),\n      link: \"/services/digital-marketing\"\n    },\n    {\n      icon: \"💻\",\n      title: t('services.web.title'),\n      description: t('services.web.desc'),\n      link: \"/services/web-development\"\n    },\n    {\n      icon: \"🎨\",\n      title: t('services.graphic.title'),\n      description: t('services.graphic.desc'),\n      link: \"/services/graphic-design\"\n    },\n    {\n      icon: \"🎬\",\n      title: t('services.video.title'),\n      description: t('services.video.desc'),\n      link: \"/services/video-production\"\n    },\n    {\n      icon: \"🔍\",\n      title: t('services.seoServices.title'),\n      description: t('services.seoServices.desc'),\n      link: \"/services/seo\"\n    }\n  ];\n\n  return (\n    <section className=\"py-20 bg-gray-50\">\n      <div className=\"container mx-auto px-4\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <div className=\"flex justify-center mb-6\">\n            <div className=\"w-16 h-16 bg-gradient-to-r from-purple-600 to-pink-500 rounded-full flex items-center justify-center\">\n              <span className=\"text-2xl\">🚀</span>\n            </div>\n          </div>\n          <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\n            {t('services.title')}\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            {t('services.subtitle')}\n          </p>\n        </div>\n\n        {/* Services Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {services.map((service, index) => (\n            <div\n              key={index}\n              className=\"bg-white rounded-xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 group\"\n            >\n              {/* Icon */}\n              <div className=\"text-center mb-6\">\n                <div className=\"w-20 h-20 bg-gradient-to-r from-purple-100 to-pink-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:from-purple-200 group-hover:to-pink-200 transition-all duration-300\">\n                  <span className=\"text-3xl\">{service.icon}</span>\n                </div>\n              </div>\n\n              {/* Content */}\n              <div className=\"text-center\">\n                <h3 className=\"text-xl font-bold text-gray-900 mb-4 group-hover:text-purple-600 transition-colors duration-300\">\n                  {service.title}\n                </h3>\n                <p className=\"text-gray-600 mb-6 leading-relaxed\">\n                  {service.description}\n                </p>\n                <Link\n                  href={service.link}\n                  className=\"inline-flex items-center text-purple-600 hover:text-purple-700 font-semibold transition-colors duration-300\"\n                >\n                  {t('services.learnMore')}\n                  <svg className={`w-4 h-4 transform group-hover:translate-x-1 transition-transform duration-300 ${isRTL ? 'mr-2 rotate-180' : 'ml-2'}`} fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                  </svg>\n                </Link>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* CTA Section */}\n        <div className=\"text-center mt-16\">\n          <Link\n            href=\"/services\"\n            className=\"inline-block bg-gradient-to-r from-purple-600 to-pink-500 text-white font-bold px-8 py-4 rounded-full text-lg hover:from-purple-700 hover:to-pink-600 transition-all duration-300 transform hover:scale-105 hover:shadow-xl\"\n          >\n            {t('services.viewAll')}\n          </Link>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default ServicesSection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKA,MAAM,kBAAkB;;IACtB,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;IAE/B,MAAM,WAAW;QACf;YACE,MAAM;YACN,OAAO,EAAE;YACT,aAAa,EAAE;YACf,MAAM;QACR;QACA;YACE,MAAM;YACN,OAAO,EAAE;YACT,aAAa,EAAE;YACf,MAAM;QACR;QACA;YACE,MAAM;YACN,OAAO,EAAE;YACT,aAAa,EAAE;YACf,MAAM;QACR;QACA;YACE,MAAM;YACN,OAAO,EAAE;YACT,aAAa,EAAE;YACf,MAAM;QACR;QACA;YACE,MAAM;YACN,OAAO,EAAE;YACT,aAAa,EAAE;YACf,MAAM;QACR;QACA;YACE,MAAM;YACN,OAAO,EAAE;YACT,aAAa,EAAE;YACf,MAAM;QACR;QACA;YACE,MAAM;YACN,OAAO,EAAE;YACT,aAAa,EAAE;YACf,MAAM;QACR;QACA;YACE,MAAM;YACN,OAAO,EAAE;YACT,aAAa,EAAE;YACf,MAAM;QACR;KACD;IAED,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAAW;;;;;;;;;;;;;;;;sCAG/B,6LAAC;4BAAG,WAAU;sCACX,EAAE;;;;;;sCAEL,6LAAC;4BAAE,WAAU;sCACV,EAAE;;;;;;;;;;;;8BAKP,6LAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC;4BAEC,WAAU;;8CAGV,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDAAY,QAAQ,IAAI;;;;;;;;;;;;;;;;8CAK5C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDACX,QAAQ,KAAK;;;;;;sDAEhB,6LAAC;4CAAE,WAAU;sDACV,QAAQ,WAAW;;;;;;sDAEtB,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAM,QAAQ,IAAI;4CAClB,WAAU;;gDAET,EAAE;8DACH,6LAAC;oDAAI,WAAW,CAAC,8EAA8E,EAAE,QAAQ,oBAAoB,QAAQ;oDAAE,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DAC/K,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;2BAxBtE;;;;;;;;;;8BAiCX,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;kCAET,EAAE;;;;;;;;;;;;;;;;;;;;;;AAMf;GAxHM;;QACiB,sIAAA,CAAA,cAAW;;;KAD5B;uCA0HS", "debugId": null}}, {"offset": {"line": 682, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/agency/apollo-agency-website/src/components/PortfolioSection.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { useLanguage } from '@/contexts/LanguageContext';\n\nconst PortfolioSection = () => {\n  const { t, isRTL } = useLanguage();\n\n  const portfolioItems = [\n    {\n      id: 1,\n      title: \"Brand Identity Design\",\n      category: \"Branding\",\n      image: \"/api/placeholder/400/500\",\n      description: \"Complete brand identity design for a tech startup\",\n      link: \"/portfolio/brand-identity\"\n    },\n    {\n      id: 2,\n      title: \"Social Media Designs\",\n      category: \"Social Media\",\n      image: \"/api/placeholder/400/500\",\n      description: \"Creative social media content for various platforms\",\n      link: \"/portfolio/social-media\"\n    },\n    {\n      id: 3,\n      title: \"SEO Optimization\",\n      category: \"SEO\",\n      image: \"/api/placeholder/400/500\",\n      description: \"Search engine optimization for e-commerce website\",\n      link: \"/portfolio/seo\"\n    },\n    {\n      id: 4,\n      title: \"Digital Marketing Campaign\",\n      category: \"Marketing\",\n      image: \"/api/placeholder/400/500\",\n      description: \"Comprehensive digital marketing campaign management\",\n      link: \"/portfolio/digital-marketing\"\n    }\n  ];\n\n  return (\n    <section className={`py-20 bg-white ${isRTL ? 'rtl' : ''}`}>\n      <div className=\"container mx-auto px-4\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <div className=\"flex justify-center mb-6\">\n            <div className=\"w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-500 rounded-full flex items-center justify-center\">\n              <span className=\"text-2xl\">🎨</span>\n            </div>\n          </div>\n          <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\n            {t('portfolio.title')}\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            {t('portfolio.subtitle')}\n          </p>\n        </div>\n\n        {/* Portfolio Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {portfolioItems.map((item) => (\n            <div\n              key={item.id}\n              className=\"group relative overflow-hidden rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2\"\n            >\n              {/* Image Container */}\n              <div className=\"relative h-80 bg-gradient-to-br from-purple-100 to-pink-100 overflow-hidden\">\n                {/* Placeholder for image */}\n                <div className=\"w-full h-full bg-gradient-to-br from-purple-200 to-pink-200 flex items-center justify-center\">\n                  <div className=\"text-center\">\n                    <div className=\"text-4xl mb-2\">\n                      {item.category === 'Branding' && '🎯'}\n                      {item.category === 'Social Media' && '📱'}\n                      {item.category === 'SEO' && '🔍'}\n                      {item.category === 'Marketing' && '📊'}\n                    </div>\n                    <p className=\"text-gray-600 font-medium\">{item.category}</p>\n                  </div>\n                </div>\n                \n                {/* Overlay */}\n                <div className=\"absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\">\n                  <div className=\"absolute bottom-4 left-4 right-4 text-white\">\n                    <h3 className=\"text-lg font-bold mb-2\">{item.title}</h3>\n                    <p className=\"text-sm opacity-90 mb-3\">{item.description}</p>\n                    <Link\n                      href={item.link}\n                      className=\"inline-flex items-center text-white bg-white/20 backdrop-blur-sm px-4 py-2 rounded-full text-sm font-medium hover:bg-white/30 transition-all duration-300\"\n                    >\n                      {t('portfolio.viewProject')}\n                      <svg className={`w-4 h-4 ${isRTL ? 'mr-2 rotate-180' : 'ml-2'}`} fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                      </svg>\n                    </Link>\n                  </div>\n                </div>\n              </div>\n\n              {/* Category Badge */}\n              <div className=\"absolute top-4 left-4\">\n                <span className=\"bg-white/90 backdrop-blur-sm text-gray-800 px-3 py-1 rounded-full text-sm font-medium\">\n                  {item.category}\n                </span>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* Portfolio Categories */}\n        <div className=\"mt-16 text-center\">\n          <h3 className=\"text-2xl font-bold text-gray-900 mb-8\">{t('portfolio.browseByCategory')}</h3>\n          <div className=\"flex flex-wrap justify-center gap-4\">\n            {[\n              { key: 'brand-identity', label: t('portfolio.categories.brandIdentity') },\n              { key: 'social-media', label: t('portfolio.categories.socialMedia') },\n              { key: 'graphic-design', label: t('portfolio.categories.graphicDesign') },\n              { key: 'landing-pages', label: t('portfolio.categories.landingPages') },\n              { key: 'marketing', label: t('portfolio.categories.marketingCampaigns') }\n            ].map((category) => (\n              <Link\n                key={category.key}\n                href={`/portfolio/${category.key}`}\n                className=\"bg-gray-100 hover:bg-purple-100 text-gray-700 hover:text-purple-700 px-6 py-3 rounded-full font-medium transition-all duration-300 transform hover:scale-105\"\n              >\n                {category.label}\n              </Link>\n            ))}\n          </div>\n        </div>\n\n        {/* CTA Section */}\n        <div className=\"text-center mt-16\">\n          <Link\n            href=\"/portfolio\"\n            className=\"inline-block bg-gradient-to-r from-blue-600 to-purple-500 text-white font-bold px-8 py-4 rounded-full text-lg hover:from-blue-700 hover:to-purple-600 transition-all duration-300 transform hover:scale-105 hover:shadow-xl\"\n          >\n            {t('portfolio.viewFullPortfolio')}\n          </Link>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default PortfolioSection;\n"], "names": [], "mappings": ";;;;AAEA;AAEA;;;AAJA;;;AAMA,MAAM,mBAAmB;;IACvB,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;IAE/B,MAAM,iBAAiB;QACrB;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,aAAa;YACb,MAAM;QACR;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,aAAa;YACb,MAAM;QACR;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,aAAa;YACb,MAAM;QACR;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,aAAa;YACb,MAAM;QACR;KACD;IAED,qBACE,6LAAC;QAAQ,WAAW,CAAC,eAAe,EAAE,QAAQ,QAAQ,IAAI;kBACxD,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAAW;;;;;;;;;;;;;;;;sCAG/B,6LAAC;4BAAG,WAAU;sCACX,EAAE;;;;;;sCAEL,6LAAC;4BAAE,WAAU;sCACV,EAAE;;;;;;;;;;;;8BAKP,6LAAC;oBAAI,WAAU;8BACZ,eAAe,GAAG,CAAC,CAAC,qBACnB,6LAAC;4BAEC,WAAU;;8CAGV,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;4DACZ,KAAK,QAAQ,KAAK,cAAc;4DAChC,KAAK,QAAQ,KAAK,kBAAkB;4DACpC,KAAK,QAAQ,KAAK,SAAS;4DAC3B,KAAK,QAAQ,KAAK,eAAe;;;;;;;kEAEpC,6LAAC;wDAAE,WAAU;kEAA6B,KAAK,QAAQ;;;;;;;;;;;;;;;;;sDAK3D,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAA0B,KAAK,KAAK;;;;;;kEAClD,6LAAC;wDAAE,WAAU;kEAA2B,KAAK,WAAW;;;;;;kEACxD,6LAAC,+JAAA,CAAA,UAAI;wDACH,MAAM,KAAK,IAAI;wDACf,WAAU;;4DAET,EAAE;0EACH,6LAAC;gEAAI,WAAW,CAAC,QAAQ,EAAE,QAAQ,oBAAoB,QAAQ;gEAAE,MAAK;gEAAO,QAAO;gEAAe,SAAQ;0EACzG,cAAA,6LAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAQ/E,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDACb,KAAK,QAAQ;;;;;;;;;;;;2BAvCb,KAAK,EAAE;;;;;;;;;;8BA+ClB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAyC,EAAE;;;;;;sCACzD,6LAAC;4BAAI,WAAU;sCACZ;gCACC;oCAAE,KAAK;oCAAkB,OAAO,EAAE;gCAAsC;gCACxE;oCAAE,KAAK;oCAAgB,OAAO,EAAE;gCAAoC;gCACpE;oCAAE,KAAK;oCAAkB,OAAO,EAAE;gCAAsC;gCACxE;oCAAE,KAAK;oCAAiB,OAAO,EAAE;gCAAqC;gCACtE;oCAAE,KAAK;oCAAa,OAAO,EAAE;gCAA2C;6BACzE,CAAC,GAAG,CAAC,CAAC,yBACL,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,CAAC,WAAW,EAAE,SAAS,GAAG,EAAE;oCAClC,WAAU;8CAET,SAAS,KAAK;mCAJV,SAAS,GAAG;;;;;;;;;;;;;;;;8BAWzB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;kCAET,EAAE;;;;;;;;;;;;;;;;;;;;;;AAMf;GA5IM;;QACiB,sIAAA,CAAA,cAAW;;;KAD5B;uCA8IS", "debugId": null}}, {"offset": {"line": 1022, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/agency/apollo-agency-website/src/components/ClientsSection.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useLanguage } from '@/contexts/LanguageContext';\n\nconst ClientsSection = () => {\n  const { t, isRTL } = useLanguage();\n  const [currentTestimonial, setCurrentTestimonial] = useState(0);\n\n  const clients = [\n    { name: \"TechCorp\", logo: \"🏢\" },\n    { name: \"InnovateLab\", logo: \"🔬\" },\n    { name: \"DesignStudio\", logo: \"🎨\" },\n    { name: \"StartupHub\", logo: \"🚀\" },\n    { name: \"MediaGroup\", logo: \"📺\" },\n    { name: \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", logo: \"🛍️\" },\n    { name: \"FinanceFirst\", logo: \"💰\" },\n    { name: \"HealthPlus\", logo: \"🏥\" },\n    { name: \"EduTech\", logo: \"📚\" },\n    { name: \"FoodieApp\", logo: \"🍕\" }\n  ];\n\n  const testimonials = [\n    {\n      name: t('testimonials.client1.name') || \"<PERSON>\",\n      company: t('testimonials.client1.company') || \"TechCorp CEO\",\n      role: \"Marketing Director\",\n      content: t('testimonials.client1.text') || \"Serv Infinity transformed our digital presence completely. Their strategic approach increased our engagement by 300%.\",\n      rating: 5\n    },\n    {\n      name: t('testimonials.client2.name') || \"Ahmed <PERSON>-Rashid\",\n      company: t('testimonials.client2.company') || \"E-commerce Director\",\n      role: \"CEO\",\n      content: t('testimonials.client2.text') || \"Outstanding results! Our sales increased by 250% within 6 months of working with their team.\",\n      rating: 5\n    },\n    {\n      name: t('testimonials.client3.name') || \"Maria Rodriguez\",\n      company: t('testimonials.client3.company') || \"Startup Founder\",\n      role: \"Creative Director\",\n      content: t('testimonials.client3.text') || \"Professional service and exceptional creativity. They helped us build a strong brand from scratch.\",\n      rating: 5\n    }\n  ];\n\n  useEffect(() => {\n    const timer = setInterval(() => {\n      setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);\n    }, 5000);\n\n    return () => clearInterval(timer);\n  }, [testimonials.length]);\n\n  return (\n    <section className={`py-20 bg-gray-50 ${isRTL ? 'rtl' : ''}`}>\n      <div className=\"container mx-auto px-4\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\n            {t('clients.title')}\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            {t('clients.subtitle')}\n          </p>\n        </div>\n\n        {/* Client Logos Grid */}\n        <div className=\"mb-20\">\n          <div className=\"grid grid-cols-2 md:grid-cols-5 gap-8 items-center\">\n            {clients.map((client, index) => (\n              <div\n                key={index}\n                className=\"bg-white rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow duration-300 flex flex-col items-center justify-center h-24 group\"\n              >\n                <div className=\"text-3xl mb-2 group-hover:scale-110 transition-transform duration-300\">\n                  {client.logo}\n                </div>\n                <span className=\"text-sm font-medium text-gray-600\">{client.name}</span>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Testimonials Section */}\n        <div className=\"bg-white rounded-2xl p-8 md:p-12 shadow-xl\">\n          <div className=\"text-center mb-12\">\n            <h3 className=\"text-3xl font-bold text-gray-900 mb-4\">\n              {t('clients.testimonials.title')}\n            </h3>\n            <p className=\"text-gray-600\">\n              {t('clients.testimonials.subtitle')}\n            </p>\n          </div>\n\n          {/* Testimonial Carousel */}\n          <div className=\"relative max-w-4xl mx-auto\">\n            <div className=\"overflow-hidden\">\n              <div \n                className=\"flex transition-transform duration-500 ease-in-out\"\n                style={{ transform: `translateX(-${currentTestimonial * 100}%)` }}\n              >\n                {testimonials.map((testimonial, index) => (\n                  <div key={index} className=\"w-full flex-shrink-0 px-4\">\n                    <div className=\"text-center\">\n                      {/* Quote Icon */}\n                      <div className=\"text-6xl text-purple-200 mb-6\">❝</div>\n                      \n                      {/* Testimonial Content */}\n                      <blockquote className=\"text-xl md:text-2xl text-gray-700 mb-8 leading-relaxed\">\n                        \"{testimonial.content}\"\n                      </blockquote>\n\n                      {/* Rating */}\n                      <div className=\"flex justify-center mb-6\">\n                        {[...Array(testimonial.rating)].map((_, i) => (\n                          <svg key={i} className=\"w-6 h-6 text-yellow-400 fill-current\" viewBox=\"0 0 24 24\">\n                            <path d=\"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\"/>\n                          </svg>\n                        ))}\n                      </div>\n\n                      {/* Client Info */}\n                      <div className=\"text-center\">\n                        <h4 className=\"text-lg font-bold text-gray-900\">{testimonial.name}</h4>\n                        <p className=\"text-purple-600 font-medium\">{testimonial.role}</p>\n                        <p className=\"text-gray-500\">{testimonial.company}</p>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            {/* Navigation Dots */}\n            <div className=\"flex justify-center mt-8 space-x-3\">\n              {testimonials.map((_, index) => (\n                <button\n                  key={index}\n                  onClick={() => setCurrentTestimonial(index)}\n                  className={`w-3 h-3 rounded-full transition-all duration-300 ${\n                    index === currentTestimonial \n                      ? 'bg-purple-600 scale-125' \n                      : 'bg-gray-300 hover:bg-gray-400'\n                  }`}\n                />\n              ))}\n            </div>\n          </div>\n        </div>\n\n        {/* Stats Section */}\n        <div className=\"mt-20 grid grid-cols-1 md:grid-cols-4 gap-8 text-center\">\n          <div className=\"bg-white rounded-lg p-6 shadow-md\">\n            <div className=\"text-3xl font-bold text-purple-600 mb-2\">500+</div>\n            <div className=\"text-gray-600\">{t('clients.stats.projects')}</div>\n          </div>\n          <div className=\"bg-white rounded-lg p-6 shadow-md\">\n            <div className=\"text-3xl font-bold text-purple-600 mb-2\">200+</div>\n            <div className=\"text-gray-600\">{t('clients.stats.clients')}</div>\n          </div>\n          <div className=\"bg-white rounded-lg p-6 shadow-md\">\n            <div className=\"text-3xl font-bold text-purple-600 mb-2\">5+</div>\n            <div className=\"text-gray-600\">{t('clients.stats.experience')}</div>\n          </div>\n          <div className=\"bg-white rounded-lg p-6 shadow-md\">\n            <div className=\"text-3xl font-bold text-purple-600 mb-2\">98%</div>\n            <div className=\"text-gray-600\">{t('clients.stats.satisfaction')}</div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default ClientsSection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKA,MAAM,iBAAiB;;IACrB,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;IAC/B,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,MAAM,UAAU;QACd;YAAE,MAAM;YAAY,MAAM;QAAK;QAC/B;YAAE,MAAM;YAAe,MAAM;QAAK;QAClC;YAAE,MAAM;YAAgB,MAAM;QAAK;QACnC;YAAE,MAAM;YAAc,MAAM;QAAK;QACjC;YAAE,MAAM;YAAc,MAAM;QAAK;QACjC;YAAE,MAAM;YAAe,MAAM;QAAM;QACnC;YAAE,MAAM;YAAgB,MAAM;QAAK;QACnC;YAAE,MAAM;YAAc,MAAM;QAAK;QACjC;YAAE,MAAM;YAAW,MAAM;QAAK;QAC9B;YAAE,MAAM;YAAa,MAAM;QAAK;KACjC;IAED,MAAM,eAAe;QACnB;YACE,MAAM,EAAE,gCAAgC;YACxC,SAAS,EAAE,mCAAmC;YAC9C,MAAM;YACN,SAAS,EAAE,gCAAgC;YAC3C,QAAQ;QACV;QACA;YACE,MAAM,EAAE,gCAAgC;YACxC,SAAS,EAAE,mCAAmC;YAC9C,MAAM;YACN,SAAS,EAAE,gCAAgC;YAC3C,QAAQ;QACV;QACA;YACE,MAAM,EAAE,gCAAgC;YACxC,SAAS,EAAE,mCAAmC;YAC9C,MAAM;YACN,SAAS,EAAE,gCAAgC;YAC3C,QAAQ;QACV;KACD;IAED,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM,QAAQ;kDAAY;oBACxB;0DAAsB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,aAAa,MAAM;;gBAClE;iDAAG;YAEH;4CAAO,IAAM,cAAc;;QAC7B;mCAAG;QAAC,aAAa,MAAM;KAAC;IAExB,qBACE,6LAAC;QAAQ,WAAW,CAAC,iBAAiB,EAAE,QAAQ,QAAQ,IAAI;kBAC1D,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCACX,EAAE;;;;;;sCAEL,6LAAC;4BAAE,WAAU;sCACV,EAAE;;;;;;;;;;;;8BAKP,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACZ,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,6LAAC;gCAEC,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;kDACZ,OAAO,IAAI;;;;;;kDAEd,6LAAC;wCAAK,WAAU;kDAAqC,OAAO,IAAI;;;;;;;+BAN3D;;;;;;;;;;;;;;;8BAab,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CACX,EAAE;;;;;;8CAEL,6LAAC;oCAAE,WAAU;8CACV,EAAE;;;;;;;;;;;;sCAKP,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,WAAW,CAAC,YAAY,EAAE,qBAAqB,IAAI,EAAE,CAAC;wCAAC;kDAE/D,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,6LAAC;gDAAgB,WAAU;0DACzB,cAAA,6LAAC;oDAAI,WAAU;;sEAEb,6LAAC;4DAAI,WAAU;sEAAgC;;;;;;sEAG/C,6LAAC;4DAAW,WAAU;;gEAAyD;gEAC3E,YAAY,OAAO;gEAAC;;;;;;;sEAIxB,6LAAC;4DAAI,WAAU;sEACZ;mEAAI,MAAM,YAAY,MAAM;6DAAE,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtC,6LAAC;oEAAY,WAAU;oEAAuC,SAAQ;8EACpE,cAAA,6LAAC;wEAAK,GAAE;;;;;;mEADA;;;;;;;;;;sEAOd,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EAAmC,YAAY,IAAI;;;;;;8EACjE,6LAAC;oEAAE,WAAU;8EAA+B,YAAY,IAAI;;;;;;8EAC5D,6LAAC;oEAAE,WAAU;8EAAiB,YAAY,OAAO;;;;;;;;;;;;;;;;;;+CAvB7C;;;;;;;;;;;;;;;8CAgChB,6LAAC;oCAAI,WAAU;8CACZ,aAAa,GAAG,CAAC,CAAC,GAAG,sBACpB,6LAAC;4CAEC,SAAS,IAAM,sBAAsB;4CACrC,WAAW,CAAC,iDAAiD,EAC3D,UAAU,qBACN,4BACA,iCACJ;2CANG;;;;;;;;;;;;;;;;;;;;;;8BAcf,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAA0C;;;;;;8CACzD,6LAAC;oCAAI,WAAU;8CAAiB,EAAE;;;;;;;;;;;;sCAEpC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAA0C;;;;;;8CACzD,6LAAC;oCAAI,WAAU;8CAAiB,EAAE;;;;;;;;;;;;sCAEpC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAA0C;;;;;;8CACzD,6LAAC;oCAAI,WAAU;8CAAiB,EAAE;;;;;;;;;;;;sCAEpC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAA0C;;;;;;8CACzD,6LAAC;oCAAI,WAAU;8CAAiB,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM9C;GAxKM;;QACiB,sIAAA,CAAA,cAAW;;;KAD5B;uCA0KS", "debugId": null}}, {"offset": {"line": 1494, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/agency/apollo-agency-website/src/components/BlogSection.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { useLanguage } from '@/contexts/LanguageContext';\n\nconst BlogSection = () => {\n  const { t, isRTL } = useLanguage();\n\n  const blogPosts = [\n    {\n      id: 1,\n      title: t('blog.post1.title') || \"How Your Business Appears in Google's First Search Results\",\n      excerpt: t('blog.post1.excerpt') || \"Learn the essential strategies to improve your website's visibility and rank higher in Google search results.\",\n      category: t('blog.categories.seo') || \"SEO\",\n      date: t('blog.post1.date') || \"June 15, 2024\",\n      readTime: t('blog.post1.readTime') || \"5 min read\",\n      image: \"🔍\",\n      link: \"/blog/seo\"\n    },\n    {\n      id: 2,\n      title: t('blog.post2.title') || \"Advertising and Marketing Office in Saudi Arabia\",\n      excerpt: t('blog.post2.excerpt') || \"Discover the best practices for establishing a successful marketing presence in the Saudi Arabian market.\",\n      category: t('blog.categories.marketing') || \"Marketing\",\n      date: t('blog.post2.date') || \"June 10, 2024\",\n      readTime: t('blog.post2.readTime') || \"7 min read\",\n      image: \"🏢\",\n      link: \"/blog/digital-marketing\"\n    },\n    {\n      id: 3,\n      title: t('blog.post3.title') || \"Search Engine Optimization Services\",\n      excerpt: t('blog.post3.excerpt') || \"Comprehensive guide to SEO services and how they can transform your online presence and drive organic traffic.\",\n      category: t('blog.categories.seo') || \"SEO\",\n      date: t('blog.post3.date') || \"June 5, 2024\",\n      readTime: t('blog.post3.readTime') || \"6 min read\",\n      image: \"📈\",\n      link: \"/blog/search-engine-marketing\"\n    },\n    {\n      id: 4,\n      title: t('blog.post4.title') || \"Advertising Print Design\",\n      excerpt: t('blog.post4.excerpt') || \"Master the art of creating compelling print advertisements that capture attention and drive results.\",\n      category: t('blog.categories.design') || \"Design\",\n      date: t('blog.post4.date') || \"May 30, 2024\",\n      readTime: t('blog.post4.readTime') || \"4 min read\",\n      image: \"🎨\",\n      link: \"/blog/graphic-design\"\n    },\n    {\n      id: 5,\n      title: t('blog.post5.title') || \"Key Elements in Logo Creation\",\n      excerpt: t('blog.post5.excerpt') || \"Understand the fundamental principles of professional logo design and brand identity development.\",\n      category: t('blog.categories.branding') || \"Branding\",\n      date: t('blog.post5.date') || \"May 25, 2024\",\n      readTime: t('blog.post5.readTime') || \"5 min read\",\n      image: \"🎯\",\n      link: \"/blog/graphic-design\"\n    },\n    {\n      id: 6,\n      title: t('blog.post6.title') || \"Understanding B2C vs B2B Marketing\",\n      excerpt: t('blog.post6.excerpt') || \"Explore the key differences between B2C and B2B marketing strategies and when to use each approach.\",\n      category: t('blog.categories.strategy') || \"Strategy\",\n      date: t('blog.post6.date') || \"May 20, 2024\",\n      readTime: t('blog.post6.readTime') || \"8 min read\",\n      image: \"📊\",\n      link: \"/blog/digital-marketing\"\n    }\n  ];\n\n  const categories = [\n    t('blog.categories.all') || \"All\",\n    t('blog.categories.seo') || \"SEO\",\n    t('blog.categories.marketing') || \"Marketing\",\n    t('blog.categories.design') || \"Design\",\n    t('blog.categories.branding') || \"Branding\",\n    t('blog.categories.strategy') || \"Strategy\"\n  ];\n\n  return (\n    <section className=\"py-20 bg-white\">\n      <div className=\"container mx-auto px-4\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <div className=\"flex justify-center mb-6\">\n            <div className=\"w-16 h-16 bg-gradient-to-r from-green-600 to-blue-500 rounded-full flex items-center justify-center\">\n              <span className=\"text-2xl\">📚</span>\n            </div>\n          </div>\n          <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\n            {t('blog.title')}\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            {t('blog.subtitle')}\n          </p>\n        </div>\n\n        {/* Category Filter */}\n        <div className=\"flex flex-wrap justify-center gap-4 mb-12\">\n          {categories.map((category) => (\n            <button\n              key={category}\n              className=\"px-6 py-2 rounded-full border-2 border-gray-200 text-gray-600 hover:border-purple-500 hover:text-purple-600 transition-all duration-300 font-medium\"\n            >\n              {category}\n            </button>\n          ))}\n        </div>\n\n        {/* Blog Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n          {blogPosts.map((post) => (\n            <article\n              key={post.id}\n              className=\"bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 overflow-hidden group\"\n            >\n              {/* Image/Icon */}\n              <div className=\"h-48 bg-gradient-to-br from-purple-100 to-pink-100 flex items-center justify-center relative overflow-hidden\">\n                <div className=\"text-6xl group-hover:scale-110 transition-transform duration-300\">\n                  {post.image}\n                </div>\n                {/* Category Badge */}\n                <div className=\"absolute top-4 left-4\">\n                  <span className=\"bg-white/90 backdrop-blur-sm text-gray-800 px-3 py-1 rounded-full text-sm font-medium\">\n                    {post.category}\n                  </span>\n                </div>\n              </div>\n\n              {/* Content */}\n              <div className=\"p-6\">\n                {/* Meta Info */}\n                <div className=\"flex items-center text-sm text-gray-500 mb-3\">\n                  <span>{post.date}</span>\n                  <span className=\"mx-2\">•</span>\n                  <span>{post.readTime}</span>\n                </div>\n\n                {/* Title */}\n                <h3 className=\"text-xl font-bold text-gray-900 mb-3 group-hover:text-purple-600 transition-colors duration-300 line-clamp-2\">\n                  {post.title}\n                </h3>\n\n                {/* Excerpt */}\n                <p className=\"text-gray-600 mb-4 line-clamp-3\">\n                  {post.excerpt}\n                </p>\n\n                {/* Read More Link */}\n                <Link\n                  href={post.link}\n                  className=\"inline-flex items-center text-purple-600 hover:text-purple-700 font-semibold transition-colors duration-300\"\n                >\n                  {t('blog.readMore')}\n                  <svg className={`w-4 h-4 transform group-hover:translate-x-1 transition-transform duration-300 ${isRTL ? 'mr-2 rotate-180' : 'ml-2'}`} fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                  </svg>\n                </Link>\n\n                {/* Social Share */}\n                <div className=\"flex items-center justify-between mt-4 pt-4 border-t border-gray-100\">\n                  <span className=\"text-sm text-gray-500\">{t('blog.share')}</span>\n                  <div className=\"flex space-x-2\">\n                    <button className=\"w-8 h-8 bg-gray-100 hover:bg-blue-100 rounded-full flex items-center justify-center transition-colors duration-300\">\n                      <span className=\"text-sm\">📘</span>\n                    </button>\n                    <button className=\"w-8 h-8 bg-gray-100 hover:bg-blue-100 rounded-full flex items-center justify-center transition-colors duration-300\">\n                      <span className=\"text-sm\">🐦</span>\n                    </button>\n                    <button className=\"w-8 h-8 bg-gray-100 hover:bg-blue-100 rounded-full flex items-center justify-center transition-colors duration-300\">\n                      <span className=\"text-sm\">💼</span>\n                    </button>\n                  </div>\n                </div>\n              </div>\n            </article>\n          ))}\n        </div>\n\n        {/* CTA Section */}\n        <div className=\"text-center mt-16\">\n          <Link\n            href=\"/blog\"\n            className=\"inline-block bg-gradient-to-r from-green-600 to-blue-500 text-white font-bold px-8 py-4 rounded-full text-lg hover:from-green-700 hover:to-blue-600 transition-all duration-300 transform hover:scale-105 hover:shadow-xl\"\n          >\n            {t('blog.readMoreArticles')}\n          </Link>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default BlogSection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKA,MAAM,cAAc;;IAClB,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;IAE/B,MAAM,YAAY;QAChB;YACE,IAAI;YACJ,OAAO,EAAE,uBAAuB;YAChC,SAAS,EAAE,yBAAyB;YACpC,UAAU,EAAE,0BAA0B;YACtC,MAAM,EAAE,sBAAsB;YAC9B,UAAU,EAAE,0BAA0B;YACtC,OAAO;YACP,MAAM;QACR;QACA;YACE,IAAI;YACJ,OAAO,EAAE,uBAAuB;YAChC,SAAS,EAAE,yBAAyB;YACpC,UAAU,EAAE,gCAAgC;YAC5C,MAAM,EAAE,sBAAsB;YAC9B,UAAU,EAAE,0BAA0B;YACtC,OAAO;YACP,MAAM;QACR;QACA;YACE,IAAI;YACJ,OAAO,EAAE,uBAAuB;YAChC,SAAS,EAAE,yBAAyB;YACpC,UAAU,EAAE,0BAA0B;YACtC,MAAM,EAAE,sBAAsB;YAC9B,UAAU,EAAE,0BAA0B;YACtC,OAAO;YACP,MAAM;QACR;QACA;YACE,IAAI;YACJ,OAAO,EAAE,uBAAuB;YAChC,SAAS,EAAE,yBAAyB;YACpC,UAAU,EAAE,6BAA6B;YACzC,MAAM,EAAE,sBAAsB;YAC9B,UAAU,EAAE,0BAA0B;YACtC,OAAO;YACP,MAAM;QACR;QACA;YACE,IAAI;YACJ,OAAO,EAAE,uBAAuB;YAChC,SAAS,EAAE,yBAAyB;YACpC,UAAU,EAAE,+BAA+B;YAC3C,MAAM,EAAE,sBAAsB;YAC9B,UAAU,EAAE,0BAA0B;YACtC,OAAO;YACP,MAAM;QACR;QACA;YACE,IAAI;YACJ,OAAO,EAAE,uBAAuB;YAChC,SAAS,EAAE,yBAAyB;YACpC,UAAU,EAAE,+BAA+B;YAC3C,MAAM,EAAE,sBAAsB;YAC9B,UAAU,EAAE,0BAA0B;YACtC,OAAO;YACP,MAAM;QACR;KACD;IAED,MAAM,aAAa;QACjB,EAAE,0BAA0B;QAC5B,EAAE,0BAA0B;QAC5B,EAAE,gCAAgC;QAClC,EAAE,6BAA6B;QAC/B,EAAE,+BAA+B;QACjC,EAAE,+BAA+B;KAClC;IAED,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAAW;;;;;;;;;;;;;;;;sCAG/B,6LAAC;4BAAG,WAAU;sCACX,EAAE;;;;;;sCAEL,6LAAC;4BAAE,WAAU;sCACV,EAAE;;;;;;;;;;;;8BAKP,6LAAC;oBAAI,WAAU;8BACZ,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC;4BAEC,WAAU;sCAET;2BAHI;;;;;;;;;;8BASX,6LAAC;oBAAI,WAAU;8BACZ,UAAU,GAAG,CAAC,CAAC,qBACd,6LAAC;4BAEC,WAAU;;8CAGV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACZ,KAAK,KAAK;;;;;;sDAGb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DACb,KAAK,QAAQ;;;;;;;;;;;;;;;;;8CAMpB,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAM,KAAK,IAAI;;;;;;8DAChB,6LAAC;oDAAK,WAAU;8DAAO;;;;;;8DACvB,6LAAC;8DAAM,KAAK,QAAQ;;;;;;;;;;;;sDAItB,6LAAC;4CAAG,WAAU;sDACX,KAAK,KAAK;;;;;;sDAIb,6LAAC;4CAAE,WAAU;sDACV,KAAK,OAAO;;;;;;sDAIf,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAM,KAAK,IAAI;4CACf,WAAU;;gDAET,EAAE;8DACH,6LAAC;oDAAI,WAAW,CAAC,8EAA8E,EAAE,QAAQ,oBAAoB,QAAQ;oDAAE,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DAC/K,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;;sDAKzE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAyB,EAAE;;;;;;8DAC3C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAO,WAAU;sEAChB,cAAA,6LAAC;gEAAK,WAAU;0EAAU;;;;;;;;;;;sEAE5B,6LAAC;4DAAO,WAAU;sEAChB,cAAA,6LAAC;gEAAK,WAAU;0EAAU;;;;;;;;;;;sEAE5B,6LAAC;4DAAO,WAAU;sEAChB,cAAA,6LAAC;gEAAK,WAAU;0EAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BAzD7B,KAAK,EAAE;;;;;;;;;;8BAmElB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;kCAET,EAAE;;;;;;;;;;;;;;;;;;;;;;AAMf;GA3LM;;QACiB,sIAAA,CAAA,cAAW;;;KAD5B;uCA6LS", "debugId": null}}, {"offset": {"line": 1897, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/agency/apollo-agency-website/src/components/ContactSection.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { useLanguage } from '@/contexts/LanguageContext';\n\nconst ContactSection = () => {\n  const { t, isRTL } = useLanguage();\n\n  return (\n    <section className={`py-20 bg-gradient-to-br from-purple-900 via-purple-800 to-pink-600 text-white relative overflow-hidden ${isRTL ? 'rtl' : ''}`}>\n      {/* Background decorations */}\n      <div className=\"absolute top-10 left-10 w-32 h-32 bg-white/10 rounded-full animate-pulse\"></div>\n      <div className=\"absolute bottom-10 right-10 w-24 h-24 bg-white/5 rounded-full animate-bounce\"></div>\n      <div className=\"absolute top-1/2 left-1/4 w-16 h-16 bg-yellow-400/20 rounded-full animate-pulse\"></div>\n\n      <div className=\"container mx-auto px-4 relative z-10\">\n        <div className=\"max-w-4xl mx-auto text-center\">\n          {/* Icon */}\n          <div className=\"flex justify-center mb-8\">\n            <div className=\"w-20 h-20 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center\">\n              <span className=\"text-3xl\">🚀</span>\n            </div>\n          </div>\n\n          {/* Heading */}\n          <h2 className=\"text-4xl md:text-6xl font-bold mb-6 leading-tight\">\n            {t('contact.title')}\n          </h2>\n\n          {/* Subheading */}\n          <p className=\"text-xl md:text-2xl mb-8 opacity-90 leading-relaxed\">\n            {t('contact.subtitle')}\n          </p>\n\n          {/* CTA Buttons */}\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center mb-12\">\n            <Link\n              href=\"/contact\"\n              className=\"bg-gradient-to-r from-yellow-400 to-orange-500 text-gray-900 font-bold px-8 py-4 rounded-full text-lg hover:from-yellow-300 hover:to-orange-400 transition-all duration-300 transform hover:scale-105 hover:shadow-2xl\"\n            >\n              {t('contact.freeConsultation')}\n            </Link>\n            <Link\n              href=\"tel:+905528330233\"\n              className=\"bg-white/20 backdrop-blur-sm border-2 border-white/30 text-white font-bold px-8 py-4 rounded-full text-lg hover:bg-white/30 transition-all duration-300 transform hover:scale-105\"\n            >\n              {t('contact.callNow')}: +90 ************\n            </Link>\n          </div>\n\n          {/* Contact Methods */}\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 mt-16\">\n            {/* WhatsApp */}\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <span className=\"text-2xl\">💬</span>\n              </div>\n              <h3 className=\"text-xl font-bold mb-2\">{t('contact.whatsapp')}</h3>\n              <p className=\"text-white/80 mb-4\">{t('contact.instantSupport')}</p>\n              <Link\n                href=\"https://wa.me/905528330233\"\n                className=\"inline-block bg-green-500 hover:bg-green-600 text-white px-6 py-2 rounded-full transition-colors duration-300\"\n              >\n                {t('contact.chatNow')}\n              </Link>\n            </div>\n\n            {/* Email */}\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <span className=\"text-2xl\">✉️</span>\n              </div>\n              <h3 className=\"text-xl font-bold mb-2\">{t('contact.email')}</h3>\n              <p className=\"text-white/80 mb-4\">{t('contact.sendMessage')}</p>\n              <Link\n                href=\"mailto:<EMAIL>\"\n                className=\"inline-block bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-full transition-colors duration-300\"\n              >\n                {t('contact.sendEmail')}\n              </Link>\n            </div>\n\n            {/* Schedule Meeting */}\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <span className=\"text-2xl\">📅</span>\n              </div>\n              <h3 className=\"text-xl font-bold mb-2\">{t('contact.scheduleMeeting')}</h3>\n              <p className=\"text-white/80 mb-4\">{t('contact.bookConsultation')}</p>\n              <Link\n                href=\"/consultation\"\n                className=\"inline-block bg-purple-500 hover:bg-purple-600 text-white px-6 py-2 rounded-full transition-colors duration-300\"\n              >\n                {t('contact.bookNow')}\n              </Link>\n            </div>\n          </div>\n\n          {/* Trust Indicators */}\n          <div className=\"mt-16 pt-8 border-t border-white/20\">\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-8 text-center\">\n              <div>\n                <div className=\"text-2xl font-bold mb-1\">24/7</div>\n                <div className=\"text-white/80 text-sm\">{t('contact.supportAvailable')}</div>\n              </div>\n              <div>\n                <div className=\"text-2xl font-bold mb-1\">500+</div>\n                <div className=\"text-white/80 text-sm\">{t('clients.stats.projects')}</div>\n              </div>\n              <div>\n                <div className=\"text-2xl font-bold mb-1\">98%</div>\n                <div className=\"text-white/80 text-sm\">{t('clients.stats.satisfaction')}</div>\n              </div>\n              <div>\n                <div className=\"text-2xl font-bold mb-1\">5+</div>\n                <div className=\"text-white/80 text-sm\">{t('clients.stats.experience')}</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default ContactSection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKA,MAAM,iBAAiB;;IACrB,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;IAE/B,qBACE,6LAAC;QAAQ,WAAW,CAAC,uGAAuG,EAAE,QAAQ,QAAQ,IAAI;;0BAEhJ,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;0BAEf,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAAW;;;;;;;;;;;;;;;;sCAK/B,6LAAC;4BAAG,WAAU;sCACX,EAAE;;;;;;sCAIL,6LAAC;4BAAE,WAAU;sCACV,EAAE;;;;;;sCAIL,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CAET,EAAE;;;;;;8CAEL,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;wCAET,EAAE;wCAAmB;;;;;;;;;;;;;sCAK1B,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAAW;;;;;;;;;;;sDAE7B,6LAAC;4CAAG,WAAU;sDAA0B,EAAE;;;;;;sDAC1C,6LAAC;4CAAE,WAAU;sDAAsB,EAAE;;;;;;sDACrC,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDAET,EAAE;;;;;;;;;;;;8CAKP,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAAW;;;;;;;;;;;sDAE7B,6LAAC;4CAAG,WAAU;sDAA0B,EAAE;;;;;;sDAC1C,6LAAC;4CAAE,WAAU;sDAAsB,EAAE;;;;;;sDACrC,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDAET,EAAE;;;;;;;;;;;;8CAKP,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAAW;;;;;;;;;;;sDAE7B,6LAAC;4CAAG,WAAU;sDAA0B,EAAE;;;;;;sDAC1C,6LAAC;4CAAE,WAAU;sDAAsB,EAAE;;;;;;sDACrC,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDAET,EAAE;;;;;;;;;;;;;;;;;;sCAMT,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAI,WAAU;0DAA0B;;;;;;0DACzC,6LAAC;gDAAI,WAAU;0DAAyB,EAAE;;;;;;;;;;;;kDAE5C,6LAAC;;0DACC,6LAAC;gDAAI,WAAU;0DAA0B;;;;;;0DACzC,6LAAC;gDAAI,WAAU;0DAAyB,EAAE;;;;;;;;;;;;kDAE5C,6LAAC;;0DACC,6LAAC;gDAAI,WAAU;0DAA0B;;;;;;0DACzC,6LAAC;gDAAI,WAAU;0DAAyB,EAAE;;;;;;;;;;;;kDAE5C,6LAAC;;0DACC,6LAAC;gDAAI,WAAU;0DAA0B;;;;;;0DACzC,6LAAC;gDAAI,WAAU;0DAAyB,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ1D;GAtHM;;QACiB,sIAAA,CAAA,cAAW;;;KAD5B;uCAwHS", "debugId": null}}]}