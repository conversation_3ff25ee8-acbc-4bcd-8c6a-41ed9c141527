(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[281],{5735:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>l});var a=i(5155),r=i(9283),s=i(6874),n=i.n(s);function l(){let{t:e,isRTL:t}=(0,r.o)();return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50 ".concat(t?"rtl":""),children:[(0,a.jsx)("section",{className:"bg-gradient-to-br from-indigo-900 via-purple-800 to-pink-600 text-white py-20",children:(0,a.jsx)("div",{className:"container mx-auto px-4",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto text-center",children:[(0,a.jsx)("h1",{className:"text-4xl md:text-6xl font-bold mb-6",children:e("header.portfolio")}),(0,a.jsx)("p",{className:"text-xl md:text-2xl mb-8 opacity-90",children:e("portfolio.subtitle")})]})})}),(0,a.jsx)("section",{className:"py-12 bg-white",children:(0,a.jsx)("div",{className:"container mx-auto px-4",children:(0,a.jsx)("div",{className:"flex flex-wrap justify-center gap-4",children:["All","Branding","Social Media","SEO","Digital Marketing","Web Design"].map(e=>(0,a.jsx)("button",{className:"px-6 py-2 rounded-full border-2 border-purple-200 text-purple-600 hover:bg-purple-600 hover:text-white transition-all duration-300 font-medium",children:e},e))})})}),(0,a.jsx)("section",{className:"py-20",children:(0,a.jsx)("div",{className:"container mx-auto px-4",children:(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:[{id:1,title:"E-commerce Brand Identity",category:"Branding",description:"Complete brand identity design for a modern e-commerce platform",image:"\uD83D\uDECD️",tags:["Logo Design","Brand Guidelines","Visual Identity"]},{id:2,title:"Social Media Campaign",category:"Social Media",description:"Comprehensive social media strategy and content creation",image:"\uD83D\uDCF1",tags:["Content Creation","Strategy","Analytics"]},{id:3,title:"SEO Optimization Project",category:"SEO",description:"Complete SEO overhaul resulting in 300% traffic increase",image:"\uD83D\uDCC8",tags:["Technical SEO","Content Optimization","Link Building"]},{id:4,title:"Restaurant Digital Marketing",category:"Digital Marketing",description:"Full digital marketing campaign for restaurant chain",image:"\uD83C\uDF55",tags:["Google Ads","Social Media","Local SEO"]},{id:5,title:"Tech Startup Branding",category:"Branding",description:"Modern brand identity for innovative tech startup",image:"\uD83D\uDCBB",tags:["Logo Design","Website Design","Marketing Materials"]},{id:6,title:"Healthcare Website Design",category:"Web Design",description:"User-friendly website design for healthcare provider",image:"\uD83C\uDFE5",tags:["UI/UX Design","Responsive Design","Accessibility"]}].map(t=>(0,a.jsxs)("div",{className:"bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 overflow-hidden",children:[(0,a.jsxs)("div",{className:"h-64 bg-gradient-to-br from-purple-100 to-pink-100 flex items-center justify-center relative",children:[(0,a.jsx)("div",{className:"text-6xl",children:t.image}),(0,a.jsx)("div",{className:"absolute top-4 left-4",children:(0,a.jsx)("span",{className:"bg-white/90 backdrop-blur-sm text-purple-600 px-3 py-1 rounded-full text-sm font-medium",children:t.category})})]}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-3",children:t.title}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:t.description}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2 mb-4",children:t.tags.map((e,t)=>(0,a.jsx)("span",{className:"bg-gray-100 text-gray-600 px-3 py-1 rounded-full text-sm",children:e},t))}),(0,a.jsx)("button",{className:"w-full bg-gradient-to-r from-purple-600 to-pink-500 text-white font-bold py-3 px-6 rounded-lg hover:from-purple-700 hover:to-pink-600 transition-all duration-300 transform hover:scale-105",children:e("portfolio.viewProject")})]})]},t.id))})})}),(0,a.jsx)("section",{className:"bg-gradient-to-r from-indigo-600 to-purple-500 text-white py-20",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 text-center",children:[(0,a.jsx)("h2",{className:"text-3xl md:text-4xl font-bold mb-6",children:"Ready to Start Your Project?"}),(0,a.jsx)("p",{className:"text-xl mb-8 opacity-90 max-w-2xl mx-auto",children:"Let's create something amazing together. Contact us to discuss your project."}),(0,a.jsx)(n(),{href:"/contact",className:"bg-white text-indigo-600 font-bold px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 transform hover:scale-105",children:"Start Your Project"})]})})]})}},8972:(e,t,i)=>{Promise.resolve().then(i.bind(i,5735))}},e=>{var t=t=>e(e.s=t);e.O(0,[874,283,441,684,358],()=>t(8972)),_N_E=e.O()}]);