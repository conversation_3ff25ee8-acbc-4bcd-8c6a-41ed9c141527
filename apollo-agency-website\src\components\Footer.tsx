'use client';

import Link from 'next/link';
import { useLanguage } from '@/contexts/LanguageContext';

const Footer = () => {
  const { t, isRTL } = useLanguage();

  return (
    <footer className={`bg-gray-900 text-white ${isRTL ? 'rtl' : ''}`}>
      {/* Main Footer Content */}
      <div className="container mx-auto px-4 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="lg:col-span-1">
            <Link href="/" className="text-2xl font-bold text-white mb-4 block">
              Serv Infinity
            </Link>
            <p className="text-gray-300 mb-6 leading-relaxed">
              {t('footer.description')}
            </p>
            <div className="flex space-x-4">
              <a href="#" className="w-10 h-10 bg-purple-600 hover:bg-purple-700 rounded-full flex items-center justify-center transition-colors duration-300">
                <span className="text-sm">📘</span>
              </a>
              <a href="#" className="w-10 h-10 bg-purple-600 hover:bg-purple-700 rounded-full flex items-center justify-center transition-colors duration-300">
                <span className="text-sm">📷</span>
              </a>
              <a href="#" className="w-10 h-10 bg-purple-600 hover:bg-purple-700 rounded-full flex items-center justify-center transition-colors duration-300">
                <span className="text-sm">💼</span>
              </a>
              <a href="#" className="w-10 h-10 bg-purple-600 hover:bg-purple-700 rounded-full flex items-center justify-center transition-colors duration-300">
                <span className="text-sm">🐦</span>
              </a>
              <a href="#" className="w-10 h-10 bg-purple-600 hover:bg-purple-700 rounded-full flex items-center justify-center transition-colors duration-300">
                <span className="text-sm">📺</span>
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-bold mb-6">{t('footer.quickLinks')}</h3>
            <ul className="space-y-3">
              <li>
                <Link href="/" className="text-gray-300 hover:text-white transition-colors duration-300">
                  {t('header.home')}
                </Link>
              </li>
              <li>
                <Link href="/about" className="text-gray-300 hover:text-white transition-colors duration-300">
                  {t('header.about')}
                </Link>
              </li>
              <li>
                <Link href="/services" className="text-gray-300 hover:text-white transition-colors duration-300">
                  {t('header.services')}
                </Link>
              </li>
              <li>
                <Link href="/portfolio" className="text-gray-300 hover:text-white transition-colors duration-300">
                  {t('header.portfolio')}
                </Link>
              </li>
              <li>
                <Link href="/blog" className="text-gray-300 hover:text-white transition-colors duration-300">
                  {t('header.blog')}
                </Link>
              </li>
              <li>
                <Link href="/contact" className="text-gray-300 hover:text-white transition-colors duration-300">
                  {t('header.contact')}
                </Link>
              </li>
            </ul>
          </div>

          {/* Services */}
          <div>
            <h3 className="text-lg font-bold mb-6">{t('footer.services')}</h3>
            <ul className="space-y-3">
              <li>
                <Link href="/services/digital-marketing" className="text-gray-300 hover:text-white transition-colors duration-300">
                  {t('services.digitalMarketing')}
                </Link>
              </li>
              <li>
                <Link href="/services/seo" className="text-gray-300 hover:text-white transition-colors duration-300">
                  {t('services.seo')}
                </Link>
              </li>
              <li>
                <Link href="/services/graphic-design" className="text-gray-300 hover:text-white transition-colors duration-300">
                  {t('services.graphicDesign')}
                </Link>
              </li>
              <li>
                <Link href="/services/social-media" className="text-gray-300 hover:text-white transition-colors duration-300">
                  {t('services.socialMedia')}
                </Link>
              </li>
              <li>
                <Link href="/services/branding" className="text-gray-300 hover:text-white transition-colors duration-300">
                  {t('services.branding')}
                </Link>
              </li>
              <li>
                <Link href="/services/content-creation" className="text-gray-300 hover:text-white transition-colors duration-300">
                  {t('services.contentCreation')}
                </Link>
              </li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="text-lg font-bold mb-6">{t('footer.contact')}</h3>

            {/* Turkey Office */}
            <div className="mb-6">
              <h4 className="font-semibold text-purple-400 mb-2">{t('footer.turkey')}</h4>
              <div className="space-y-2 text-gray-300">
                <p className={`flex items-start ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <span className={isRTL ? 'ml-2' : 'mr-2'}>📍</span>
                  <span className="text-sm">Üniversite Mah. E-5 Yan Yol Üzeri, Çınar Sk. No:1 D:4, 34320 Avcılar/İstanbul</span>
                </p>
                <p className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <span className={isRTL ? 'ml-2' : 'mr-2'}>📞</span>
                  <span className="text-sm">+90 552 833 0233</span>
                </p>
                <p className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <span className={isRTL ? 'ml-2' : 'mr-2'}>✉️</span>
                  <span className="text-sm"><EMAIL></span>
                </p>
              </div>
            </div>

            {/* Saudi Arabia Office */}
            <div>
              <h4 className="font-semibold text-purple-400 mb-2">{t('footer.saudi')}</h4>
              <div className="space-y-2 text-gray-300">
                <p className={`flex items-start ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <span className={isRTL ? 'ml-2' : 'mr-2'}>📍</span>
                  <span className="text-sm">Jeddah, Al-Mohammadiyah District, Prince Sultan Street, Building 7163, Office 203</span>
                </p>
                <p className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <span className={isRTL ? 'ml-2' : 'mr-2'}>📞</span>
                  <span className="text-sm">+966 537 774 368</span>
                </p>
                <p className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <span className={isRTL ? 'ml-2' : 'mr-2'}>✉️</span>
                  <span className="text-sm"><EMAIL></span>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Newsletter Section */}
      <div className="bg-gray-800 py-12">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h3 className="text-2xl font-bold mb-4">{t('footer.newsletter.title')}</h3>
            <p className="text-gray-300 mb-8">{t('footer.newsletter.subtitle')}</p>
            <div className={`flex flex-col sm:flex-row gap-4 justify-center items-center ${isRTL ? 'sm:flex-row-reverse' : ''}`}>
              <input
                type="email"
                placeholder={t('footer.newsletter.placeholder')}
                className="px-6 py-3 rounded-full text-gray-900 w-full sm:w-auto sm:min-w-[300px] focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
              <button className="bg-gradient-to-r from-purple-600 to-pink-500 text-white px-8 py-3 rounded-full font-semibold hover:from-purple-700 hover:to-pink-600 transition-all duration-300 transform hover:scale-105">
                {t('footer.newsletter.subscribe')}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Footer */}
      <div className="bg-gray-950 py-6">
        <div className="container mx-auto px-4">
          <div className={`flex flex-col md:flex-row justify-between items-center ${isRTL ? 'md:flex-row-reverse' : ''}`}>
            <div className="text-gray-400 text-sm mb-4 md:mb-0">
              {t('footer.copyright')}
            </div>
            <div className={`flex text-sm ${isRTL ? 'space-x-reverse space-x-6' : 'space-x-6'}`}>
              <Link href="/privacy" className="text-gray-400 hover:text-white transition-colors duration-300">
                {t('footer.privacy')}
              </Link>
              <Link href="/terms" className="text-gray-400 hover:text-white transition-colors duration-300">
                {t('footer.terms')}
              </Link>
              <Link href="/cookies" className="text-gray-400 hover:text-white transition-colors duration-300">
                {t('footer.cookies')}
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
