'use client';

import { useLanguage } from '@/contexts/LanguageContext';
import Link from 'next/link';

export default function SEOBlogPage() {
  const { isRTL } = useLanguage();

  const posts = [
    {
      id: 1,
      title: 'SEO in 2024: What\'s Changed and What Still Works',
      excerpt: 'Comprehensive overview of the latest SEO updates and strategies that continue to drive results.',
      author: 'SEO Expert',
      date: '2024-01-25',
      readTime: '11 min read',
      category: 'SEO Strategy',
      image: '🔍'
    },
    {
      id: 2,
      title: 'Technical SEO Checklist: 50 Points for Better Rankings',
      excerpt: 'Complete technical SEO checklist to ensure your website is optimized for search engines.',
      author: 'Technical SEO Pro',
      date: '2024-01-19',
      readTime: '15 min read',
      category: 'Technical SEO',
      image: '⚙️'
    },
    {
      id: 3,
      title: 'Local SEO: Dominating Google My Business in 2024',
      excerpt: 'Advanced local SEO strategies to improve your local search visibility and attract nearby customers.',
      author: 'Local SEO Specialist',
      date: '2024-01-14',
      readTime: '9 min read',
      category: 'Local SEO',
      image: '📍'
    },
    {
      id: 4,
      title: 'Content SEO: Creating Content That Ranks and Converts',
      excerpt: 'Learn how to create SEO-optimized content that ranks well and drives conversions.',
      author: 'Content SEO Writer',
      date: '2024-01-09',
      readTime: '8 min read',
      category: 'Content SEO',
      image: '📝'
    },
    {
      id: 5,
      title: 'Link Building Strategies That Actually Work in 2024',
      excerpt: 'Effective link building techniques to improve your domain authority and search rankings.',
      author: 'Link Building Expert',
      date: '2024-01-04',
      readTime: '12 min read',
      category: 'Link Building',
      image: '🔗'
    },
    {
      id: 6,
      title: 'SEO Analytics: Measuring What Matters for Rankings',
      excerpt: 'Essential SEO metrics to track and how to use data to improve your search performance.',
      author: 'SEO Analyst',
      date: '2023-12-30',
      readTime: '10 min read',
      category: 'SEO Analytics',
      image: '📊'
    }
  ];

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : ''}`}>
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-green-900 via-teal-800 to-blue-600 text-white py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6">
              <span className="text-3xl">🔍</span>
            </div>
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              SEO Blog
            </h1>
            <p className="text-xl md:text-2xl mb-8 opacity-90">
              Master search engine optimization with expert insights and strategies
            </p>
            <Link
              href="/contact"
              className="inline-block bg-gradient-to-r from-green-500 to-blue-500 text-white font-bold px-8 py-4 rounded-full hover:from-green-400 hover:to-blue-400 transition-all duration-300 transform hover:scale-105"
            >
              Get SEO Help
            </Link>
          </div>
        </div>
      </section>

      {/* Blog Posts Grid */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Latest SEO Articles
              </h2>
              <p className="text-xl text-gray-600">
                Expert insights on search engine optimization and ranking strategies
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {posts.map((post) => (
                <article key={post.id} className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300">
                  <div className="bg-gradient-to-r from-green-100 to-blue-100 p-8 text-center">
                    <div className="text-6xl mb-4">{post.image}</div>
                    <span className="bg-white/80 text-green-600 px-3 py-1 rounded-full text-sm font-medium">
                      {post.category}
                    </span>
                  </div>

                  <div className="p-6">
                    <h3 className="text-xl font-bold text-gray-900 mb-3 line-clamp-2">
                      {post.title}
                    </h3>
                    <p className="text-gray-600 mb-4 line-clamp-3">
                      {post.excerpt}
                    </p>
                    
                    <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                      <span>{post.author}</span>
                      <span>{post.readTime}</span>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500">{post.date}</span>
                      <Link
                        href={`/blog/seo/${post.id}`}
                        className="inline-flex items-center text-green-600 hover:text-green-700 font-medium"
                      >
                        Read More
                        <svg className={`w-4 h-4 ${isRTL ? 'mr-1 rotate-180' : 'ml-1'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                      </Link>
                    </div>
                  </div>
                </article>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* SEO Categories */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                SEO Topics
              </h2>
              <p className="text-xl text-gray-600">
                Explore different aspects of search engine optimization
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[
                { name: 'Technical SEO', icon: '⚙️', count: '15 articles' },
                { name: 'Local SEO', icon: '📍', count: '12 articles' },
                { name: 'Content SEO', icon: '📝', count: '20 articles' },
                { name: 'Link Building', icon: '🔗', count: '10 articles' },
                { name: 'SEO Analytics', icon: '📊', count: '8 articles' },
                { name: 'Mobile SEO', icon: '📱', count: '6 articles' },
                { name: 'E-commerce SEO', icon: '🛒', count: '9 articles' },
                { name: 'SEO Tools', icon: '🛠️', count: '7 articles' }
              ].map((category, index) => (
                <div
                  key={index}
                  className="text-center p-6 bg-gray-50 rounded-lg hover:bg-green-50 transition-colors duration-300"
                >
                  <div className="text-4xl mb-3">{category.icon}</div>
                  <h3 className="text-lg font-bold text-gray-900 mb-2">{category.name}</h3>
                  <p className="text-gray-600 text-sm">{category.count}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-gradient-to-r from-green-600 to-blue-500 text-white py-20">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Ready to Improve Your Search Rankings?
          </h2>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Let our SEO experts help you rank higher and drive more organic traffic to your website.
          </p>
          <Link
            href="/contact"
            className="bg-white text-green-600 font-bold px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 transform hover:scale-105"
          >
            Get SEO Consultation
          </Link>
        </div>
      </section>
    </div>
  );
}
