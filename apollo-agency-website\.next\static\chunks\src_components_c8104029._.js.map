{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/agency/apollo-agency-website/src/components/HeroSection.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { useState, useEffect } from 'react';\n\nconst HeroSection = () => {\n  const [currentSlide, setCurrentSlide] = useState(0);\n\n  const slides = [\n    {\n      title: \"We Create Together Your Marketing Success Formula\",\n      subtitle: \"Our services combine expertise and strategic vision to give you outstanding marketing success that achieves your goals and distinguishes you from competitors.\",\n      cta: \"Contact Us\",\n      background: \"bg-gradient-to-br from-purple-900 via-purple-800 to-pink-600\"\n    },\n    {\n      title: \"Digital Marketing Excellence\",\n      subtitle: \"Transform your business with our comprehensive digital marketing strategies designed to maximize your online presence and drive results.\",\n      cta: \"Get Started\",\n      background: \"bg-gradient-to-br from-blue-900 via-blue-800 to-purple-600\"\n    },\n    {\n      title: \"Brand Development & Strategy\",\n      subtitle: \"Build a powerful brand identity that resonates with your audience and creates lasting connections in the digital landscape.\",\n      cta: \"Learn More\",\n      background: \"bg-gradient-to-br from-indigo-900 via-purple-800 to-pink-600\"\n    }\n  ];\n\n  useEffect(() => {\n    const timer = setInterval(() => {\n      setCurrentSlide((prev) => (prev + 1) % slides.length);\n    }, 5000);\n\n    return () => clearInterval(timer);\n  }, [slides.length]);\n\n  const nextSlide = () => {\n    setCurrentSlide((prev) => (prev + 1) % slides.length);\n  };\n\n  const prevSlide = () => {\n    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length);\n  };\n\n  return (\n    <section className=\"relative min-h-screen flex items-center justify-center overflow-hidden\">\n      {/* Background with animated gradient */}\n      <div className={`absolute inset-0 ${slides[currentSlide].background} transition-all duration-1000`}>\n        {/* Animated background shapes */}\n        <div className=\"absolute top-20 left-10 w-32 h-32 bg-white/10 rounded-full animate-pulse\"></div>\n        <div className=\"absolute top-40 right-20 w-24 h-24 bg-white/5 rounded-full animate-bounce\"></div>\n        <div className=\"absolute bottom-20 left-1/4 w-40 h-40 bg-white/5 rounded-full animate-pulse\"></div>\n        <div className=\"absolute bottom-40 right-1/3 w-20 h-20 bg-white/10 rounded-full animate-bounce\"></div>\n        \n        {/* Floating geometric shapes */}\n        <div className=\"absolute top-1/3 left-1/4 transform rotate-45\">\n          <div className=\"w-16 h-16 bg-gradient-to-r from-yellow-400 to-orange-500 opacity-20 animate-spin-slow\"></div>\n        </div>\n        <div className=\"absolute top-1/2 right-1/4 transform -rotate-12\">\n          <div className=\"w-12 h-12 bg-gradient-to-r from-pink-400 to-purple-500 opacity-30 animate-pulse\"></div>\n        </div>\n      </div>\n\n      {/* Content */}\n      <div className=\"relative z-10 container mx-auto px-4 text-center text-white\">\n        <div className=\"max-w-4xl mx-auto\">\n          {/* Main heading */}\n          <h1 className=\"text-4xl md:text-6xl lg:text-7xl font-bold mb-6 leading-tight\">\n            <span className=\"block opacity-0 animate-fade-in-up\" style={{ animationDelay: '0.2s', animationFillMode: 'forwards' }}>\n              {slides[currentSlide].title.split(' ').slice(0, 3).join(' ')}\n            </span>\n            <span className=\"block opacity-0 animate-fade-in-up\" style={{ animationDelay: '0.4s', animationFillMode: 'forwards' }}>\n              {slides[currentSlide].title.split(' ').slice(3).join(' ')}\n            </span>\n          </h1>\n\n          {/* Subtitle */}\n          <p className=\"text-lg md:text-xl lg:text-2xl mb-8 opacity-90 max-w-3xl mx-auto leading-relaxed opacity-0 animate-fade-in-up\" style={{ animationDelay: '0.6s', animationFillMode: 'forwards' }}>\n            {slides[currentSlide].subtitle}\n          </p>\n\n          {/* CTA Button */}\n          <div className=\"opacity-0 animate-fade-in-up\" style={{ animationDelay: '0.8s', animationFillMode: 'forwards' }}>\n            <Link \n              href=\"/contact\" \n              className=\"inline-block bg-gradient-to-r from-yellow-400 to-orange-500 text-gray-900 font-bold px-8 py-4 rounded-full text-lg hover:from-yellow-300 hover:to-orange-400 transition-all duration-300 transform hover:scale-105 hover:shadow-2xl\"\n            >\n              {slides[currentSlide].cta}\n            </Link>\n          </div>\n\n          {/* Stats or features */}\n          <div className=\"mt-16 grid grid-cols-1 md:grid-cols-3 gap-8 opacity-0 animate-fade-in-up\" style={{ animationDelay: '1s', animationFillMode: 'forwards' }}>\n            <div className=\"text-center\">\n              <div className=\"text-3xl md:text-4xl font-bold mb-2\">500+</div>\n              <div className=\"text-lg opacity-80\">Successful Projects</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-3xl md:text-4xl font-bold mb-2\">200+</div>\n              <div className=\"text-lg opacity-80\">Happy Clients</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-3xl md:text-4xl font-bold mb-2\">5+</div>\n              <div className=\"text-lg opacity-80\">Years Experience</div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Navigation arrows */}\n      <button \n        onClick={prevSlide}\n        className=\"absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/20 hover:bg-white/30 text-white p-3 rounded-full transition-all duration-300 z-20\"\n      >\n        <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 19l-7-7 7-7\" />\n        </svg>\n      </button>\n      \n      <button \n        onClick={nextSlide}\n        className=\"absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/20 hover:bg-white/30 text-white p-3 rounded-full transition-all duration-300 z-20\"\n      >\n        <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n        </svg>\n      </button>\n\n      {/* Slide indicators */}\n      <div className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 flex space-x-3 z-20\">\n        {slides.map((_, index) => (\n          <button\n            key={index}\n            onClick={() => setCurrentSlide(index)}\n            className={`w-3 h-3 rounded-full transition-all duration-300 ${\n              index === currentSlide ? 'bg-white' : 'bg-white/50'\n            }`}\n          />\n        ))}\n      </div>\n\n      {/* Scroll indicator */}\n      <div className=\"absolute bottom-4 left-1/2 transform -translate-x-1/2 animate-bounce\">\n        <svg className=\"w-6 h-6 text-white/70\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 14l-7 7m0 0l-7-7m7 7V3\" />\n        </svg>\n      </div>\n    </section>\n  );\n};\n\nexport default HeroSection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKA,MAAM,cAAc;;IAClB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,SAAS;QACb;YACE,OAAO;YACP,UAAU;YACV,KAAK;YACL,YAAY;QACd;QACA;YACE,OAAO;YACP,UAAU;YACV,KAAK;YACL,YAAY;QACd;QACA;YACE,OAAO;YACP,UAAU;YACV,KAAK;YACL,YAAY;QACd;KACD;IAED,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM,QAAQ;+CAAY;oBACxB;uDAAgB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,OAAO,MAAM;;gBACtD;8CAAG;YAEH;yCAAO,IAAM,cAAc;;QAC7B;gCAAG;QAAC,OAAO,MAAM;KAAC;IAElB,MAAM,YAAY;QAChB,gBAAgB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,OAAO,MAAM;IACtD;IAEA,MAAM,YAAY;QAChB,gBAAgB,CAAC,OAAS,CAAC,OAAO,IAAI,OAAO,MAAM,IAAI,OAAO,MAAM;IACtE;IAEA,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAW,CAAC,iBAAiB,EAAE,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,6BAA6B,CAAC;;kCAEhG,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCAGf,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;;;;;;;;;;kCAEjB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;0BAKnB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAG,WAAU;;8CACZ,6LAAC;oCAAK,WAAU;oCAAqC,OAAO;wCAAE,gBAAgB;wCAAQ,mBAAmB;oCAAW;8CACjH,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC;;;;;;8CAE1D,6LAAC;oCAAK,WAAU;oCAAqC,OAAO;wCAAE,gBAAgB;wCAAQ,mBAAmB;oCAAW;8CACjH,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC,GAAG,IAAI,CAAC;;;;;;;;;;;;sCAKzD,6LAAC;4BAAE,WAAU;4BAAgH,OAAO;gCAAE,gBAAgB;gCAAQ,mBAAmB;4BAAW;sCACzL,MAAM,CAAC,aAAa,CAAC,QAAQ;;;;;;sCAIhC,6LAAC;4BAAI,WAAU;4BAA+B,OAAO;gCAAE,gBAAgB;gCAAQ,mBAAmB;4BAAW;sCAC3G,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CAET,MAAM,CAAC,aAAa,CAAC,GAAG;;;;;;;;;;;sCAK7B,6LAAC;4BAAI,WAAU;4BAA2E,OAAO;gCAAE,gBAAgB;gCAAM,mBAAmB;4BAAW;;8CACrJ,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAsC;;;;;;sDACrD,6LAAC;4CAAI,WAAU;sDAAqB;;;;;;;;;;;;8CAEtC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAsC;;;;;;sDACrD,6LAAC;4CAAI,WAAU;sDAAqB;;;;;;;;;;;;8CAEtC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAsC;;;;;;sDACrD,6LAAC;4CAAI,WAAU;sDAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO5C,6LAAC;gBACC,SAAS;gBACT,WAAU;0BAEV,cAAA,6LAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BACjE,cAAA,6LAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;;;;;;0BAIzE,6LAAC;gBACC,SAAS;gBACT,WAAU;0BAEV,cAAA,6LAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BACjE,cAAA,6LAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;;;;;;0BAKzE,6LAAC;gBAAI,WAAU;0BACZ,OAAO,GAAG,CAAC,CAAC,GAAG,sBACd,6LAAC;wBAEC,SAAS,IAAM,gBAAgB;wBAC/B,WAAW,CAAC,iDAAiD,EAC3D,UAAU,eAAe,aAAa,eACtC;uBAJG;;;;;;;;;;0BAUX,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;oBAAwB,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BAC/E,cAAA,6LAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;AAK/E;GAjJM;KAAA;uCAmJS", "debugId": null}}, {"offset": {"line": 419, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/agency/apollo-agency-website/src/components/ServicesSection.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\n\nconst ServicesSection = () => {\n  const services = [\n    {\n      icon: \"🎯\",\n      title: \"Brand Development\",\n      description: \"We develop unique brands that enhance your digital presence\",\n      link: \"/services/branding\"\n    },\n    {\n      icon: \"✍️\",\n      title: \"Content Creation\",\n      description: \"We write content that aligns with the products and services offered\",\n      link: \"/services/content-creation\"\n    },\n    {\n      icon: \"👥\",\n      title: \"Influencer Marketing\",\n      description: \"We ensure successful marketing through influential people\",\n      link: \"/services/influencer-marketing\"\n    },\n    {\n      icon: \"📱\",\n      title: \"Digital Marketing\",\n      description: \"We launch carefully studied and focused advertising campaigns to achieve your goals accurately\",\n      link: \"/services/digital-marketing\"\n    },\n    {\n      icon: \"💻\",\n      title: \"Website Development\",\n      description: \"We design and develop websites using the latest programming technologies\",\n      link: \"/services/web-development\"\n    },\n    {\n      icon: \"🎨\",\n      title: \"Graphic Design\",\n      description: \"We understand and analyze your needs and provide you with the best designs that serve your business\",\n      link: \"/services/graphic-design\"\n    },\n    {\n      icon: \"🎬\",\n      title: \"Video Production\",\n      description: \"We shoot and create professional advertising videos\",\n      link: \"/services/video-production\"\n    },\n    {\n      icon: \"🔍\",\n      title: \"SEO Services\",\n      description: \"We improve the appearance of websites on search engines\",\n      link: \"/services/seo\"\n    }\n  ];\n\n  return (\n    <section className=\"py-20 bg-gray-50\">\n      <div className=\"container mx-auto px-4\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <div className=\"flex justify-center mb-6\">\n            <div className=\"w-16 h-16 bg-gradient-to-r from-purple-600 to-pink-500 rounded-full flex items-center justify-center\">\n              <span className=\"text-2xl\">🚀</span>\n            </div>\n          </div>\n          <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\n            Everything You Need to Launch Your Business in One Place!\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            With Apollo Agency for Marketing Solutions\n          </p>\n        </div>\n\n        {/* Services Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {services.map((service, index) => (\n            <div\n              key={index}\n              className=\"bg-white rounded-xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 group\"\n            >\n              {/* Icon */}\n              <div className=\"text-center mb-6\">\n                <div className=\"w-20 h-20 bg-gradient-to-r from-purple-100 to-pink-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:from-purple-200 group-hover:to-pink-200 transition-all duration-300\">\n                  <span className=\"text-3xl\">{service.icon}</span>\n                </div>\n              </div>\n\n              {/* Content */}\n              <div className=\"text-center\">\n                <h3 className=\"text-xl font-bold text-gray-900 mb-4 group-hover:text-purple-600 transition-colors duration-300\">\n                  {service.title}\n                </h3>\n                <p className=\"text-gray-600 mb-6 leading-relaxed\">\n                  {service.description}\n                </p>\n                <Link\n                  href={service.link}\n                  className=\"inline-flex items-center text-purple-600 hover:text-purple-700 font-semibold transition-colors duration-300\"\n                >\n                  Learn More\n                  <svg className=\"ml-2 w-4 h-4 transform group-hover:translate-x-1 transition-transform duration-300\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                  </svg>\n                </Link>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* CTA Section */}\n        <div className=\"text-center mt-16\">\n          <Link\n            href=\"/services\"\n            className=\"inline-block bg-gradient-to-r from-purple-600 to-pink-500 text-white font-bold px-8 py-4 rounded-full text-lg hover:from-purple-700 hover:to-pink-600 transition-all duration-300 transform hover:scale-105 hover:shadow-xl\"\n          >\n            View All Services\n          </Link>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default ServicesSection;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIA,MAAM,kBAAkB;IACtB,MAAM,WAAW;QACf;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,MAAM;QACR;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,MAAM;QACR;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,MAAM;QACR;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,MAAM;QACR;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,MAAM;QACR;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,MAAM;QACR;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,MAAM;QACR;QACA;YACE,MAAM;YACN,OAAO;YAC<PERSON>,aAAa;<PERSON><PERSON><PERSON>,MAAM;QACR;KACD;IAED,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAAW;;;;;;;;;;;;;;;;sCAG/B,6LAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,6LAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC;4BAEC,WAAU;;8CAGV,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDAAY,QAAQ,IAAI;;;;;;;;;;;;;;;;8CAK5C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDACX,QAAQ,KAAK;;;;;;sDAEhB,6LAAC;4CAAE,WAAU;sDACV,QAAQ,WAAW;;;;;;sDAEtB,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAM,QAAQ,IAAI;4CAClB,WAAU;;gDACX;8DAEC,6LAAC;oDAAI,WAAU;oDAAqF,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DAC5I,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;2BAxBtE;;;;;;;;;;8BAiCX,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;kCACX;;;;;;;;;;;;;;;;;;;;;;AAOX;KAtHM;uCAwHS", "debugId": null}}]}