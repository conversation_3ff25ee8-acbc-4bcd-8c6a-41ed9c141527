'use client';

import { useLanguage } from '@/contexts/LanguageContext';
import Link from 'next/link';

export default function GraphicDesignPortfolioPage() {
  const { t, isRTL } = useLanguage();

  const projects = [
    {
      id: 1,
      title: 'TechCorp Brand Identity',
      description: 'Complete brand identity design including logo, business cards, and marketing materials',
      deliverables: ['Logo Design', 'Business Cards', 'Letterhead', 'Brand Guidelines'],
      results: ['200% increase in brand recognition', 'Professional brand image', 'Consistent visual identity'],
      image: '🎨',
      category: 'Brand Identity'
    },
    {
      id: 2,
      title: 'Restaurant Menu Design',
      description: 'Modern menu design with food photography and layout optimization',
      deliverables: ['Menu Design', 'Food Photography', 'Table Tents', 'Digital Menu'],
      results: ['30% increase in average order value', 'Improved customer experience', 'Modern brand image'],
      image: '🍽️',
      category: 'Print Design'
    },
    {
      id: 3,
      title: 'E-commerce Product Catalog',
      description: 'Product catalog design for online fashion retailer',
      deliverables: ['Catalog Layout', 'Product Photography', 'Digital Brochure', 'Social Media Graphics'],
      results: ['50% increase in product views', 'Higher conversion rates', 'Professional presentation'],
      image: '📖',
      category: 'Catalog Design'
    },
    {
      id: 4,
      title: 'Healthcare Infographics',
      description: 'Educational infographics for healthcare awareness campaign',
      deliverables: ['Infographic Design', 'Social Media Graphics', 'Poster Design', 'Digital Assets'],
      results: ['1M+ views on social media', 'Increased health awareness', 'Professional credibility'],
      image: '📊',
      category: 'Infographic Design'
    }
  ];

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : ''}`}>
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-orange-900 via-red-800 to-pink-600 text-white py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6">
              <span className="text-3xl">🎨</span>
            </div>
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              {t('portfolio.categories.graphicDesign')} Portfolio
            </h1>
            <p className="text-xl md:text-2xl mb-8 opacity-90">
              Creative designs that communicate your brand message effectively
            </p>
            <Link
              href="/contact"
              className="inline-block bg-gradient-to-r from-yellow-400 to-orange-500 text-gray-900 font-bold px-8 py-4 rounded-full hover:from-yellow-300 hover:to-orange-400 transition-all duration-300 transform hover:scale-105"
            >
              Start Your Design Project
            </Link>
          </div>
        </div>
      </section>

      {/* Projects Grid */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Featured Design Projects
              </h2>
              <p className="text-xl text-gray-600">
                Creative solutions that make an impact
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {projects.map((project) => (
                <div key={project.id} className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300">
                  {/* Project Header */}
                  <div className="bg-gradient-to-r from-orange-100 to-red-100 p-8 text-center">
                    <div className="text-6xl mb-4">{project.image}</div>
                    <span className="bg-white/80 text-orange-600 px-3 py-1 rounded-full text-sm font-medium">
                      {project.category}
                    </span>
                  </div>

                  {/* Project Content */}
                  <div className="p-8">
                    <h3 className="text-2xl font-bold text-gray-900 mb-4">{project.title}</h3>
                    <p className="text-gray-600 mb-4">{project.description}</p>
                    
                    <div className="mb-6">
                      <h4 className="font-semibold text-gray-900 mb-2">Deliverables:</h4>
                      <div className="flex flex-wrap gap-2">
                        {project.deliverables.map((item, index) => (
                          <span key={index} className="bg-orange-100 text-orange-600 px-3 py-1 rounded-full text-sm">
                            {item}
                          </span>
                        ))}
                      </div>
                    </div>

                    <div className="mb-6">
                      <h4 className="font-semibold text-gray-900 mb-3">Results Achieved:</h4>
                      <ul className="space-y-2">
                        {project.results.map((result, index) => (
                          <li key={index} className="flex items-center text-green-600">
                            <span className="mr-2">✓</span>
                            {result}
                          </li>
                        ))}
                      </ul>
                    </div>

                    <Link
                      href={`/case-studies/${project.id}`}
                      className="inline-flex items-center bg-gradient-to-r from-orange-600 to-red-500 text-white font-bold px-6 py-3 rounded-lg hover:from-orange-700 hover:to-red-600 transition-all duration-300"
                    >
                      View Full Case Study
                      <svg className={`w-4 h-4 ${isRTL ? 'mr-2 rotate-180' : 'ml-2'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </Link>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Design Services */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Our Graphic Design Services
              </h2>
              <p className="text-xl text-gray-600">
                Professional design solutions for all your business needs
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                { icon: '🏷️', title: 'Logo Design', description: 'Memorable logos that represent your brand' },
                { icon: '📄', title: 'Print Design', description: 'Brochures, flyers, and marketing materials' },
                { icon: '📱', title: 'Digital Graphics', description: 'Social media graphics and web assets' },
                { icon: '📊', title: 'Infographics', description: 'Data visualization and information design' },
                { icon: '📦', title: 'Packaging Design', description: 'Product packaging that stands out' },
                { icon: '🎯', title: 'Brand Identity', description: 'Complete brand identity systems' }
              ].map((service, index) => (
                <div key={index} className="text-center p-6 rounded-lg hover:bg-gray-50 transition-colors duration-300">
                  <div className="text-4xl mb-4">{service.icon}</div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{service.title}</h3>
                  <p className="text-gray-600">{service.description}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Design Process */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Our Design Process
              </h2>
              <p className="text-xl text-gray-600">
                From concept to completion, we ensure every design meets your goals
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
              {[
                { step: '01', title: 'Discovery', description: 'Understanding your brand and requirements' },
                { step: '02', title: 'Concept', description: 'Creating initial design concepts and ideas' },
                { step: '03', title: 'Design', description: 'Developing the final design with your feedback' },
                { step: '04', title: 'Delivery', description: 'Providing all files and formats you need' }
              ].map((item, index) => (
                <div key={index} className="text-center">
                  <div className="w-16 h-16 bg-gradient-to-r from-orange-600 to-red-500 text-white rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4">
                    {item.step}
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{item.title}</h3>
                  <p className="text-gray-600">{item.description}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-gradient-to-r from-orange-600 to-red-500 text-white py-20">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Ready to Create Amazing Designs?
          </h2>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Let's bring your vision to life with professional graphic design that makes an impact.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/contact"
              className="bg-white text-orange-600 font-bold px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 transform hover:scale-105"
            >
              Start Your Design Project
            </Link>
            <Link
              href="/portfolio"
              className="border-2 border-white text-white font-bold px-8 py-4 rounded-full hover:bg-white hover:text-orange-600 transition-all duration-300"
            >
              View All Projects
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
