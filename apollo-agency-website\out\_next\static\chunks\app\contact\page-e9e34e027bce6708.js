(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[977],{1208:(e,s,a)=>{Promise.resolve().then(a.bind(a,8983))},8983:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>n});var l=a(5155),r=a(9283),t=a(2115);function n(){let{t:e,isRTL:s}=(0,r.o)(),[a,n]=(0,t.useState)({name:"",email:"",phone:"",company:"",service:"",message:""}),i=e=>{n({...a,[e.target.name]:e.target.value})};return(0,l.jsxs)("div",{className:"min-h-screen bg-gray-50 ".concat(s?"rtl":""),children:[(0,l.jsx)("section",{className:"bg-gradient-to-br from-blue-900 via-purple-800 to-pink-600 text-white py-20",children:(0,l.jsx)("div",{className:"container mx-auto px-4",children:(0,l.jsxs)("div",{className:"max-w-4xl mx-auto text-center",children:[(0,l.jsx)("h1",{className:"text-4xl md:text-6xl font-bold mb-6",children:e("header.contact")}),(0,l.jsx)("p",{className:"text-xl md:text-2xl mb-8 opacity-90",children:"Ready to transform your digital presence? Let's start the conversation."})]})})}),(0,l.jsx)("section",{className:"py-20",children:(0,l.jsx)("div",{className:"container mx-auto px-4",children:(0,l.jsx)("div",{className:"max-w-6xl mx-auto",children:(0,l.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12",children:[(0,l.jsxs)("div",{className:"bg-white rounded-xl p-8 shadow-lg",children:[(0,l.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Send Us a Message"}),(0,l.jsxs)("form",{onSubmit:e=>{e.preventDefault(),console.log("Form submitted:",a),alert("Thank you for your message! We will get back to you soon.")},className:"space-y-6",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2",children:"Full Name *"}),(0,l.jsx)("input",{type:"text",id:"name",name:"name",required:!0,value:a.name,onChange:i,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent",placeholder:"Your full name"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address *"}),(0,l.jsx)("input",{type:"email",id:"email",name:"email",required:!0,value:a.email,onChange:i,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent",placeholder:"<EMAIL>"})]})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700 mb-2",children:"Phone Number"}),(0,l.jsx)("input",{type:"tel",id:"phone",name:"phone",value:a.phone,onChange:i,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent",placeholder:"+****************"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"company",className:"block text-sm font-medium text-gray-700 mb-2",children:"Company Name"}),(0,l.jsx)("input",{type:"text",id:"company",name:"company",value:a.company,onChange:i,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent",placeholder:"Your company"})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"service",className:"block text-sm font-medium text-gray-700 mb-2",children:"Service Interested In"}),(0,l.jsxs)("select",{id:"service",name:"service",value:a.service,onChange:i,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent",children:[(0,l.jsx)("option",{value:"",children:"Select a service"}),(0,l.jsx)("option",{value:"digital-marketing",children:"Digital Marketing"}),(0,l.jsx)("option",{value:"seo",children:"SEO Services"}),(0,l.jsx)("option",{value:"branding",children:"Brand Development"}),(0,l.jsx)("option",{value:"social-media",children:"Social Media Management"}),(0,l.jsx)("option",{value:"web-development",children:"Website Development"}),(0,l.jsx)("option",{value:"content-creation",children:"Content Creation"})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"message",className:"block text-sm font-medium text-gray-700 mb-2",children:"Message *"}),(0,l.jsx)("textarea",{id:"message",name:"message",required:!0,rows:6,value:a.message,onChange:i,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent",placeholder:"Tell us about your project and goals..."})]}),(0,l.jsx)("button",{type:"submit",className:"w-full bg-gradient-to-r from-purple-600 to-pink-500 text-white font-bold py-4 px-6 rounded-lg hover:from-purple-700 hover:to-pink-600 transition-all duration-300 transform hover:scale-105",children:"Send Message"})]})]}),(0,l.jsxs)("div",{className:"space-y-8",children:[(0,l.jsxs)("div",{className:"bg-white rounded-xl p-8 shadow-lg",children:[(0,l.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Get In Touch"}),(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,l.jsx)("div",{className:"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center flex-shrink-0",children:(0,l.jsx)("span",{className:"text-xl",children:"\uD83D\uDCDE"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"font-semibold text-gray-900",children:"Phone"}),(0,l.jsx)("p",{className:"text-gray-600",children:"+90 ************"}),(0,l.jsx)("p",{className:"text-gray-600",children:"+966 537 774 368"})]})]}),(0,l.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,l.jsx)("div",{className:"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center flex-shrink-0",children:(0,l.jsx)("span",{className:"text-xl",children:"✉️"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"font-semibold text-gray-900",children:"Email"}),(0,l.jsx)("p",{className:"text-gray-600",children:"<EMAIL>"}),(0,l.jsx)("p",{className:"text-gray-600",children:"<EMAIL>"})]})]}),(0,l.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,l.jsx)("div",{className:"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center flex-shrink-0",children:(0,l.jsx)("span",{className:"text-xl",children:"\uD83D\uDCAC"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"font-semibold text-gray-900",children:"WhatsApp"}),(0,l.jsx)("p",{className:"text-gray-600",children:"Quick support available"}),(0,l.jsx)("a",{href:"https://wa.me/905528330233",className:"text-purple-600 hover:text-purple-700 font-medium",children:"Chat with us"})]})]})]})]}),(0,l.jsxs)("div",{className:"bg-white rounded-xl p-8 shadow-lg",children:[(0,l.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Our Offices"}),(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"font-semibold text-purple-600 mb-2",children:"Turkey Office"}),(0,l.jsx)("p",{className:"text-gray-600 text-sm",children:"\xdcniversite Mah. E-5 Yan Yol \xdczeri, \xc7ınar Sk. No:1 D:4, 34320 Avcılar/İstanbul"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"font-semibold text-purple-600 mb-2",children:"Saudi Arabia Office"}),(0,l.jsx)("p",{className:"text-gray-600 text-sm",children:"Jeddah, Al-Mohammadiyah District, Prince Sultan Street, Building 7163, Office 203"})]})]})]})]})]})})})})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[283,441,684,358],()=>s(1208)),_N_E=e.O()}]);