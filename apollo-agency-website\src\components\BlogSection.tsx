'use client';

import Link from 'next/link';

const BlogSection = () => {
  const blogPosts = [
    {
      id: 1,
      title: "How Your Business Appears in Google's First Search Results",
      excerpt: "Learn the essential strategies to improve your website's visibility and rank higher in Google search results.",
      category: "SEO",
      date: "June 15, 2024",
      readTime: "5 min read",
      image: "🔍",
      link: "/blog/google-first-results"
    },
    {
      id: 2,
      title: "Advertising and Marketing Office in Saudi Arabia",
      excerpt: "Discover the best practices for establishing a successful marketing presence in the Saudi Arabian market.",
      category: "Marketing",
      date: "June 10, 2024",
      readTime: "7 min read",
      image: "🏢",
      link: "/blog/marketing-office-saudi"
    },
    {
      id: 3,
      title: "Search Engine Optimization Services",
      excerpt: "Comprehensive guide to SEO services and how they can transform your online presence and drive organic traffic.",
      category: "SEO",
      date: "June 5, 2024",
      readTime: "6 min read",
      image: "📈",
      link: "/blog/seo-services"
    },
    {
      id: 4,
      title: "Advertising Print Design",
      excerpt: "Master the art of creating compelling print advertisements that capture attention and drive results.",
      category: "Design",
      date: "May 30, 2024",
      readTime: "4 min read",
      image: "🎨",
      link: "/blog/print-design"
    },
    {
      id: 5,
      title: "Key Elements in Logo Creation",
      excerpt: "Understand the fundamental principles of professional logo design and brand identity development.",
      category: "Branding",
      date: "May 25, 2024",
      readTime: "5 min read",
      image: "🎯",
      link: "/blog/logo-creation"
    },
    {
      id: 6,
      title: "Understanding B2C vs B2B Marketing",
      excerpt: "Explore the key differences between B2C and B2B marketing strategies and when to use each approach.",
      category: "Strategy",
      date: "May 20, 2024",
      readTime: "8 min read",
      image: "📊",
      link: "/blog/b2c-vs-b2b"
    }
  ];

  const categories = ["All", "SEO", "Marketing", "Design", "Branding", "Strategy"];

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="flex justify-center mb-6">
            <div className="w-16 h-16 bg-gradient-to-r from-green-600 to-blue-500 rounded-full flex items-center justify-center">
              <span className="text-2xl">📚</span>
            </div>
          </div>
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Our Expertise at Your Fingertips
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Stay updated with the latest insights, tips, and trends in digital marketing
          </p>
        </div>

        {/* Category Filter */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {categories.map((category) => (
            <button
              key={category}
              className="px-6 py-2 rounded-full border-2 border-gray-200 text-gray-600 hover:border-purple-500 hover:text-purple-600 transition-all duration-300 font-medium"
            >
              {category}
            </button>
          ))}
        </div>

        {/* Blog Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {blogPosts.map((post) => (
            <article
              key={post.id}
              className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 overflow-hidden group"
            >
              {/* Image/Icon */}
              <div className="h-48 bg-gradient-to-br from-purple-100 to-pink-100 flex items-center justify-center relative overflow-hidden">
                <div className="text-6xl group-hover:scale-110 transition-transform duration-300">
                  {post.image}
                </div>
                {/* Category Badge */}
                <div className="absolute top-4 left-4">
                  <span className="bg-white/90 backdrop-blur-sm text-gray-800 px-3 py-1 rounded-full text-sm font-medium">
                    {post.category}
                  </span>
                </div>
              </div>

              {/* Content */}
              <div className="p-6">
                {/* Meta Info */}
                <div className="flex items-center text-sm text-gray-500 mb-3">
                  <span>{post.date}</span>
                  <span className="mx-2">•</span>
                  <span>{post.readTime}</span>
                </div>

                {/* Title */}
                <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-purple-600 transition-colors duration-300 line-clamp-2">
                  {post.title}
                </h3>

                {/* Excerpt */}
                <p className="text-gray-600 mb-4 line-clamp-3">
                  {post.excerpt}
                </p>

                {/* Read More Link */}
                <Link
                  href={post.link}
                  className="inline-flex items-center text-purple-600 hover:text-purple-700 font-semibold transition-colors duration-300"
                >
                  Read More
                  <svg className="ml-2 w-4 h-4 transform group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </Link>

                {/* Social Share */}
                <div className="flex items-center justify-between mt-4 pt-4 border-t border-gray-100">
                  <span className="text-sm text-gray-500">Share:</span>
                  <div className="flex space-x-2">
                    <button className="w-8 h-8 bg-gray-100 hover:bg-blue-100 rounded-full flex items-center justify-center transition-colors duration-300">
                      <span className="text-sm">📘</span>
                    </button>
                    <button className="w-8 h-8 bg-gray-100 hover:bg-blue-100 rounded-full flex items-center justify-center transition-colors duration-300">
                      <span className="text-sm">🐦</span>
                    </button>
                    <button className="w-8 h-8 bg-gray-100 hover:bg-blue-100 rounded-full flex items-center justify-center transition-colors duration-300">
                      <span className="text-sm">💼</span>
                    </button>
                  </div>
                </div>
              </div>
            </article>
          ))}
        </div>

        {/* CTA Section */}
        <div className="text-center mt-16">
          <Link
            href="/blog"
            className="inline-block bg-gradient-to-r from-green-600 to-blue-500 text-white font-bold px-8 py-4 rounded-full text-lg hover:from-green-700 hover:to-blue-600 transition-all duration-300 transform hover:scale-105 hover:shadow-xl"
          >
            Read More Articles
          </Link>
        </div>
      </div>
    </section>
  );
};

export default BlogSection;
