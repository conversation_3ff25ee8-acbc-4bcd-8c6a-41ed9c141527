{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/ui/storybook/with-shadow-portal.tsx"], "sourcesContent": ["import { Base } from '../styles/base'\nimport { Colors } from '../styles/colors'\nimport { CssReset } from '../styles/css-reset'\nimport { ComponentStyles } from '../styles/component-styles'\nimport { ShadowPortal } from '../components/shadow-portal'\nimport { DarkTheme } from '../styles/dark-theme'\n\nexport const withShadowPortal = (Story: any) => (\n  <ShadowPortal>\n    <CssReset />\n    <Base />\n    <Colors />\n    <ComponentStyles />\n    <DarkTheme />\n    <Story />\n  </ShadowPortal>\n)\n"], "names": ["withShadowPortal", "Story", "ShadowPort<PERSON>", "CssReset", "Base", "Colors", "ComponentStyles", "DarkTheme"], "mappings": ";;;;+BAOaA;;;eAAAA;;;;sBAPQ;wBACE;0BACE;iCACO;8BACH;2BACH;AAEnB,MAAMA,mBAAmB,CAACC,sBAC/B,sBAACC,0BAAY;;0BACX,qBAACC,kBAAQ;0BACT,qBAACC,UAAI;0BACL,qBAACC,cAAM;0BACP,qBAACC,gCAAe;0BAChB,qBAACC,oBAAS;0BACV,qBAACN"}