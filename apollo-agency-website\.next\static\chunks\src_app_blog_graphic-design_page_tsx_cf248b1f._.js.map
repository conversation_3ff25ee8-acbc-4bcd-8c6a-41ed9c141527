{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/agency/apollo-agency-website/src/app/blog/graphic-design/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useLanguage } from '@/contexts/LanguageContext';\nimport Link from 'next/link';\n\nexport default function GraphicDesignBlogPage() {\n  const { t, isRTL } = useLanguage();\n\n  const posts = [\n    {\n      id: 1,\n      title: 'Logo Design Trends That Will Define 2024',\n      excerpt: 'Explore the latest logo design trends and learn how to create timeless brand identities.',\n      author: 'Creative Team',\n      date: '2024-01-24',\n      readTime: '8 min read',\n      category: 'Logo Design',\n      image: '🎨'\n    },\n    {\n      id: 2,\n      title: 'Color Psychology in Graphic Design: A Complete Guide',\n      excerpt: 'Understanding how colors affect emotions and behavior to create more effective designs.',\n      author: 'Design Expert',\n      date: '2024-01-18',\n      readTime: '10 min read',\n      category: 'Color Theory',\n      image: '🌈'\n    },\n    {\n      id: 3,\n      title: 'Typography Best Practices for Digital and Print',\n      excerpt: 'Master typography principles to create readable and visually appealing designs.',\n      author: 'Typography Pro',\n      date: '2024-01-13',\n      readTime: '7 min read',\n      category: 'Typography',\n      image: '📝'\n    },\n    {\n      id: 4,\n      title: 'Creating Effective Infographics That Tell a Story',\n      excerpt: 'Learn how to design infographics that communicate complex information clearly and engagingly.',\n      author: 'Data Viz Expert',\n      date: '2024-01-08',\n      readTime: '9 min read',\n      category: 'Infographics',\n      image: '📊'\n    },\n    {\n      id: 5,\n      title: 'Brand Identity Design: From Concept to Completion',\n      excerpt: 'Step-by-step process for creating comprehensive brand identity systems.',\n      author: 'Brand Designer',\n      date: '2024-01-03',\n      readTime: '12 min read',\n      category: 'Brand Identity',\n      image: '🏷️'\n    },\n    {\n      id: 6,\n      title: 'Packaging Design That Sells: Psychology and Principles',\n      excerpt: 'How to create packaging designs that attract customers and drive sales.',\n      author: 'Package Designer',\n      date: '2023-12-29',\n      readTime: '8 min read',\n      category: 'Packaging',\n      image: '📦'\n    }\n  ];\n\n  return (\n    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : ''}`}>\n      {/* Hero Section */}\n      <section className=\"bg-gradient-to-br from-orange-900 via-red-800 to-pink-600 text-white py-20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-4xl mx-auto text-center\">\n            <div className=\"w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6\">\n              <span className=\"text-3xl\">🎨</span>\n            </div>\n            <h1 className=\"text-4xl md:text-6xl font-bold mb-6\">\n              Graphic Design Blog\n            </h1>\n            <p className=\"text-xl md:text-2xl mb-8 opacity-90\">\n              Creative insights and design principles for visual excellence\n            </p>\n            <Link\n              href=\"/contact\"\n              className=\"inline-block bg-gradient-to-r from-yellow-400 to-orange-500 text-gray-900 font-bold px-8 py-4 rounded-full hover:from-yellow-300 hover:to-orange-400 transition-all duration-300 transform hover:scale-105\"\n            >\n              Get Design Help\n            </Link>\n          </div>\n        </div>\n      </section>\n\n      {/* Blog Posts Grid */}\n      <section className=\"py-20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-6xl mx-auto\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-6\">\n                Latest Graphic Design Articles\n              </h2>\n              <p className=\"text-xl text-gray-600\">\n                Expert insights on design trends and creative techniques\n              </p>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n              {posts.map((post) => (\n                <article key={post.id} className=\"bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300\">\n                  <div className=\"bg-gradient-to-r from-orange-100 to-red-100 p-8 text-center\">\n                    <div className=\"text-6xl mb-4\">{post.image}</div>\n                    <span className=\"bg-white/80 text-orange-600 px-3 py-1 rounded-full text-sm font-medium\">\n                      {post.category}\n                    </span>\n                  </div>\n\n                  <div className=\"p-6\">\n                    <h3 className=\"text-xl font-bold text-gray-900 mb-3 line-clamp-2\">\n                      {post.title}\n                    </h3>\n                    <p className=\"text-gray-600 mb-4 line-clamp-3\">\n                      {post.excerpt}\n                    </p>\n                    \n                    <div className=\"flex items-center justify-between text-sm text-gray-500 mb-4\">\n                      <span>{post.author}</span>\n                      <span>{post.readTime}</span>\n                    </div>\n                    \n                    <div className=\"flex items-center justify-between\">\n                      <span className=\"text-sm text-gray-500\">{post.date}</span>\n                      <Link\n                        href={`/blog/graphic-design/${post.id}`}\n                        className=\"inline-flex items-center text-orange-600 hover:text-orange-700 font-medium\"\n                      >\n                        Read More\n                        <svg className={`w-4 h-4 ${isRTL ? 'mr-1 rotate-180' : 'ml-1'}`} fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                        </svg>\n                      </Link>\n                    </div>\n                  </div>\n                </article>\n              ))}\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"bg-gradient-to-r from-orange-600 to-red-500 text-white py-20\">\n        <div className=\"container mx-auto px-4 text-center\">\n          <h2 className=\"text-3xl md:text-4xl font-bold mb-6\">\n            Need Professional Graphic Design?\n          </h2>\n          <p className=\"text-xl mb-8 opacity-90 max-w-2xl mx-auto\">\n            Let our creative team bring your vision to life with stunning graphic design.\n          </p>\n          <Link\n            href=\"/contact\"\n            className=\"bg-white text-orange-600 font-bold px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 transform hover:scale-105\"\n          >\n            Start Design Project\n          </Link>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;IAE/B,MAAM,QAAQ;QACZ;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,QAAQ;YACR,MAAM;YACN,UAAU;YACV,UAAU;YACV,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,QAAQ;YACR,MAAM;YACN,UAAU;YACV,UAAU;YACV,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,QAAQ;YACR,MAAM;YACN,UAAU;YACV,UAAU;YACV,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,QAAQ;YACR,MAAM;YACN,UAAU;YACV,UAAU;YACV,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,QAAQ;YACR,MAAM;YACN,UAAU;YACV,UAAU;YACV,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,QAAQ;YACR,MAAM;YACN,UAAU;YACV,UAAU;YACV,OAAO;QACT;KACD;IAED,qBACE,6LAAC;QAAI,WAAW,CAAC,wBAAwB,EAAE,QAAQ,QAAQ,IAAI;;0BAE7D,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAAW;;;;;;;;;;;0CAE7B,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAGpD,6LAAC;gCAAE,WAAU;0CAAsC;;;;;;0CAGnD,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;0BAQP,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAGlE,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAKvC,6LAAC;gCAAI,WAAU;0CACZ,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC;wCAAsB,WAAU;;0DAC/B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAiB,KAAK,KAAK;;;;;;kEAC1C,6LAAC;wDAAK,WAAU;kEACb,KAAK,QAAQ;;;;;;;;;;;;0DAIlB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEACX,KAAK,KAAK;;;;;;kEAEb,6LAAC;wDAAE,WAAU;kEACV,KAAK,OAAO;;;;;;kEAGf,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EAAM,KAAK,MAAM;;;;;;0EAClB,6LAAC;0EAAM,KAAK,QAAQ;;;;;;;;;;;;kEAGtB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAyB,KAAK,IAAI;;;;;;0EAClD,6LAAC,+JAAA,CAAA,UAAI;gEACH,MAAM,CAAC,qBAAqB,EAAE,KAAK,EAAE,EAAE;gEACvC,WAAU;;oEACX;kFAEC,6LAAC;wEAAI,WAAW,CAAC,QAAQ,EAAE,QAAQ,oBAAoB,QAAQ;wEAAE,MAAK;wEAAO,QAAO;wEAAe,SAAQ;kFACzG,cAAA,6LAAC;4EAAK,eAAc;4EAAQ,gBAAe;4EAAQ,aAAa;4EAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uCA7BjE,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;0BA0C/B,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAsC;;;;;;sCAGpD,6LAAC;4BAAE,WAAU;sCAA4C;;;;;;sCAGzD,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAOX;GAtKwB;;QACD,sIAAA,CAAA,cAAW;;;KADV", "debugId": null}}]}