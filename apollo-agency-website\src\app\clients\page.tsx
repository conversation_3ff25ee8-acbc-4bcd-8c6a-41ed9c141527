'use client';

import { useLanguage } from '@/contexts/LanguageContext';
import Link from 'next/link';

export default function ClientsPage() {
  const { t, isRTL } = useLanguage();

  const clients = [
    { name: 'TechCorp', logo: '🏢', industry: 'Technology' },
    { name: 'InnovateLab', logo: '🔬', industry: 'Research' },
    { name: 'DesignStudio', logo: '🎨', industry: 'Creative' },
    { name: 'StartupHub', logo: '🚀', industry: 'Startup' },
    { name: 'MediaGroup', logo: '📺', industry: 'Media' },
    { name: 'RetailChain', logo: '🛍️', industry: 'Retail' },
    { name: 'FinanceFirst', logo: '💰', industry: 'Finance' },
    { name: 'HealthPlus', logo: '🏥', industry: 'Healthcare' },
    { name: 'EduTech', logo: '📚', industry: 'Education' },
    { name: 'FoodieApp', logo: '🍕', industry: 'Food & Beverage' },
    { name: 'TravelCo', logo: '✈️', industry: 'Travel' },
    { name: 'FitnessPro', logo: '💪', industry: 'Fitness' }
  ];

  const testimonials = [
    {
      name: '<PERSON>',
      company: 'TechCorp',
      role: 'Marketing Director',
      content: 'Serv Infinity transformed our digital presence completely. Their strategic approach and creative solutions helped us increase our online engagement by 300%.',
      rating: 5
    },
    {
      name: 'Ahmed Al-Rashid',
      company: 'InnovateLab',
      role: 'CEO',
      content: 'Working with Serv Infinity was a game-changer for our startup. Their comprehensive marketing strategy helped us reach our target audience effectively.',
      rating: 5
    },
    {
      name: 'Maria Rodriguez',
      company: 'DesignStudio',
      role: 'Creative Director',
      content: 'The team at Serv Infinity understands our vision and consistently delivers exceptional results. Their attention to detail is remarkable.',
      rating: 5
    },
    {
      name: 'David Chen',
      company: 'StartupHub',
      role: 'Founder',
      content: 'Serv Infinity helped us scale our business from a small startup to a recognized brand in our industry. Their expertise is unmatched.',
      rating: 5
    }
  ];

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : ''}`}>
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-900 via-indigo-800 to-purple-600 text-white py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              {t('header.clients')}
            </h1>
            <p className="text-xl md:text-2xl mb-8 opacity-90">
              {t('clients.subtitle')}
            </p>
          </div>
        </div>
      </section>

      {/* Client Logos Grid */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Trusted by Leading Companies
            </h2>
            <p className="text-xl text-gray-600">
              We're proud to work with amazing clients across various industries
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8">
            {clients.map((client, index) => (
              <div
                key={index}
                className="bg-gray-50 rounded-lg p-6 hover:bg-white hover:shadow-lg transition-all duration-300 flex flex-col items-center justify-center h-32 group"
              >
                <div className="text-4xl mb-2 group-hover:scale-110 transition-transform duration-300">
                  {client.logo}
                </div>
                <span className="text-sm font-medium text-gray-800 text-center">{client.name}</span>
                <span className="text-xs text-gray-500 text-center">{client.industry}</span>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              {t('clients.testimonials.title')}
            </h2>
            <p className="text-xl text-gray-600">
              {t('clients.testimonials.subtitle')}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {testimonials.map((testimonial, index) => (
              <div key={index} className="bg-white rounded-xl p-8 shadow-lg hover:shadow-xl transition-all duration-300">
                {/* Quote Icon */}
                <div className="text-4xl text-purple-200 mb-4">❝</div>
                
                {/* Testimonial Content */}
                <blockquote className="text-lg text-gray-700 mb-6 leading-relaxed">
                  "{testimonial.content}"
                </blockquote>

                {/* Rating */}
                <div className="flex mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <svg key={i} className="w-5 h-5 text-yellow-400 fill-current" viewBox="0 0 24 24">
                      <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>
                  ))}
                </div>

                {/* Client Info */}
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-gradient-to-r from-purple-100 to-pink-100 rounded-full flex items-center justify-center mr-4">
                    <span className="text-xl">👤</span>
                  </div>
                  <div>
                    <h4 className="font-bold text-gray-900">{testimonial.name}</h4>
                    <p className="text-purple-600 font-medium">{testimonial.role}</p>
                    <p className="text-gray-500 text-sm">{testimonial.company}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Our Impact in Numbers
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="text-4xl md:text-5xl font-bold text-purple-600 mb-2">500+</div>
              <div className="text-gray-600">{t('clients.stats.projects')}</div>
            </div>
            <div className="text-center">
              <div className="text-4xl md:text-5xl font-bold text-purple-600 mb-2">200+</div>
              <div className="text-gray-600">{t('clients.stats.clients')}</div>
            </div>
            <div className="text-center">
              <div className="text-4xl md:text-5xl font-bold text-purple-600 mb-2">5+</div>
              <div className="text-gray-600">{t('clients.stats.experience')}</div>
            </div>
            <div className="text-center">
              <div className="text-4xl md:text-5xl font-bold text-purple-600 mb-2">98%</div>
              <div className="text-gray-600">{t('clients.stats.satisfaction')}</div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-gradient-to-r from-blue-600 to-purple-500 text-white py-20">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Ready to Join Our Success Stories?
          </h2>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Let's work together to achieve your marketing goals and create your own success story.
          </p>
          <Link
            href="/contact"
            className="bg-white text-blue-600 font-bold px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 transform hover:scale-105"
          >
            Start Your Success Story
          </Link>
        </div>
      </section>
    </div>
  );
}
