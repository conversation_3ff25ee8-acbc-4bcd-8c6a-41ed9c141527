'use client';

import { useLanguage } from '@/contexts/LanguageContext';
import Link from 'next/link';

export default function BrandIdentityPortfolioPage() {
  const { t, isRTL } = useLanguage();

  const projects = [
    {
      id: 1,
      title: 'TechCorp Complete Rebrand',
      description: 'Full brand identity redesign for technology company',
      deliverables: ['Logo Design', 'Brand Guidelines', 'Color Palette', 'Typography', 'Business Cards', 'Letterhead'],
      results: ['300% increase in brand recognition', 'Improved market positioning', 'Consistent brand experience'],
      image: '🎯',
      category: 'Complete Rebrand'
    },
    {
      id: 2,
      title: 'StartupHub Brand Launch',
      description: 'Brand identity creation for new startup accelerator',
      deliverables: ['Logo Design', 'Brand Strategy', 'Visual Identity', 'Marketing Materials', 'Website Design'],
      results: ['Strong market entry', 'Professional credibility', 'Investor confidence'],
      image: '🚀',
      category: 'Brand Launch'
    },
    {
      id: 3,
      title: 'HealthPlus Medical Brand',
      description: 'Healthcare brand identity with trust and professionalism focus',
      deliverables: ['Medical Logo', 'Patient Materials', 'Signage Design', 'Digital Assets'],
      results: ['Increased patient trust', 'Professional appearance', 'Clear communication'],
      image: '🏥',
      category: 'Healthcare Branding'
    },
    {
      id: 4,
      title: 'FoodieApp Restaurant Brand',
      description: 'Modern restaurant brand identity with appetite appeal',
      deliverables: ['Restaurant Logo', 'Menu Design', 'Packaging', 'Interior Graphics'],
      results: ['200% increase in customers', 'Premium brand perception', 'Social media buzz'],
      image: '🍕',
      category: 'Restaurant Branding'
    }
  ];

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : ''}`}>
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-indigo-900 via-purple-800 to-pink-600 text-white py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6">
              <span className="text-3xl">🎯</span>
            </div>
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              {t('portfolio.categories.brandIdentity')} Portfolio
            </h1>
            <p className="text-xl md:text-2xl mb-8 opacity-90">
              Creating memorable brand identities that connect with your audience
            </p>
            <Link
              href="/contact"
              className="inline-block bg-gradient-to-r from-purple-500 to-pink-500 text-white font-bold px-8 py-4 rounded-full hover:from-purple-400 hover:to-pink-400 transition-all duration-300 transform hover:scale-105"
            >
              Build Your Brand Identity
            </Link>
          </div>
        </div>
      </section>

      {/* Projects Grid */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Featured Brand Identity Projects
              </h2>
              <p className="text-xl text-gray-600">
                Brands we've helped establish and grow
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {projects.map((project) => (
                <div key={project.id} className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300">
                  {/* Project Header */}
                  <div className="bg-gradient-to-r from-indigo-100 to-purple-100 p-8 text-center">
                    <div className="text-6xl mb-4">{project.image}</div>
                    <span className="bg-white/80 text-indigo-600 px-3 py-1 rounded-full text-sm font-medium">
                      {project.category}
                    </span>
                  </div>

                  {/* Project Content */}
                  <div className="p-8">
                    <h3 className="text-2xl font-bold text-gray-900 mb-4">{project.title}</h3>
                    <p className="text-gray-600 mb-4">{project.description}</p>
                    
                    <div className="mb-6">
                      <h4 className="font-semibold text-gray-900 mb-2">Deliverables:</h4>
                      <div className="flex flex-wrap gap-2">
                        {project.deliverables.map((item, index) => (
                          <span key={index} className="bg-indigo-100 text-indigo-600 px-3 py-1 rounded-full text-sm">
                            {item}
                          </span>
                        ))}
                      </div>
                    </div>

                    <div className="mb-6">
                      <h4 className="font-semibold text-gray-900 mb-3">Results Achieved:</h4>
                      <ul className="space-y-2">
                        {project.results.map((result, index) => (
                          <li key={index} className="flex items-center text-green-600">
                            <span className="mr-2">✓</span>
                            {result}
                          </li>
                        ))}
                      </ul>
                    </div>

                    <Link
                      href={`/case-studies/${project.id}`}
                      className="inline-flex items-center bg-gradient-to-r from-indigo-600 to-purple-500 text-white font-bold px-6 py-3 rounded-lg hover:from-indigo-700 hover:to-purple-600 transition-all duration-300"
                    >
                      View Full Case Study
                      <svg className={`w-4 h-4 ${isRTL ? 'mr-2 rotate-180' : 'ml-2'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </Link>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Brand Identity Services */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Our Brand Identity Services
              </h2>
              <p className="text-xl text-gray-600">
                Complete brand identity solutions from concept to implementation
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                { icon: '🎨', title: 'Logo Design', description: 'Unique logos that represent your brand essence' },
                { icon: '📋', title: 'Brand Guidelines', description: 'Comprehensive brand usage guidelines' },
                { icon: '🎨', title: 'Visual Identity', description: 'Color palettes, typography, and visual elements' },
                { icon: '📄', title: 'Stationery Design', description: 'Business cards, letterheads, and documents' },
                { icon: '📦', title: 'Packaging Design', description: 'Product packaging that reflects your brand' },
                { icon: '🌐', title: 'Digital Assets', description: 'Web graphics and social media templates' }
              ].map((service, index) => (
                <div key={index} className="text-center p-6 rounded-lg hover:bg-gray-50 transition-colors duration-300">
                  <div className="text-4xl mb-4">{service.icon}</div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{service.title}</h3>
                  <p className="text-gray-600">{service.description}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Brand Development Process */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Our Brand Development Process
              </h2>
              <p className="text-xl text-gray-600">
                Strategic approach to building powerful brand identities
              </p>
            </div>
            
            <div className="space-y-8">
              {[
                { step: '01', title: 'Brand Discovery', description: 'Understanding your business, values, and target audience' },
                { step: '02', title: 'Strategy Development', description: 'Creating brand positioning and messaging strategy' },
                { step: '03', title: 'Visual Identity', description: 'Designing logo, colors, typography, and visual elements' },
                { step: '04', title: 'Brand Guidelines', description: 'Creating comprehensive brand usage guidelines' },
                { step: '05', title: 'Implementation', description: 'Applying brand across all touchpoints and materials' }
              ].map((item, index) => (
                <div key={index} className="flex items-start space-x-6">
                  <div className="w-16 h-16 bg-gradient-to-r from-indigo-600 to-purple-500 text-white rounded-full flex items-center justify-center text-xl font-bold flex-shrink-0">
                    {item.step}
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-3">{item.title}</h3>
                    <p className="text-gray-600 text-lg">{item.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-gradient-to-r from-indigo-600 to-purple-500 text-white py-20">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Ready to Build a Powerful Brand Identity?
          </h2>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Let's create a brand identity that sets you apart from the competition and connects with your audience.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/contact"
              className="bg-white text-indigo-600 font-bold px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 transform hover:scale-105"
            >
              Start Your Brand Journey
            </Link>
            <Link
              href="/portfolio"
              className="border-2 border-white text-white font-bold px-8 py-4 rounded-full hover:bg-white hover:text-indigo-600 transition-all duration-300"
            >
              View All Projects
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
