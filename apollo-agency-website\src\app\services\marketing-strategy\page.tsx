'use client';

import { useLanguage } from '@/contexts/LanguageContext';
import Link from 'next/link';

export default function MarketingStrategyPage() {
  const { t, isRTL } = useLanguage();

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : ''}`}>
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-purple-900 via-indigo-800 to-blue-600 text-white py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6">
              <span className="text-3xl">📊</span>
            </div>
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              {t('services.marketingStrategy')}
            </h1>
            <p className="text-xl md:text-2xl mb-8 opacity-90">
              {t('services.marketingStrategy.subtitle')}
            </p>
            <Link
              href="/contact"
              className="inline-block bg-gradient-to-r from-purple-500 to-blue-500 text-white font-bold px-8 py-4 rounded-full hover:from-purple-400 hover:to-blue-400 transition-all duration-300 transform hover:scale-105"
            >
              {t('services.marketingStrategy.cta')}
            </Link>
          </div>
        </div>
      </section>

      {/* Strategy Services */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                {t('services.marketingStrategy.servicesTitle')}
              </h2>
              <p className="text-xl text-gray-600">
                {t('services.marketingStrategy.servicesSubtitle')}
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  icon: '🎯',
                  title: t('services.marketingStrategy.services.analysis.title'),
                  description: t('services.marketingStrategy.services.analysis.description'),
                  features: [
                    t('services.marketingStrategy.services.analysis.feature1'),
                    t('services.marketingStrategy.services.analysis.feature2'),
                    t('services.marketingStrategy.services.analysis.feature3'),
                    t('services.marketingStrategy.services.analysis.feature4')
                  ]
                },
                {
                  icon: '📈',
                  title: t('services.marketingStrategy.services.planning.title'),
                  description: t('services.marketingStrategy.services.planning.description'),
                  features: [
                    t('services.marketingStrategy.services.planning.feature1'),
                    t('services.marketingStrategy.services.planning.feature2'),
                    t('services.marketingStrategy.services.planning.feature3'),
                    t('services.marketingStrategy.services.planning.feature4')
                  ]
                },
                {
                  icon: '🚀',
                  title: t('services.marketingStrategy.services.execution.title'),
                  description: t('services.marketingStrategy.services.execution.description'),
                  features: [
                    t('services.marketingStrategy.services.execution.feature1'),
                    t('services.marketingStrategy.services.execution.feature2'),
                    t('services.marketingStrategy.services.execution.feature3'),
                    t('services.marketingStrategy.services.execution.feature4')
                  ]
                },
                {
                  icon: '📊',
                  title: t('services.marketingStrategy.services.optimization.title'),
                  description: t('services.marketingStrategy.services.optimization.description'),
                  features: [
                    t('services.marketingStrategy.services.optimization.feature1'),
                    t('services.marketingStrategy.services.optimization.feature2'),
                    t('services.marketingStrategy.services.optimization.feature3'),
                    t('services.marketingStrategy.services.optimization.feature4')
                  ]
                },
                {
                  icon: '💡',
                  title: t('services.marketingStrategy.services.consulting.title'),
                  description: t('services.marketingStrategy.services.consulting.description'),
                  features: [
                    t('services.marketingStrategy.services.consulting.feature1'),
                    t('services.marketingStrategy.services.consulting.feature2'),
                    t('services.marketingStrategy.services.consulting.feature3'),
                    t('services.marketingStrategy.services.consulting.feature4')
                  ]
                },
                {
                  icon: '🔄',
                  title: t('services.marketingStrategy.services.growth.title'),
                  description: t('services.marketingStrategy.services.growth.description'),
                  features: [
                    t('services.marketingStrategy.services.growth.feature1'),
                    t('services.marketingStrategy.services.growth.feature2'),
                    t('services.marketingStrategy.services.growth.feature3'),
                    t('services.marketingStrategy.services.growth.feature4')
                  ]
                }
              ].map((service, index) => (
                <div key={index} className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300">
                  <div className="text-4xl mb-4">{service.icon}</div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{service.title}</h3>
                  <p className="text-gray-600 mb-4">{service.description}</p>
                  <ul className="space-y-2">
                    {service.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center text-gray-600 text-sm">
                        <span className="text-purple-600 mr-2">✓</span>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Strategy Process */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                {t('services.marketingStrategy.processTitle')}
              </h2>
              <p className="text-xl text-gray-600">
                {t('services.marketingStrategy.processSubtitle')}
              </p>
            </div>
            
            <div className="space-y-8">
              {[
                { 
                  step: '01', 
                  title: t('services.marketingStrategy.process.step1.title'),
                  description: t('services.marketingStrategy.process.step1.description')
                },
                { 
                  step: '02', 
                  title: t('services.marketingStrategy.process.step2.title'),
                  description: t('services.marketingStrategy.process.step2.description')
                },
                { 
                  step: '03', 
                  title: t('services.marketingStrategy.process.step3.title'),
                  description: t('services.marketingStrategy.process.step3.description')
                },
                { 
                  step: '04', 
                  title: t('services.marketingStrategy.process.step4.title'),
                  description: t('services.marketingStrategy.process.step4.description')
                },
                { 
                  step: '05', 
                  title: t('services.marketingStrategy.process.step5.title'),
                  description: t('services.marketingStrategy.process.step5.description')
                }
              ].map((item, index) => (
                <div key={index} className="flex items-start space-x-6">
                  <div className="w-16 h-16 bg-gradient-to-r from-purple-600 to-blue-500 text-white rounded-full flex items-center justify-center text-xl font-bold flex-shrink-0">
                    {item.step}
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-3">{item.title}</h3>
                    <p className="text-gray-600 text-lg">{item.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Success Metrics */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              {t('services.marketingStrategy.metricsTitle')}
            </h2>
            <p className="text-xl text-gray-600 mb-12">
              {t('services.marketingStrategy.metricsSubtitle')}
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
              <div className="text-center">
                <div className="text-4xl md:text-5xl font-bold text-purple-600 mb-2">400%</div>
                <div className="text-gray-600">{t('services.marketingStrategy.metrics.roi')}</div>
              </div>
              <div className="text-center">
                <div className="text-4xl md:text-5xl font-bold text-purple-600 mb-2">150+</div>
                <div className="text-gray-600">{t('services.marketingStrategy.metrics.strategies')}</div>
              </div>
              <div className="text-center">
                <div className="text-4xl md:text-5xl font-bold text-purple-600 mb-2">95%</div>
                <div className="text-gray-600">{t('services.marketingStrategy.metrics.success')}</div>
              </div>
              <div className="text-center">
                <div className="text-4xl md:text-5xl font-bold text-purple-600 mb-2">24/7</div>
                <div className="text-gray-600">{t('services.marketingStrategy.metrics.support')}</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-gradient-to-r from-purple-600 to-blue-500 text-white py-20">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            {t('services.marketingStrategy.ctaTitle')}
          </h2>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            {t('services.marketingStrategy.ctaSubtitle')}
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/contact"
              className="bg-white text-purple-600 font-bold px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 transform hover:scale-105"
            >
              {t('services.marketingStrategy.ctaButton')}
            </Link>
            <Link
              href="/portfolio/marketing"
              className="border-2 border-white text-white font-bold px-8 py-4 rounded-full hover:bg-white hover:text-purple-600 transition-all duration-300"
            >
              {t('services.marketingStrategy.portfolioButton')}
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
