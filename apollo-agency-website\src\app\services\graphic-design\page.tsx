'use client';

import { useLanguage } from '@/contexts/LanguageContext';
import Link from 'next/link';

export default function GraphicDesignPage() {
  const { t, isRTL } = useLanguage();

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : ''}`}>
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-orange-900 via-red-800 to-pink-600 text-white py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6">
              <span className="text-3xl">🎨</span>
            </div>
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              {t('services.graphicDesign')}
            </h1>
            <p className="text-xl md:text-2xl mb-8 opacity-90">
              Creative designs that communicate your brand message and captivate your audience
            </p>
            <Link
              href="/contact"
              className="inline-block bg-gradient-to-r from-yellow-400 to-orange-500 text-gray-900 font-bold px-8 py-4 rounded-full hover:from-yellow-300 hover:to-orange-400 transition-all duration-300 transform hover:scale-105"
            >
              Start Your Design Project
            </Link>
          </div>
        </div>
      </section>

      {/* Design Services */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Complete Graphic Design Solutions
              </h2>
              <p className="text-xl text-gray-600">
                From concept to completion, we create designs that make an impact
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  icon: '🏷️',
                  title: 'Logo Design',
                  description: 'Memorable logos that represent your brand essence and values.',
                  features: ['Brand research', 'Concept development', 'Multiple variations', 'Vector files']
                },
                {
                  icon: '📄',
                  title: 'Print Design',
                  description: 'Professional print materials that make a lasting impression.',
                  features: ['Brochures & flyers', 'Business cards', 'Posters & banners', 'Print-ready files']
                },
                {
                  icon: '📱',
                  title: 'Digital Graphics',
                  description: 'Eye-catching digital assets for web and social media.',
                  features: ['Social media graphics', 'Web banners', 'Email templates', 'Digital ads']
                },
                {
                  icon: '📊',
                  title: 'Infographics',
                  description: 'Data visualization that makes complex information easy to understand.',
                  features: ['Data visualization', 'Statistical graphics', 'Process diagrams', 'Educational content']
                },
                {
                  icon: '📦',
                  title: 'Packaging Design',
                  description: 'Product packaging that stands out on shelves and online.',
                  features: ['Product packaging', 'Label design', 'Box design', '3D mockups']
                },
                {
                  icon: '🎯',
                  title: 'Brand Identity',
                  description: 'Complete visual identity systems that build brand recognition.',
                  features: ['Brand guidelines', 'Color palettes', 'Typography', 'Visual elements']
                }
              ].map((service, index) => (
                <div key={index} className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300">
                  <div className="text-4xl mb-4">{service.icon}</div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{service.title}</h3>
                  <p className="text-gray-600 mb-4">{service.description}</p>
                  <ul className="space-y-2">
                    {service.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center text-gray-600 text-sm">
                        <span className="text-orange-600 mr-2">✓</span>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Design Specialties */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Our Design Specialties
              </h2>
              <p className="text-xl text-gray-600">
                Expertise across various design disciplines and industries
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {[
                { specialty: 'Corporate Design', icon: '🏢', description: 'Professional business materials' },
                { specialty: 'E-commerce Design', icon: '🛒', description: 'Product and promotional graphics' },
                { specialty: 'Healthcare Design', icon: '🏥', description: 'Medical and wellness materials' },
                { specialty: 'Restaurant Design', icon: '🍽️', description: 'Food and hospitality graphics' },
                { specialty: 'Tech Design', icon: '💻', description: 'Software and app graphics' },
                { specialty: 'Fashion Design', icon: '👗', description: 'Style and lifestyle graphics' },
                { specialty: 'Real Estate Design', icon: '🏠', description: 'Property marketing materials' },
                { specialty: 'Event Design', icon: '🎉', description: 'Event and celebration graphics' }
              ].map((specialty, index) => (
                <div key={index} className="text-center p-6 bg-gray-50 rounded-lg hover:bg-orange-50 transition-colors duration-300">
                  <div className="text-4xl mb-3">{specialty.icon}</div>
                  <h3 className="text-lg font-bold text-gray-900 mb-2">{specialty.specialty}</h3>
                  <p className="text-gray-600 text-sm">{specialty.description}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Design Process */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Our Design Process
              </h2>
              <p className="text-xl text-gray-600">
                From concept to completion, ensuring every design meets your goals
              </p>
            </div>
            
            <div className="space-y-8">
              {[
                { 
                  step: '01', 
                  title: 'Discovery & Brief', 
                  description: 'Understanding your brand, goals, and design requirements in detail.' 
                },
                { 
                  step: '02', 
                  title: 'Concept Development', 
                  description: 'Creating initial design concepts and exploring creative directions.' 
                },
                { 
                  step: '03', 
                  title: 'Design & Refinement', 
                  description: 'Developing chosen concepts with your feedback and refinements.' 
                },
                { 
                  step: '04', 
                  title: 'Finalization & Delivery', 
                  description: 'Preparing final files in all required formats for immediate use.' 
                }
              ].map((item, index) => (
                <div key={index} className="flex items-start space-x-6">
                  <div className="w-16 h-16 bg-gradient-to-r from-orange-600 to-red-500 text-white rounded-full flex items-center justify-center text-xl font-bold flex-shrink-0">
                    {item.step}
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-3">{item.title}</h3>
                    <p className="text-gray-600 text-lg">{item.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Design Tools & Software */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Professional Design Tools
            </h2>
            <p className="text-xl text-gray-600 mb-12">
              We use industry-leading software to create exceptional designs
            </p>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              {[
                { tool: 'Adobe Illustrator', icon: '🎨' },
                { tool: 'Adobe Photoshop', icon: '📸' },
                { tool: 'Adobe InDesign', icon: '📄' },
                { tool: 'Figma', icon: '🎯' },
                { tool: 'Sketch', icon: '✏️' },
                { tool: 'Canva Pro', icon: '🖌️' },
                { tool: 'After Effects', icon: '🎬' },
                { tool: 'Procreate', icon: '🎭' }
              ].map((tool, index) => (
                <div key={index} className="text-center p-4">
                  <div className="text-4xl mb-2">{tool.icon}</div>
                  <p className="text-gray-700 font-medium">{tool.tool}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Success Metrics */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Design Success Metrics
            </h2>
            <p className="text-xl text-gray-600 mb-12">
              Quality designs that deliver measurable results
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
              <div className="text-center">
                <div className="text-4xl md:text-5xl font-bold text-orange-600 mb-2">1000+</div>
                <div className="text-gray-600">Designs Created</div>
              </div>
              <div className="text-center">
                <div className="text-4xl md:text-5xl font-bold text-orange-600 mb-2">98%</div>
                <div className="text-gray-600">Client Satisfaction</div>
              </div>
              <div className="text-center">
                <div className="text-4xl md:text-5xl font-bold text-orange-600 mb-2">48h</div>
                <div className="text-gray-600">Average Turnaround</div>
              </div>
              <div className="text-center">
                <div className="text-4xl md:text-5xl font-bold text-orange-600 mb-2">200%</div>
                <div className="text-gray-600">Brand Recognition Boost</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-gradient-to-r from-orange-600 to-red-500 text-white py-20">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Ready to Create Amazing Designs?
          </h2>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Let's bring your vision to life with professional graphic design that makes an impact.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/contact"
              className="bg-white text-orange-600 font-bold px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 transform hover:scale-105"
            >
              Start Your Design Project
            </Link>
            <Link
              href="/portfolio/graphic-design"
              className="border-2 border-white text-white font-bold px-8 py-4 rounded-full hover:bg-white hover:text-orange-600 transition-all duration-300"
            >
              View Design Portfolio
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
