'use client';

import { useLanguage } from '@/contexts/LanguageContext';
import Link from 'next/link';

export default function MarketingCampaignsPortfolioPage() {
  const { t, isRTL } = useLanguage();

  const campaigns = [
    {
      id: 1,
      title: 'TechCorp Product Launch Campaign',
      description: 'Multi-channel marketing campaign for software product launch',
      channels: ['Google Ads', 'Facebook Ads', 'LinkedIn', 'Email Marketing', 'Content Marketing'],
      results: ['500% ROI', '50,000+ leads', '300% brand awareness increase', '$2M revenue generated'],
      image: '🚀',
      category: 'Product Launch',
      duration: '6 months'
    },
    {
      id: 2,
      title: 'E-commerce Holiday Campaign',
      description: 'Seasonal marketing campaign for online fashion retailer',
      channels: ['Instagram Ads', 'Google Shopping', 'Email Campaigns', 'Influencer Marketing'],
      results: ['400% sales increase', '200% website traffic', '150% customer acquisition', '60% repeat purchases'],
      image: '🛍️',
      category: 'E-commerce',
      duration: '3 months'
    },
    {
      id: 3,
      title: 'Healthcare Awareness Campaign',
      description: 'Educational campaign for healthcare service provider',
      channels: ['Facebook Ads', 'Content Marketing', 'SEO', 'Local Marketing'],
      results: ['1000+ new patients', '300% appointment bookings', '500% website traffic', '200% brand recognition'],
      image: '🏥',
      category: 'Healthcare',
      duration: '12 months'
    },
    {
      id: 4,
      title: 'B2B Lead Generation Campaign',
      description: 'Targeted lead generation for business software company',
      channels: ['LinkedIn Ads', 'Google Ads', 'Email Marketing', 'Webinars'],
      results: ['10,000+ qualified leads', '25% conversion rate', '400% pipeline growth', '$5M in opportunities'],
      image: '💼',
      category: 'B2B Marketing',
      duration: '9 months'
    }
  ];

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : ''}`}>
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-green-900 via-teal-800 to-blue-600 text-white py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6">
              <span className="text-3xl">📊</span>
            </div>
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              {t('portfolio.categories.marketingCampaigns')} Portfolio
            </h1>
            <p className="text-xl md:text-2xl mb-8 opacity-90">
              Data-driven marketing campaigns that deliver measurable results
            </p>
            <Link
              href="/contact"
              className="inline-block bg-gradient-to-r from-green-500 to-blue-500 text-white font-bold px-8 py-4 rounded-full hover:from-green-400 hover:to-blue-400 transition-all duration-300 transform hover:scale-105"
            >
              Launch Your Campaign
            </Link>
          </div>
        </div>
      </section>

      {/* Campaigns Grid */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Successful Marketing Campaigns
              </h2>
              <p className="text-xl text-gray-600">
                Campaigns that exceeded expectations and delivered ROI
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {campaigns.map((campaign) => (
                <div key={campaign.id} className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300">
                  {/* Campaign Header */}
                  <div className="bg-gradient-to-r from-green-100 to-blue-100 p-8 text-center">
                    <div className="text-6xl mb-4">{campaign.image}</div>
                    <div className="flex justify-center items-center gap-4">
                      <span className="bg-white/80 text-green-600 px-3 py-1 rounded-full text-sm font-medium">
                        {campaign.category}
                      </span>
                      <span className="bg-white/80 text-blue-600 px-3 py-1 rounded-full text-sm font-medium">
                        {campaign.duration}
                      </span>
                    </div>
                  </div>

                  {/* Campaign Content */}
                  <div className="p-8">
                    <h3 className="text-2xl font-bold text-gray-900 mb-4">{campaign.title}</h3>
                    <p className="text-gray-600 mb-4">{campaign.description}</p>
                    
                    <div className="mb-6">
                      <h4 className="font-semibold text-gray-900 mb-2">Marketing Channels:</h4>
                      <div className="flex flex-wrap gap-2">
                        {campaign.channels.map((channel, index) => (
                          <span key={index} className="bg-green-100 text-green-600 px-3 py-1 rounded-full text-sm">
                            {channel}
                          </span>
                        ))}
                      </div>
                    </div>

                    <div className="mb-6">
                      <h4 className="font-semibold text-gray-900 mb-3">Campaign Results:</h4>
                      <ul className="space-y-2">
                        {campaign.results.map((result, index) => (
                          <li key={index} className="flex items-center text-green-600">
                            <span className="mr-2">📈</span>
                            {result}
                          </li>
                        ))}
                      </ul>
                    </div>

                    <Link
                      href={`/case-studies/${campaign.id}`}
                      className="inline-flex items-center bg-gradient-to-r from-green-600 to-blue-500 text-white font-bold px-6 py-3 rounded-lg hover:from-green-700 hover:to-blue-600 transition-all duration-300"
                    >
                      View Full Case Study
                      <svg className={`w-4 h-4 ${isRTL ? 'mr-2 rotate-180' : 'ml-2'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </Link>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Marketing Services */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Our Marketing Campaign Services
              </h2>
              <p className="text-xl text-gray-600">
                Full-service marketing campaigns that drive results
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                { icon: '🎯', title: 'Strategy Development', description: 'Data-driven marketing strategies' },
                { icon: '📱', title: 'Multi-Channel Campaigns', description: 'Integrated campaigns across all channels' },
                { icon: '📊', title: 'Performance Tracking', description: 'Real-time analytics and reporting' },
                { icon: '🎨', title: 'Creative Development', description: 'Compelling ad creatives and content' },
                { icon: '💰', title: 'Budget Optimization', description: 'Maximum ROI from your marketing spend' },
                { icon: '🔄', title: 'Campaign Optimization', description: 'Continuous improvement and testing' }
              ].map((service, index) => (
                <div key={index} className="text-center p-6 rounded-lg hover:bg-gray-50 transition-colors duration-300">
                  <div className="text-4xl mb-4">{service.icon}</div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{service.title}</h3>
                  <p className="text-gray-600">{service.description}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Campaign Process */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Our Campaign Development Process
              </h2>
              <p className="text-xl text-gray-600">
                Systematic approach to creating successful marketing campaigns
              </p>
            </div>
            
            <div className="space-y-8">
              {[
                { step: '01', title: 'Strategy & Planning', description: 'Define goals, target audience, and campaign strategy' },
                { step: '02', title: 'Creative Development', description: 'Create compelling ad creatives and marketing materials' },
                { step: '03', title: 'Campaign Launch', description: 'Execute campaigns across selected marketing channels' },
                { step: '04', title: 'Monitor & Optimize', description: 'Track performance and optimize for better results' },
                { step: '05', title: 'Report & Scale', description: 'Analyze results and scale successful campaigns' }
              ].map((item, index) => (
                <div key={index} className="flex items-start space-x-6">
                  <div className="w-16 h-16 bg-gradient-to-r from-green-600 to-blue-500 text-white rounded-full flex items-center justify-center text-xl font-bold flex-shrink-0">
                    {item.step}
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-3">{item.title}</h3>
                    <p className="text-gray-600 text-lg">{item.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-gradient-to-r from-green-600 to-blue-500 text-white py-20">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Ready to Launch Your Next Campaign?
          </h2>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Let's create a marketing campaign that drives real results and grows your business.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/contact"
              className="bg-white text-green-600 font-bold px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 transform hover:scale-105"
            >
              Start Your Campaign
            </Link>
            <Link
              href="/portfolio"
              className="border-2 border-white text-white font-bold px-8 py-4 rounded-full hover:bg-white hover:text-green-600 transition-all duration-300"
            >
              View All Projects
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
