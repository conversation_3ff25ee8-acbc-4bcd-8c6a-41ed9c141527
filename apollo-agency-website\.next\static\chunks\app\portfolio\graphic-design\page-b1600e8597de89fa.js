(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[563],{3508:(e,t,s)=>{Promise.resolve().then(s.bind(s,6281))},6281:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l});var i=s(5155),a=s(9283),r=s(6874),n=s.n(r);function l(){let{t:e,isRTL:t}=(0,a.o)();return(0,i.jsxs)("div",{className:"min-h-screen bg-gray-50 ".concat(t?"rtl":""),children:[(0,i.jsx)("section",{className:"bg-gradient-to-br from-orange-900 via-red-800 to-pink-600 text-white py-20",children:(0,i.jsx)("div",{className:"container mx-auto px-4",children:(0,i.jsxs)("div",{className:"max-w-4xl mx-auto text-center",children:[(0,i.jsx)("div",{className:"w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,i.jsx)("span",{className:"text-3xl",children:"\uD83C\uDFA8"})}),(0,i.jsxs)("h1",{className:"text-4xl md:text-6xl font-bold mb-6",children:[e("portfolio.categories.graphicDesign")," Portfolio"]}),(0,i.jsx)("p",{className:"text-xl md:text-2xl mb-8 opacity-90",children:"Creative designs that communicate your brand message effectively"}),(0,i.jsx)(n(),{href:"/contact",className:"inline-block bg-gradient-to-r from-yellow-400 to-orange-500 text-gray-900 font-bold px-8 py-4 rounded-full hover:from-yellow-300 hover:to-orange-400 transition-all duration-300 transform hover:scale-105",children:"Start Your Design Project"})]})})}),(0,i.jsx)("section",{className:"py-20",children:(0,i.jsx)("div",{className:"container mx-auto px-4",children:(0,i.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,i.jsxs)("div",{className:"text-center mb-16",children:[(0,i.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-6",children:"Featured Design Projects"}),(0,i.jsx)("p",{className:"text-xl text-gray-600",children:"Creative solutions that make an impact"})]}),(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[{id:1,title:"TechCorp Brand Identity",description:"Complete brand identity design including logo, business cards, and marketing materials",deliverables:["Logo Design","Business Cards","Letterhead","Brand Guidelines"],results:["200% increase in brand recognition","Professional brand image","Consistent visual identity"],image:"\uD83C\uDFA8",category:"Brand Identity"},{id:2,title:"Restaurant Menu Design",description:"Modern menu design with food photography and layout optimization",deliverables:["Menu Design","Food Photography","Table Tents","Digital Menu"],results:["30% increase in average order value","Improved customer experience","Modern brand image"],image:"\uD83C\uDF7D️",category:"Print Design"},{id:3,title:"E-commerce Product Catalog",description:"Product catalog design for online fashion retailer",deliverables:["Catalog Layout","Product Photography","Digital Brochure","Social Media Graphics"],results:["50% increase in product views","Higher conversion rates","Professional presentation"],image:"\uD83D\uDCD6",category:"Catalog Design"},{id:4,title:"Healthcare Infographics",description:"Educational infographics for healthcare awareness campaign",deliverables:["Infographic Design","Social Media Graphics","Poster Design","Digital Assets"],results:["1M+ views on social media","Increased health awareness","Professional credibility"],image:"\uD83D\uDCCA",category:"Infographic Design"}].map(e=>(0,i.jsxs)("div",{className:"bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300",children:[(0,i.jsxs)("div",{className:"bg-gradient-to-r from-orange-100 to-red-100 p-8 text-center",children:[(0,i.jsx)("div",{className:"text-6xl mb-4",children:e.image}),(0,i.jsx)("span",{className:"bg-white/80 text-orange-600 px-3 py-1 rounded-full text-sm font-medium",children:e.category})]}),(0,i.jsxs)("div",{className:"p-8",children:[(0,i.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-4",children:e.title}),(0,i.jsx)("p",{className:"text-gray-600 mb-4",children:e.description}),(0,i.jsxs)("div",{className:"mb-6",children:[(0,i.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"Deliverables:"}),(0,i.jsx)("div",{className:"flex flex-wrap gap-2",children:e.deliverables.map((e,t)=>(0,i.jsx)("span",{className:"bg-orange-100 text-orange-600 px-3 py-1 rounded-full text-sm",children:e},t))})]}),(0,i.jsxs)("div",{className:"mb-6",children:[(0,i.jsx)("h4",{className:"font-semibold text-gray-900 mb-3",children:"Results Achieved:"}),(0,i.jsx)("ul",{className:"space-y-2",children:e.results.map((e,t)=>(0,i.jsxs)("li",{className:"flex items-center text-green-600",children:[(0,i.jsx)("span",{className:"mr-2",children:"✓"}),e]},t))})]}),(0,i.jsxs)(n(),{href:"/case-studies/".concat(e.id),className:"inline-flex items-center bg-gradient-to-r from-orange-600 to-red-500 text-white font-bold px-6 py-3 rounded-lg hover:from-orange-700 hover:to-red-600 transition-all duration-300",children:["View Full Case Study",(0,i.jsx)("svg",{className:"w-4 h-4 ".concat(t?"mr-2 rotate-180":"ml-2"),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,i.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})]})]})]},e.id))})]})})}),(0,i.jsx)("section",{className:"py-20 bg-white",children:(0,i.jsx)("div",{className:"container mx-auto px-4",children:(0,i.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,i.jsxs)("div",{className:"text-center mb-16",children:[(0,i.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-6",children:"Our Graphic Design Services"}),(0,i.jsx)("p",{className:"text-xl text-gray-600",children:"Professional design solutions for all your business needs"})]}),(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:[{icon:"\uD83C\uDFF7️",title:"Logo Design",description:"Memorable logos that represent your brand"},{icon:"\uD83D\uDCC4",title:"Print Design",description:"Brochures, flyers, and marketing materials"},{icon:"\uD83D\uDCF1",title:"Digital Graphics",description:"Social media graphics and web assets"},{icon:"\uD83D\uDCCA",title:"Infographics",description:"Data visualization and information design"},{icon:"\uD83D\uDCE6",title:"Packaging Design",description:"Product packaging that stands out"},{icon:"\uD83C\uDFAF",title:"Brand Identity",description:"Complete brand identity systems"}].map((e,t)=>(0,i.jsxs)("div",{className:"text-center p-6 rounded-lg hover:bg-gray-50 transition-colors duration-300",children:[(0,i.jsx)("div",{className:"text-4xl mb-4",children:e.icon}),(0,i.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-3",children:e.title}),(0,i.jsx)("p",{className:"text-gray-600",children:e.description})]},t))})]})})}),(0,i.jsx)("section",{className:"py-20 bg-gray-50",children:(0,i.jsx)("div",{className:"container mx-auto px-4",children:(0,i.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,i.jsxs)("div",{className:"text-center mb-16",children:[(0,i.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-6",children:"Our Design Process"}),(0,i.jsx)("p",{className:"text-xl text-gray-600",children:"From concept to completion, we ensure every design meets your goals"})]}),(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[{step:"01",title:"Discovery",description:"Understanding your brand and requirements"},{step:"02",title:"Concept",description:"Creating initial design concepts and ideas"},{step:"03",title:"Design",description:"Developing the final design with your feedback"},{step:"04",title:"Delivery",description:"Providing all files and formats you need"}].map((e,t)=>(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"w-16 h-16 bg-gradient-to-r from-orange-600 to-red-500 text-white rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4",children:e.step}),(0,i.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-3",children:e.title}),(0,i.jsx)("p",{className:"text-gray-600",children:e.description})]},t))})]})})}),(0,i.jsx)("section",{className:"bg-gradient-to-r from-orange-600 to-red-500 text-white py-20",children:(0,i.jsxs)("div",{className:"container mx-auto px-4 text-center",children:[(0,i.jsx)("h2",{className:"text-3xl md:text-4xl font-bold mb-6",children:"Ready to Create Amazing Designs?"}),(0,i.jsx)("p",{className:"text-xl mb-8 opacity-90 max-w-2xl mx-auto",children:"Let's bring your vision to life with professional graphic design that makes an impact."}),(0,i.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,i.jsx)(n(),{href:"/contact",className:"bg-white text-orange-600 font-bold px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 transform hover:scale-105",children:"Start Your Design Project"}),(0,i.jsx)(n(),{href:"/portfolio",className:"border-2 border-white text-white font-bold px-8 py-4 rounded-full hover:bg-white hover:text-orange-600 transition-all duration-300",children:"View All Projects"})]})]})})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[874,283,441,684,358],()=>t(3508)),_N_E=e.O()}]);