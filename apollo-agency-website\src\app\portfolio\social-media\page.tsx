'use client';

import { useLanguage } from '@/contexts/LanguageContext';
import Link from 'next/link';

export default function SocialMediaPortfolioPage() {
  const { t, isRTL } = useLanguage();

  const projects = [
    {
      id: 1,
      title: 'TechCorp Social Media Campaign',
      description: 'Complete social media strategy and content creation for a tech startup',
      platform: 'Instagram, Facebook, LinkedIn',
      results: ['300% increase in followers', '500% boost in engagement', '200% more leads'],
      image: '📱',
      category: 'Social Media Management'
    },
    {
      id: 2,
      title: 'Restaurant Chain Social Presence',
      description: 'Food photography and social media marketing for restaurant chain',
      platform: 'Instagram, TikTok, Facebook',
      results: ['400% increase in foot traffic', '250% growth in online orders', '600% more social engagement'],
      image: '🍕',
      category: 'Content Creation'
    },
    {
      id: 3,
      title: 'Fashion Brand Influencer Campaign',
      description: 'Influencer marketing campaign for fashion brand launch',
      platform: 'Instagram, YouTube, TikTok',
      results: ['1M+ campaign reach', '50K new followers', '300% sales increase'],
      image: '👗',
      category: 'Influencer Marketing'
    },
    {
      id: 4,
      title: 'Healthcare Social Media Strategy',
      description: 'Educational content and community building for healthcare provider',
      platform: 'Facebook, LinkedIn, YouTube',
      results: ['500% increase in patient inquiries', '200% growth in community', '150% more appointments'],
      image: '🏥',
      category: 'Healthcare Marketing'
    }
  ];

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : ''}`}>
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-pink-900 via-purple-800 to-indigo-600 text-white py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6">
              <span className="text-3xl">📱</span>
            </div>
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              {t('portfolio.categories.socialMedia')} Portfolio
            </h1>
            <p className="text-xl md:text-2xl mb-8 opacity-90">
              Discover our social media success stories and campaigns that drove real results
            </p>
            <Link
              href="/contact"
              className="inline-block bg-gradient-to-r from-pink-500 to-purple-500 text-white font-bold px-8 py-4 rounded-full hover:from-pink-400 hover:to-purple-400 transition-all duration-300 transform hover:scale-105"
            >
              Start Your Social Media Journey
            </Link>
          </div>
        </div>
      </section>

      {/* Projects Grid */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Featured Social Media Projects
              </h2>
              <p className="text-xl text-gray-600">
                Real campaigns, real results, real impact
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {projects.map((project) => (
                <div key={project.id} className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300">
                  {/* Project Header */}
                  <div className="bg-gradient-to-r from-pink-100 to-purple-100 p-8 text-center">
                    <div className="text-6xl mb-4">{project.image}</div>
                    <span className="bg-white/80 text-purple-600 px-3 py-1 rounded-full text-sm font-medium">
                      {project.category}
                    </span>
                  </div>

                  {/* Project Content */}
                  <div className="p-8">
                    <h3 className="text-2xl font-bold text-gray-900 mb-4">{project.title}</h3>
                    <p className="text-gray-600 mb-4">{project.description}</p>
                    
                    <div className="mb-6">
                      <h4 className="font-semibold text-gray-900 mb-2">Platforms:</h4>
                      <p className="text-purple-600">{project.platform}</p>
                    </div>

                    <div className="mb-6">
                      <h4 className="font-semibold text-gray-900 mb-3">Results Achieved:</h4>
                      <ul className="space-y-2">
                        {project.results.map((result, index) => (
                          <li key={index} className="flex items-center text-green-600">
                            <span className="mr-2">✓</span>
                            {result}
                          </li>
                        ))}
                      </ul>
                    </div>

                    <Link
                      href={`/case-studies/${project.id}`}
                      className="inline-flex items-center bg-gradient-to-r from-pink-600 to-purple-500 text-white font-bold px-6 py-3 rounded-lg hover:from-pink-700 hover:to-purple-600 transition-all duration-300"
                    >
                      View Full Case Study
                      <svg className={`w-4 h-4 ${isRTL ? 'mr-2 rotate-180' : 'ml-2'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </Link>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Services Offered */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Our Social Media Services
              </h2>
              <p className="text-xl text-gray-600">
                Comprehensive social media solutions for your business
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                { icon: '📝', title: 'Content Creation', description: 'Engaging posts, stories, and visual content' },
                { icon: '📊', title: 'Strategy Development', description: 'Data-driven social media strategies' },
                { icon: '👥', title: 'Community Management', description: 'Building and engaging your audience' },
                { icon: '📈', title: 'Analytics & Reporting', description: 'Detailed performance insights' },
                { icon: '🎯', title: 'Paid Advertising', description: 'Targeted social media ad campaigns' },
                { icon: '🤝', title: 'Influencer Marketing', description: 'Collaborations with relevant influencers' }
              ].map((service, index) => (
                <div key={index} className="text-center p-6 rounded-lg hover:bg-gray-50 transition-colors duration-300">
                  <div className="text-4xl mb-4">{service.icon}</div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{service.title}</h3>
                  <p className="text-gray-600">{service.description}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-gradient-to-r from-pink-600 to-purple-500 text-white py-20">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Ready to Boost Your Social Media Presence?
          </h2>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Let's create a social media strategy that drives engagement and grows your business.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/contact"
              className="bg-white text-purple-600 font-bold px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 transform hover:scale-105"
            >
              Get Social Media Strategy
            </Link>
            <Link
              href="/portfolio"
              className="border-2 border-white text-white font-bold px-8 py-4 rounded-full hover:bg-white hover:text-purple-600 transition-all duration-300"
            >
              View All Projects
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
