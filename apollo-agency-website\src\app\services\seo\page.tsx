'use client';

import { useLanguage } from '@/contexts/LanguageContext';
import Link from 'next/link';

export default function SEOPage() {
  const { t, isRTL } = useLanguage();

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : ''}`}>
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-green-900 via-green-800 to-blue-600 text-white py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6">
              <span className="text-3xl">🔍</span>
            </div>
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              {t('services.seo')}
            </h1>
            <p className="text-xl md:text-2xl mb-8 opacity-90">
              Improve your website's visibility and rank higher in search results
            </p>
            <Link
              href="/contact"
              className="inline-block bg-gradient-to-r from-yellow-400 to-orange-500 text-gray-900 font-bold px-8 py-4 rounded-full hover:from-yellow-300 hover:to-orange-400 transition-all duration-300 transform hover:scale-105"
            >
              Start SEO Audit
            </Link>
          </div>
        </div>
      </section>

      {/* SEO Services */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Comprehensive SEO Services
              </h2>
              <p className="text-xl text-gray-600">
                Everything you need to dominate search engine results
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {[
                {
                  icon: '🔎',
                  title: 'Keyword Research & Analysis',
                  description: 'Identify high-value keywords that your target audience is searching for.',
                  features: ['Competitor keyword analysis', 'Search volume research', 'Keyword difficulty assessment', 'Long-tail keyword opportunities']
                },
                {
                  icon: '⚙️',
                  title: 'Technical SEO Audit',
                  description: 'Comprehensive technical analysis to identify and fix SEO issues.',
                  features: ['Site speed optimization', 'Mobile responsiveness check', 'URL structure analysis', 'Schema markup implementation']
                },
                {
                  icon: '📝',
                  title: 'On-Page SEO Optimization',
                  description: 'Optimize your website content and structure for better rankings.',
                  features: ['Title tag optimization', 'Meta description writing', 'Header tag structure', 'Internal linking strategy']
                },
                {
                  icon: '🔗',
                  title: 'Link Building Strategy',
                  description: 'Build high-quality backlinks to increase your domain authority.',
                  features: ['Guest posting opportunities', 'Resource page outreach', 'Broken link building', 'Local citation building']
                },
                {
                  icon: '📍',
                  title: 'Local SEO Services',
                  description: 'Optimize your business for local search results and Google My Business.',
                  features: ['Google My Business optimization', 'Local citation management', 'Review management', 'Local keyword targeting']
                },
                {
                  icon: '📊',
                  title: 'SEO Analytics & Reporting',
                  description: 'Track your SEO performance with detailed analytics and reports.',
                  features: ['Ranking position tracking', 'Organic traffic analysis', 'Conversion tracking', 'Monthly SEO reports']
                }
              ].map((service, index) => (
                <div key={index} className="bg-white rounded-xl p-8 shadow-lg hover:shadow-xl transition-all duration-300">
                  <div className="text-4xl mb-4">{service.icon}</div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-3">{service.title}</h3>
                  <p className="text-gray-600 mb-4">{service.description}</p>
                  <ul className="space-y-2">
                    {service.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center text-gray-600">
                        <span className="text-green-600 mr-2">✓</span>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Why Choose Us */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-16">
              Why Choose Our SEO Services?
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {[
                { 
                  icon: '🏆', 
                  title: 'Proven Results', 
                  description: 'We have helped hundreds of businesses achieve top rankings and increase organic traffic.' 
                },
                { 
                  icon: '🔬', 
                  title: 'Data-Driven Approach', 
                  description: 'Our strategies are based on thorough research and analysis, not guesswork.' 
                },
                { 
                  icon: '🤝', 
                  title: 'Transparent Reporting', 
                  description: 'Regular updates and detailed reports keep you informed about your SEO progress.' 
                }
              ].map((item, index) => (
                <div key={index} className="text-center">
                  <div className="text-5xl mb-4">{item.icon}</div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{item.title}</h3>
                  <p className="text-gray-600">{item.description}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-gradient-to-r from-green-600 to-blue-500 text-white py-20">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Ready to Improve Your Search Rankings?
          </h2>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Get a free SEO audit and discover how we can help your website rank higher in search results.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/contact"
              className="bg-white text-green-600 font-bold px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 transform hover:scale-105"
            >
              Get Free SEO Audit
            </Link>
            <Link
              href="/case-studies"
              className="border-2 border-white text-white font-bold px-8 py-4 rounded-full hover:bg-white hover:text-green-600 transition-all duration-300"
            >
              View Case Studies
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
