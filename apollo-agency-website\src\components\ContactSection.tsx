'use client';

import Link from 'next/link';
import { useLanguage } from '@/contexts/LanguageContext';

const ContactSection = () => {
  const { t, isRTL } = useLanguage();

  return (
    <section className={`py-20 bg-gradient-to-br from-purple-900 via-purple-800 to-pink-600 text-white relative overflow-hidden ${isRTL ? 'rtl' : ''}`}>
      {/* Background decorations */}
      <div className="absolute top-10 left-10 w-32 h-32 bg-white/10 rounded-full animate-pulse"></div>
      <div className="absolute bottom-10 right-10 w-24 h-24 bg-white/5 rounded-full animate-bounce"></div>
      <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-yellow-400/20 rounded-full animate-pulse"></div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-4xl mx-auto text-center">
          {/* Icon */}
          <div className="flex justify-center mb-8">
            <div className="w-20 h-20 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center">
              <span className="text-3xl">🚀</span>
            </div>
          </div>

          {/* Heading */}
          <h2 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
            {t('contact.title')}
          </h2>

          {/* Subheading */}
          <p className="text-xl md:text-2xl mb-8 opacity-90 leading-relaxed">
            {t('contact.subtitle')}
          </p>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
            <Link
              href="/contact"
              className="bg-gradient-to-r from-yellow-400 to-orange-500 text-gray-900 font-bold px-8 py-4 rounded-full text-lg hover:from-yellow-300 hover:to-orange-400 transition-all duration-300 transform hover:scale-105 hover:shadow-2xl"
            >
              {t('contact.freeConsultation')}
            </Link>
            <Link
              href="tel:+905528330233"
              className="bg-white/20 backdrop-blur-sm border-2 border-white/30 text-white font-bold px-8 py-4 rounded-full text-lg hover:bg-white/30 transition-all duration-300 transform hover:scale-105"
            >
              {t('contact.callNow')}: +90 ************
            </Link>
          </div>

          {/* Contact Methods */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-16">
            {/* WhatsApp */}
            <div className="text-center">
              <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">💬</span>
              </div>
              <h3 className="text-xl font-bold mb-2">{t('contact.whatsapp')}</h3>
              <p className="text-white/80 mb-4">{t('contact.instantSupport')}</p>
              <Link
                href="https://wa.me/905528330233"
                className="inline-block bg-green-500 hover:bg-green-600 text-white px-6 py-2 rounded-full transition-colors duration-300"
              >
                {t('contact.chatNow')}
              </Link>
            </div>

            {/* Email */}
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">✉️</span>
              </div>
              <h3 className="text-xl font-bold mb-2">{t('contact.email')}</h3>
              <p className="text-white/80 mb-4">{t('contact.sendMessage')}</p>
              <Link
                href="mailto:<EMAIL>"
                className="inline-block bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-full transition-colors duration-300"
              >
                {t('contact.sendEmail')}
              </Link>
            </div>

            {/* Schedule Meeting */}
            <div className="text-center">
              <div className="w-16 h-16 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">📅</span>
              </div>
              <h3 className="text-xl font-bold mb-2">{t('contact.scheduleMeeting')}</h3>
              <p className="text-white/80 mb-4">{t('contact.bookConsultation')}</p>
              <Link
                href="/consultation"
                className="inline-block bg-purple-500 hover:bg-purple-600 text-white px-6 py-2 rounded-full transition-colors duration-300"
              >
                {t('contact.bookNow')}
              </Link>
            </div>
          </div>

          {/* Trust Indicators */}
          <div className="mt-16 pt-8 border-t border-white/20">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
              <div>
                <div className="text-2xl font-bold mb-1">24/7</div>
                <div className="text-white/80 text-sm">{t('contact.supportAvailable')}</div>
              </div>
              <div>
                <div className="text-2xl font-bold mb-1">500+</div>
                <div className="text-white/80 text-sm">{t('clients.stats.projects')}</div>
              </div>
              <div>
                <div className="text-2xl font-bold mb-1">98%</div>
                <div className="text-white/80 text-sm">{t('clients.stats.satisfaction')}</div>
              </div>
              <div>
                <div className="text-2xl font-bold mb-1">5+</div>
                <div className="text-white/80 text-sm">{t('clients.stats.experience')}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ContactSection;
