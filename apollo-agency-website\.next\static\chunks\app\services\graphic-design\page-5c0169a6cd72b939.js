(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[901],{1163:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});var i=s(5155),a=s(9283),n=s(6874),l=s.n(n);function r(){let{t:e,isRTL:t}=(0,a.o)();return(0,i.jsxs)("div",{className:"min-h-screen bg-gray-50 ".concat(t?"rtl":""),children:[(0,i.jsx)("section",{className:"bg-gradient-to-br from-orange-900 via-red-800 to-pink-600 text-white py-20",children:(0,i.jsx)("div",{className:"container mx-auto px-4",children:(0,i.jsxs)("div",{className:"max-w-4xl mx-auto text-center",children:[(0,i.jsx)("div",{className:"w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,i.jsx)("span",{className:"text-3xl",children:"\uD83C\uDFA8"})}),(0,i.jsx)("h1",{className:"text-4xl md:text-6xl font-bold mb-6",children:e("services.graphicDesign")}),(0,i.jsx)("p",{className:"text-xl md:text-2xl mb-8 opacity-90",children:"Creative designs that communicate your brand message and captivate your audience"}),(0,i.jsx)(l(),{href:"/contact",className:"inline-block bg-gradient-to-r from-yellow-400 to-orange-500 text-gray-900 font-bold px-8 py-4 rounded-full hover:from-yellow-300 hover:to-orange-400 transition-all duration-300 transform hover:scale-105",children:"Start Your Design Project"})]})})}),(0,i.jsx)("section",{className:"py-20",children:(0,i.jsx)("div",{className:"container mx-auto px-4",children:(0,i.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,i.jsxs)("div",{className:"text-center mb-16",children:[(0,i.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-6",children:"Complete Graphic Design Solutions"}),(0,i.jsx)("p",{className:"text-xl text-gray-600",children:"From concept to completion, we create designs that make an impact"})]}),(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:[{icon:"\uD83C\uDFF7️",title:"Logo Design",description:"Memorable logos that represent your brand essence and values.",features:["Brand research","Concept development","Multiple variations","Vector files"]},{icon:"\uD83D\uDCC4",title:"Print Design",description:"Professional print materials that make a lasting impression.",features:["Brochures & flyers","Business cards","Posters & banners","Print-ready files"]},{icon:"\uD83D\uDCF1",title:"Digital Graphics",description:"Eye-catching digital assets for web and social media.",features:["Social media graphics","Web banners","Email templates","Digital ads"]},{icon:"\uD83D\uDCCA",title:"Infographics",description:"Data visualization that makes complex information easy to understand.",features:["Data visualization","Statistical graphics","Process diagrams","Educational content"]},{icon:"\uD83D\uDCE6",title:"Packaging Design",description:"Product packaging that stands out on shelves and online.",features:["Product packaging","Label design","Box design","3D mockups"]},{icon:"\uD83C\uDFAF",title:"Brand Identity",description:"Complete visual identity systems that build brand recognition.",features:["Brand guidelines","Color palettes","Typography","Visual elements"]}].map((e,t)=>(0,i.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300",children:[(0,i.jsx)("div",{className:"text-4xl mb-4",children:e.icon}),(0,i.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-3",children:e.title}),(0,i.jsx)("p",{className:"text-gray-600 mb-4",children:e.description}),(0,i.jsx)("ul",{className:"space-y-2",children:e.features.map((e,t)=>(0,i.jsxs)("li",{className:"flex items-center text-gray-600 text-sm",children:[(0,i.jsx)("span",{className:"text-orange-600 mr-2",children:"✓"}),e]},t))})]},t))})]})})}),(0,i.jsx)("section",{className:"py-20 bg-white",children:(0,i.jsx)("div",{className:"container mx-auto px-4",children:(0,i.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,i.jsxs)("div",{className:"text-center mb-16",children:[(0,i.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-6",children:"Our Design Specialties"}),(0,i.jsx)("p",{className:"text-xl text-gray-600",children:"Expertise across various design disciplines and industries"})]}),(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:[{specialty:"Corporate Design",icon:"\uD83C\uDFE2",description:"Professional business materials"},{specialty:"E-commerce Design",icon:"\uD83D\uDED2",description:"Product and promotional graphics"},{specialty:"Healthcare Design",icon:"\uD83C\uDFE5",description:"Medical and wellness materials"},{specialty:"Restaurant Design",icon:"\uD83C\uDF7D️",description:"Food and hospitality graphics"},{specialty:"Tech Design",icon:"\uD83D\uDCBB",description:"Software and app graphics"},{specialty:"Fashion Design",icon:"\uD83D\uDC57",description:"Style and lifestyle graphics"},{specialty:"Real Estate Design",icon:"\uD83C\uDFE0",description:"Property marketing materials"},{specialty:"Event Design",icon:"\uD83C\uDF89",description:"Event and celebration graphics"}].map((e,t)=>(0,i.jsxs)("div",{className:"text-center p-6 bg-gray-50 rounded-lg hover:bg-orange-50 transition-colors duration-300",children:[(0,i.jsx)("div",{className:"text-4xl mb-3",children:e.icon}),(0,i.jsx)("h3",{className:"text-lg font-bold text-gray-900 mb-2",children:e.specialty}),(0,i.jsx)("p",{className:"text-gray-600 text-sm",children:e.description})]},t))})]})})}),(0,i.jsx)("section",{className:"py-20 bg-gray-50",children:(0,i.jsx)("div",{className:"container mx-auto px-4",children:(0,i.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,i.jsxs)("div",{className:"text-center mb-16",children:[(0,i.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-6",children:"Our Design Process"}),(0,i.jsx)("p",{className:"text-xl text-gray-600",children:"From concept to completion, ensuring every design meets your goals"})]}),(0,i.jsx)("div",{className:"space-y-8",children:[{step:"01",title:"Discovery & Brief",description:"Understanding your brand, goals, and design requirements in detail."},{step:"02",title:"Concept Development",description:"Creating initial design concepts and exploring creative directions."},{step:"03",title:"Design & Refinement",description:"Developing chosen concepts with your feedback and refinements."},{step:"04",title:"Finalization & Delivery",description:"Preparing final files in all required formats for immediate use."}].map((e,t)=>(0,i.jsxs)("div",{className:"flex items-start space-x-6",children:[(0,i.jsx)("div",{className:"w-16 h-16 bg-gradient-to-r from-orange-600 to-red-500 text-white rounded-full flex items-center justify-center text-xl font-bold flex-shrink-0",children:e.step}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-3",children:e.title}),(0,i.jsx)("p",{className:"text-gray-600 text-lg",children:e.description})]})]},t))})]})})}),(0,i.jsx)("section",{className:"py-20 bg-white",children:(0,i.jsx)("div",{className:"container mx-auto px-4",children:(0,i.jsxs)("div",{className:"max-w-4xl mx-auto text-center",children:[(0,i.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-6",children:"Professional Design Tools"}),(0,i.jsx)("p",{className:"text-xl text-gray-600 mb-12",children:"We use industry-leading software to create exceptional designs"}),(0,i.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-8",children:[{tool:"Adobe Illustrator",icon:"\uD83C\uDFA8"},{tool:"Adobe Photoshop",icon:"\uD83D\uDCF8"},{tool:"Adobe InDesign",icon:"\uD83D\uDCC4"},{tool:"Figma",icon:"\uD83C\uDFAF"},{tool:"Sketch",icon:"✏️"},{tool:"Canva Pro",icon:"\uD83D\uDD8C️"},{tool:"After Effects",icon:"\uD83C\uDFAC"},{tool:"Procreate",icon:"\uD83C\uDFAD"}].map((e,t)=>(0,i.jsxs)("div",{className:"text-center p-4",children:[(0,i.jsx)("div",{className:"text-4xl mb-2",children:e.icon}),(0,i.jsx)("p",{className:"text-gray-700 font-medium",children:e.tool})]},t))})]})})}),(0,i.jsx)("section",{className:"py-20 bg-gray-50",children:(0,i.jsx)("div",{className:"container mx-auto px-4",children:(0,i.jsxs)("div",{className:"max-w-4xl mx-auto text-center",children:[(0,i.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-6",children:"Design Success Metrics"}),(0,i.jsx)("p",{className:"text-xl text-gray-600 mb-12",children:"Quality designs that deliver measurable results"}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"text-4xl md:text-5xl font-bold text-orange-600 mb-2",children:"1000+"}),(0,i.jsx)("div",{className:"text-gray-600",children:"Designs Created"})]}),(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"text-4xl md:text-5xl font-bold text-orange-600 mb-2",children:"98%"}),(0,i.jsx)("div",{className:"text-gray-600",children:"Client Satisfaction"})]}),(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"text-4xl md:text-5xl font-bold text-orange-600 mb-2",children:"48h"}),(0,i.jsx)("div",{className:"text-gray-600",children:"Average Turnaround"})]}),(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"text-4xl md:text-5xl font-bold text-orange-600 mb-2",children:"200%"}),(0,i.jsx)("div",{className:"text-gray-600",children:"Brand Recognition Boost"})]})]})]})})}),(0,i.jsx)("section",{className:"bg-gradient-to-r from-orange-600 to-red-500 text-white py-20",children:(0,i.jsxs)("div",{className:"container mx-auto px-4 text-center",children:[(0,i.jsx)("h2",{className:"text-3xl md:text-4xl font-bold mb-6",children:"Ready to Create Amazing Designs?"}),(0,i.jsx)("p",{className:"text-xl mb-8 opacity-90 max-w-2xl mx-auto",children:"Let's bring your vision to life with professional graphic design that makes an impact."}),(0,i.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,i.jsx)(l(),{href:"/contact",className:"bg-white text-orange-600 font-bold px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 transform hover:scale-105",children:"Start Your Design Project"}),(0,i.jsx)(l(),{href:"/portfolio/graphic-design",className:"border-2 border-white text-white font-bold px-8 py-4 rounded-full hover:bg-white hover:text-orange-600 transition-all duration-300",children:"View Design Portfolio"})]})]})})]})}},7413:(e,t,s)=>{Promise.resolve().then(s.bind(s,1163))}},e=>{var t=t=>e(e.s=t);e.O(0,[874,283,441,684,358],()=>t(7413)),_N_E=e.O()}]);