{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/agency/apollo-agency-website/src/app/portfolio/graphic-design/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useLanguage } from '@/contexts/LanguageContext';\nimport Link from 'next/link';\n\nexport default function GraphicDesignPortfolioPage() {\n  const { t, isRTL } = useLanguage();\n\n  const projects = [\n    {\n      id: 1,\n      title: 'TechCorp Brand Identity',\n      description: 'Complete brand identity design including logo, business cards, and marketing materials',\n      deliverables: ['Logo Design', 'Business Cards', 'Letterhead', 'Brand Guidelines'],\n      results: ['200% increase in brand recognition', 'Professional brand image', 'Consistent visual identity'],\n      image: '🎨',\n      category: 'Brand Identity'\n    },\n    {\n      id: 2,\n      title: 'Restaurant Menu Design',\n      description: 'Modern menu design with food photography and layout optimization',\n      deliverables: ['Menu Design', 'Food Photography', 'Table Tents', 'Digital Menu'],\n      results: ['30% increase in average order value', 'Improved customer experience', 'Modern brand image'],\n      image: '🍽️',\n      category: 'Print Design'\n    },\n    {\n      id: 3,\n      title: 'E-commerce Product Catalog',\n      description: 'Product catalog design for online fashion retailer',\n      deliverables: ['Catalog Layout', 'Product Photography', 'Digital Brochure', 'Social Media Graphics'],\n      results: ['50% increase in product views', 'Higher conversion rates', 'Professional presentation'],\n      image: '📖',\n      category: 'Catalog Design'\n    },\n    {\n      id: 4,\n      title: 'Healthcare Infographics',\n      description: 'Educational infographics for healthcare awareness campaign',\n      deliverables: ['Infographic Design', 'Social Media Graphics', 'Poster Design', 'Digital Assets'],\n      results: ['1M+ views on social media', 'Increased health awareness', 'Professional credibility'],\n      image: '📊',\n      category: 'Infographic Design'\n    }\n  ];\n\n  return (\n    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : ''}`}>\n      {/* Hero Section */}\n      <section className=\"bg-gradient-to-br from-orange-900 via-red-800 to-pink-600 text-white py-20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-4xl mx-auto text-center\">\n            <div className=\"w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6\">\n              <span className=\"text-3xl\">🎨</span>\n            </div>\n            <h1 className=\"text-4xl md:text-6xl font-bold mb-6\">\n              {t('portfolio.categories.graphicDesign')} Portfolio\n            </h1>\n            <p className=\"text-xl md:text-2xl mb-8 opacity-90\">\n              Creative designs that communicate your brand message effectively\n            </p>\n            <Link\n              href=\"/contact\"\n              className=\"inline-block bg-gradient-to-r from-yellow-400 to-orange-500 text-gray-900 font-bold px-8 py-4 rounded-full hover:from-yellow-300 hover:to-orange-400 transition-all duration-300 transform hover:scale-105\"\n            >\n              Start Your Design Project\n            </Link>\n          </div>\n        </div>\n      </section>\n\n      {/* Projects Grid */}\n      <section className=\"py-20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-6xl mx-auto\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-6\">\n                Featured Design Projects\n              </h2>\n              <p className=\"text-xl text-gray-600\">\n                Creative solutions that make an impact\n              </p>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8\">\n              {projects.map((project) => (\n                <div key={project.id} className=\"bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300\">\n                  {/* Project Header */}\n                  <div className=\"bg-gradient-to-r from-orange-100 to-red-100 p-8 text-center\">\n                    <div className=\"text-6xl mb-4\">{project.image}</div>\n                    <span className=\"bg-white/80 text-orange-600 px-3 py-1 rounded-full text-sm font-medium\">\n                      {project.category}\n                    </span>\n                  </div>\n\n                  {/* Project Content */}\n                  <div className=\"p-8\">\n                    <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">{project.title}</h3>\n                    <p className=\"text-gray-600 mb-4\">{project.description}</p>\n                    \n                    <div className=\"mb-6\">\n                      <h4 className=\"font-semibold text-gray-900 mb-2\">Deliverables:</h4>\n                      <div className=\"flex flex-wrap gap-2\">\n                        {project.deliverables.map((item, index) => (\n                          <span key={index} className=\"bg-orange-100 text-orange-600 px-3 py-1 rounded-full text-sm\">\n                            {item}\n                          </span>\n                        ))}\n                      </div>\n                    </div>\n\n                    <div className=\"mb-6\">\n                      <h4 className=\"font-semibold text-gray-900 mb-3\">Results Achieved:</h4>\n                      <ul className=\"space-y-2\">\n                        {project.results.map((result, index) => (\n                          <li key={index} className=\"flex items-center text-green-600\">\n                            <span className=\"mr-2\">✓</span>\n                            {result}\n                          </li>\n                        ))}\n                      </ul>\n                    </div>\n\n                    <Link\n                      href={`/case-studies/${project.id}`}\n                      className=\"inline-flex items-center bg-gradient-to-r from-orange-600 to-red-500 text-white font-bold px-6 py-3 rounded-lg hover:from-orange-700 hover:to-red-600 transition-all duration-300\"\n                    >\n                      View Full Case Study\n                      <svg className={`w-4 h-4 ${isRTL ? 'mr-2 rotate-180' : 'ml-2'}`} fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                      </svg>\n                    </Link>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Design Services */}\n      <section className=\"py-20 bg-white\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-4xl mx-auto\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-6\">\n                Our Graphic Design Services\n              </h2>\n              <p className=\"text-xl text-gray-600\">\n                Professional design solutions for all your business needs\n              </p>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n              {[\n                { icon: '🏷️', title: 'Logo Design', description: 'Memorable logos that represent your brand' },\n                { icon: '📄', title: 'Print Design', description: 'Brochures, flyers, and marketing materials' },\n                { icon: '📱', title: 'Digital Graphics', description: 'Social media graphics and web assets' },\n                { icon: '📊', title: 'Infographics', description: 'Data visualization and information design' },\n                { icon: '📦', title: 'Packaging Design', description: 'Product packaging that stands out' },\n                { icon: '🎯', title: 'Brand Identity', description: 'Complete brand identity systems' }\n              ].map((service, index) => (\n                <div key={index} className=\"text-center p-6 rounded-lg hover:bg-gray-50 transition-colors duration-300\">\n                  <div className=\"text-4xl mb-4\">{service.icon}</div>\n                  <h3 className=\"text-xl font-bold text-gray-900 mb-3\">{service.title}</h3>\n                  <p className=\"text-gray-600\">{service.description}</p>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Design Process */}\n      <section className=\"py-20 bg-gray-50\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-4xl mx-auto\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-6\">\n                Our Design Process\n              </h2>\n              <p className=\"text-xl text-gray-600\">\n                From concept to completion, we ensure every design meets your goals\n              </p>\n            </div>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n              {[\n                { step: '01', title: 'Discovery', description: 'Understanding your brand and requirements' },\n                { step: '02', title: 'Concept', description: 'Creating initial design concepts and ideas' },\n                { step: '03', title: 'Design', description: 'Developing the final design with your feedback' },\n                { step: '04', title: 'Delivery', description: 'Providing all files and formats you need' }\n              ].map((item, index) => (\n                <div key={index} className=\"text-center\">\n                  <div className=\"w-16 h-16 bg-gradient-to-r from-orange-600 to-red-500 text-white rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4\">\n                    {item.step}\n                  </div>\n                  <h3 className=\"text-xl font-bold text-gray-900 mb-3\">{item.title}</h3>\n                  <p className=\"text-gray-600\">{item.description}</p>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"bg-gradient-to-r from-orange-600 to-red-500 text-white py-20\">\n        <div className=\"container mx-auto px-4 text-center\">\n          <h2 className=\"text-3xl md:text-4xl font-bold mb-6\">\n            Ready to Create Amazing Designs?\n          </h2>\n          <p className=\"text-xl mb-8 opacity-90 max-w-2xl mx-auto\">\n            Let's bring your vision to life with professional graphic design that makes an impact.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Link\n              href=\"/contact\"\n              className=\"bg-white text-orange-600 font-bold px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 transform hover:scale-105\"\n            >\n              Start Your Design Project\n            </Link>\n            <Link\n              href=\"/portfolio\"\n              className=\"border-2 border-white text-white font-bold px-8 py-4 rounded-full hover:bg-white hover:text-orange-600 transition-all duration-300\"\n            >\n              View All Projects\n            </Link>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;IAE/B,MAAM,WAAW;QACf;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,cAAc;gBAAC;gBAAe;gBAAkB;gBAAc;aAAmB;YACjF,SAAS;gBAAC;gBAAsC;gBAA4B;aAA6B;YACzG,OAAO;YACP,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,cAAc;gBAAC;gBAAe;gBAAoB;gBAAe;aAAe;YAChF,SAAS;gBAAC;gBAAuC;gBAAgC;aAAqB;YACtG,OAAO;YACP,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,cAAc;gBAAC;gBAAkB;gBAAuB;gBAAoB;aAAwB;YACpG,SAAS;gBAAC;gBAAiC;gBAA2B;aAA4B;YAClG,OAAO;YACP,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,cAAc;gBAAC;gBAAsB;gBAAyB;gBAAiB;aAAiB;YAChG,SAAS;gBAAC;gBAA6B;gBAA8B;aAA2B;YAChG,OAAO;YACP,UAAU;QACZ;KACD;IAED,qBACE,6LAAC;QAAI,WAAW,CAAC,wBAAwB,EAAE,QAAQ,QAAQ,IAAI;;0BAE7D,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAAW;;;;;;;;;;;0CAE7B,6LAAC;gCAAG,WAAU;;oCACX,EAAE;oCAAsC;;;;;;;0CAE3C,6LAAC;gCAAE,WAAU;0CAAsC;;;;;;0CAGnD,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;0BAQP,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAGlE,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAKvC,6LAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;wCAAqB,WAAU;;0DAE9B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAiB,QAAQ,KAAK;;;;;;kEAC7C,6LAAC;wDAAK,WAAU;kEACb,QAAQ,QAAQ;;;;;;;;;;;;0DAKrB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAyC,QAAQ,KAAK;;;;;;kEACpE,6LAAC;wDAAE,WAAU;kEAAsB,QAAQ,WAAW;;;;;;kEAEtD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EAAmC;;;;;;0EACjD,6LAAC;gEAAI,WAAU;0EACZ,QAAQ,YAAY,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC/B,6LAAC;wEAAiB,WAAU;kFACzB;uEADQ;;;;;;;;;;;;;;;;kEAOjB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EAAmC;;;;;;0EACjD,6LAAC;gEAAG,WAAU;0EACX,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAC5B,6LAAC;wEAAe,WAAU;;0FACxB,6LAAC;gFAAK,WAAU;0FAAO;;;;;;4EACtB;;uEAFM;;;;;;;;;;;;;;;;kEAQf,6LAAC,+JAAA,CAAA,UAAI;wDACH,MAAM,CAAC,cAAc,EAAE,QAAQ,EAAE,EAAE;wDACnC,WAAU;;4DACX;0EAEC,6LAAC;gEAAI,WAAW,CAAC,QAAQ,EAAE,QAAQ,oBAAoB,QAAQ;gEAAE,MAAK;gEAAO,QAAO;gEAAe,SAAQ;0EACzG,cAAA,6LAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;uCA3CnE,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;0BAuD9B,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAGlE,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAKvC,6LAAC;gCAAI,WAAU;0CACZ;oCACC;wCAAE,MAAM;wCAAO,OAAO;wCAAe,aAAa;oCAA4C;oCAC9F;wCAAE,MAAM;wCAAM,OAAO;wCAAgB,aAAa;oCAA6C;oCAC/F;wCAAE,MAAM;wCAAM,OAAO;wCAAoB,aAAa;oCAAuC;oCAC7F;wCAAE,MAAM;wCAAM,OAAO;wCAAgB,aAAa;oCAA4C;oCAC9F;wCAAE,MAAM;wCAAM,OAAO;wCAAoB,aAAa;oCAAoC;oCAC1F;wCAAE,MAAM;wCAAM,OAAO;wCAAkB,aAAa;oCAAkC;iCACvF,CAAC,GAAG,CAAC,CAAC,SAAS,sBACd,6LAAC;wCAAgB,WAAU;;0DACzB,6LAAC;gDAAI,WAAU;0DAAiB,QAAQ,IAAI;;;;;;0DAC5C,6LAAC;gDAAG,WAAU;0DAAwC,QAAQ,KAAK;;;;;;0DACnE,6LAAC;gDAAE,WAAU;0DAAiB,QAAQ,WAAW;;;;;;;uCAHzC;;;;;;;;;;;;;;;;;;;;;;;;;;0BAYpB,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAGlE,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAKvC,6LAAC;gCAAI,WAAU;0CACZ;oCACC;wCAAE,MAAM;wCAAM,OAAO;wCAAa,aAAa;oCAA4C;oCAC3F;wCAAE,MAAM;wCAAM,OAAO;wCAAW,aAAa;oCAA6C;oCAC1F;wCAAE,MAAM;wCAAM,OAAO;wCAAU,aAAa;oCAAiD;oCAC7F;wCAAE,MAAM;wCAAM,OAAO;wCAAY,aAAa;oCAA2C;iCAC1F,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,6LAAC;wCAAgB,WAAU;;0DACzB,6LAAC;gDAAI,WAAU;0DACZ,KAAK,IAAI;;;;;;0DAEZ,6LAAC;gDAAG,WAAU;0DAAwC,KAAK,KAAK;;;;;;0DAChE,6LAAC;gDAAE,WAAU;0DAAiB,KAAK,WAAW;;;;;;;uCALtC;;;;;;;;;;;;;;;;;;;;;;;;;;0BAcpB,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAsC;;;;;;sCAGpD,6LAAC;4BAAE,WAAU;sCAA4C;;;;;;sCAGzD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GArOwB;;QACD,sIAAA,CAAA,cAAW;;;KADV", "debugId": null}}]}