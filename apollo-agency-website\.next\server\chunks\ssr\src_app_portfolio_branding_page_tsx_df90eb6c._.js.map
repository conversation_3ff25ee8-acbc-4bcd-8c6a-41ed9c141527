{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/agency/apollo-agency-website/src/app/portfolio/branding/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useLanguage } from '@/contexts/LanguageContext';\nimport Link from 'next/link';\n\nexport default function BrandingPortfolioPage() {\n  const { t, isRTL } = useLanguage();\n\n  // This is the same as brand-identity but with different URL for compatibility\n  const projects = [\n    {\n      id: 1,\n      title: 'TechCorp Complete Rebrand',\n      description: 'Full brand identity redesign for technology company',\n      deliverables: ['Logo Design', 'Brand Guidelines', 'Color Palette', 'Typography', 'Business Cards', 'Letterhead'],\n      results: ['300% increase in brand recognition', 'Improved market positioning', 'Consistent brand experience'],\n      image: '🎯',\n      category: 'Complete Rebrand'\n    },\n    {\n      id: 2,\n      title: 'StartupHub Brand Launch',\n      description: 'Brand identity creation for new startup accelerator',\n      deliverables: ['Logo Design', 'Brand Strategy', 'Visual Identity', 'Marketing Materials', 'Website Design'],\n      results: ['Strong market entry', 'Professional credibility', 'Investor confidence'],\n      image: '🚀',\n      category: 'Brand Launch'\n    },\n    {\n      id: 3,\n      title: 'HealthPlus Medical Brand',\n      description: 'Healthcare brand identity with trust and professionalism focus',\n      deliverables: ['Medical Logo', 'Patient Materials', 'Signage Design', 'Digital Assets'],\n      results: ['Increased patient trust', 'Professional appearance', 'Clear communication'],\n      image: '🏥',\n      category: 'Healthcare Branding'\n    },\n    {\n      id: 4,\n      title: 'FoodieApp Restaurant Brand',\n      description: 'Modern restaurant brand identity with appetite appeal',\n      deliverables: ['Restaurant Logo', 'Menu Design', 'Packaging', 'Interior Graphics'],\n      results: ['200% increase in customers', 'Premium brand perception', 'Social media buzz'],\n      image: '🍕',\n      category: 'Restaurant Branding'\n    }\n  ];\n\n  return (\n    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : ''}`}>\n      {/* Hero Section */}\n      <section className=\"bg-gradient-to-br from-indigo-900 via-purple-800 to-pink-600 text-white py-20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-4xl mx-auto text-center\">\n            <div className=\"w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6\">\n              <span className=\"text-3xl\">🎯</span>\n            </div>\n            <h1 className=\"text-4xl md:text-6xl font-bold mb-6\">\n              Branding Portfolio\n            </h1>\n            <p className=\"text-xl md:text-2xl mb-8 opacity-90\">\n              Creating memorable brand identities that connect with your audience\n            </p>\n            <Link\n              href=\"/contact\"\n              className=\"inline-block bg-gradient-to-r from-purple-500 to-pink-500 text-white font-bold px-8 py-4 rounded-full hover:from-purple-400 hover:to-pink-400 transition-all duration-300 transform hover:scale-105\"\n            >\n              Build Your Brand Identity\n            </Link>\n          </div>\n        </div>\n      </section>\n\n      {/* Projects Grid */}\n      <section className=\"py-20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-6xl mx-auto\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-6\">\n                Featured Branding Projects\n              </h2>\n              <p className=\"text-xl text-gray-600\">\n                Brands we've helped establish and grow\n              </p>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8\">\n              {projects.map((project) => (\n                <div key={project.id} className=\"bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300\">\n                  {/* Project Header */}\n                  <div className=\"bg-gradient-to-r from-indigo-100 to-purple-100 p-8 text-center\">\n                    <div className=\"text-6xl mb-4\">{project.image}</div>\n                    <span className=\"bg-white/80 text-indigo-600 px-3 py-1 rounded-full text-sm font-medium\">\n                      {project.category}\n                    </span>\n                  </div>\n\n                  {/* Project Content */}\n                  <div className=\"p-8\">\n                    <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">{project.title}</h3>\n                    <p className=\"text-gray-600 mb-4\">{project.description}</p>\n                    \n                    <div className=\"mb-6\">\n                      <h4 className=\"font-semibold text-gray-900 mb-2\">Deliverables:</h4>\n                      <div className=\"flex flex-wrap gap-2\">\n                        {project.deliverables.map((item, index) => (\n                          <span key={index} className=\"bg-indigo-100 text-indigo-600 px-3 py-1 rounded-full text-sm\">\n                            {item}\n                          </span>\n                        ))}\n                      </div>\n                    </div>\n\n                    <div className=\"mb-6\">\n                      <h4 className=\"font-semibold text-gray-900 mb-3\">Results Achieved:</h4>\n                      <ul className=\"space-y-2\">\n                        {project.results.map((result, index) => (\n                          <li key={index} className=\"flex items-center text-green-600\">\n                            <span className=\"mr-2\">✓</span>\n                            {result}\n                          </li>\n                        ))}\n                      </ul>\n                    </div>\n\n                    <Link\n                      href={`/case-studies/${project.id}`}\n                      className=\"inline-flex items-center bg-gradient-to-r from-indigo-600 to-purple-500 text-white font-bold px-6 py-3 rounded-lg hover:from-indigo-700 hover:to-purple-600 transition-all duration-300\"\n                    >\n                      View Full Case Study\n                      <svg className={`w-4 h-4 ${isRTL ? 'mr-2 rotate-180' : 'ml-2'}`} fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                      </svg>\n                    </Link>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Branding Services */}\n      <section className=\"py-20 bg-white\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-4xl mx-auto\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-6\">\n                Our Branding Services\n              </h2>\n              <p className=\"text-xl text-gray-600\">\n                Complete brand identity solutions from concept to implementation\n              </p>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n              {[\n                { icon: '🎨', title: 'Logo Design', description: 'Unique logos that represent your brand essence' },\n                { icon: '📋', title: 'Brand Guidelines', description: 'Comprehensive brand usage guidelines' },\n                { icon: '🎨', title: 'Visual Identity', description: 'Color palettes, typography, and visual elements' },\n                { icon: '📄', title: 'Stationery Design', description: 'Business cards, letterheads, and documents' },\n                { icon: '📦', title: 'Packaging Design', description: 'Product packaging that reflects your brand' },\n                { icon: '🌐', title: 'Digital Assets', description: 'Web graphics and social media templates' }\n              ].map((service, index) => (\n                <div key={index} className=\"text-center p-6 rounded-lg hover:bg-gray-50 transition-colors duration-300\">\n                  <div className=\"text-4xl mb-4\">{service.icon}</div>\n                  <h3 className=\"text-xl font-bold text-gray-900 mb-3\">{service.title}</h3>\n                  <p className=\"text-gray-600\">{service.description}</p>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"bg-gradient-to-r from-indigo-600 to-purple-500 text-white py-20\">\n        <div className=\"container mx-auto px-4 text-center\">\n          <h2 className=\"text-3xl md:text-4xl font-bold mb-6\">\n            Ready to Build a Powerful Brand?\n          </h2>\n          <p className=\"text-xl mb-8 opacity-90 max-w-2xl mx-auto\">\n            Let's create a brand identity that sets you apart from the competition and connects with your audience.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Link\n              href=\"/contact\"\n              className=\"bg-white text-indigo-600 font-bold px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 transform hover:scale-105\"\n            >\n              Start Your Brand Journey\n            </Link>\n            <Link\n              href=\"/portfolio\"\n              className=\"border-2 border-white text-white font-bold px-8 py-4 rounded-full hover:bg-white hover:text-indigo-600 transition-all duration-300\"\n            >\n              View All Projects\n            </Link>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,cAAW,AAAD;IAE/B,8EAA8E;IAC9E,MAAM,WAAW;QACf;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,cAAc;gBAAC;gBAAe;gBAAoB;gBAAiB;gBAAc;gBAAkB;aAAa;YAChH,SAAS;gBAAC;gBAAsC;gBAA+B;aAA8B;YAC7G,OAAO;YACP,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,cAAc;gBAAC;gBAAe;gBAAkB;gBAAmB;gBAAuB;aAAiB;YAC3G,SAAS;gBAAC;gBAAuB;gBAA4B;aAAsB;YACnF,OAAO;YACP,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,cAAc;gBAAC;gBAAgB;gBAAqB;gBAAkB;aAAiB;YACvF,SAAS;gBAAC;gBAA2B;gBAA2B;aAAsB;YACtF,OAAO;YACP,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,cAAc;gBAAC;gBAAmB;gBAAe;gBAAa;aAAoB;YAClF,SAAS;gBAAC;gBAA8B;gBAA4B;aAAoB;YACxF,OAAO;YACP,UAAU;QACZ;KACD;IAED,qBACE,8OAAC;QAAI,WAAW,CAAC,wBAAwB,EAAE,QAAQ,QAAQ,IAAI;;0BAE7D,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CAAW;;;;;;;;;;;0CAE7B,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAGpD,8OAAC;gCAAE,WAAU;0CAAsC;;;;;;0CAGnD,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;0BAQP,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAGlE,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAKvC,8OAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;wCAAqB,WAAU;;0DAE9B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAiB,QAAQ,KAAK;;;;;;kEAC7C,8OAAC;wDAAK,WAAU;kEACb,QAAQ,QAAQ;;;;;;;;;;;;0DAKrB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAyC,QAAQ,KAAK;;;;;;kEACpE,8OAAC;wDAAE,WAAU;kEAAsB,QAAQ,WAAW;;;;;;kEAEtD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAmC;;;;;;0EACjD,8OAAC;gEAAI,WAAU;0EACZ,QAAQ,YAAY,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC/B,8OAAC;wEAAiB,WAAU;kFACzB;uEADQ;;;;;;;;;;;;;;;;kEAOjB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAmC;;;;;;0EACjD,8OAAC;gEAAG,WAAU;0EACX,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAC5B,8OAAC;wEAAe,WAAU;;0FACxB,8OAAC;gFAAK,WAAU;0FAAO;;;;;;4EACtB;;uEAFM;;;;;;;;;;;;;;;;kEAQf,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAM,CAAC,cAAc,EAAE,QAAQ,EAAE,EAAE;wDACnC,WAAU;;4DACX;0EAEC,8OAAC;gEAAI,WAAW,CAAC,QAAQ,EAAE,QAAQ,oBAAoB,QAAQ;gEAAE,MAAK;gEAAO,QAAO;gEAAe,SAAQ;0EACzG,cAAA,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;uCA3CnE,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;0BAuD9B,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAGlE,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAKvC,8OAAC;gCAAI,WAAU;0CACZ;oCACC;wCAAE,MAAM;wCAAM,OAAO;wCAAe,aAAa;oCAAiD;oCAClG;wCAAE,MAAM;wCAAM,OAAO;wCAAoB,aAAa;oCAAuC;oCAC7F;wCAAE,MAAM;wCAAM,OAAO;wCAAmB,aAAa;oCAAkD;oCACvG;wCAAE,MAAM;wCAAM,OAAO;wCAAqB,aAAa;oCAA6C;oCACpG;wCAAE,MAAM;wCAAM,OAAO;wCAAoB,aAAa;oCAA6C;oCACnG;wCAAE,MAAM;wCAAM,OAAO;wCAAkB,aAAa;oCAA0C;iCAC/F,CAAC,GAAG,CAAC,CAAC,SAAS,sBACd,8OAAC;wCAAgB,WAAU;;0DACzB,8OAAC;gDAAI,WAAU;0DAAiB,QAAQ,IAAI;;;;;;0DAC5C,8OAAC;gDAAG,WAAU;0DAAwC,QAAQ,KAAK;;;;;;0DACnE,8OAAC;gDAAE,WAAU;0DAAiB,QAAQ,WAAW;;;;;;;uCAHzC;;;;;;;;;;;;;;;;;;;;;;;;;;0BAYpB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAsC;;;;;;sCAGpD,8OAAC;4BAAE,WAAU;sCAA4C;;;;;;sCAGzD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}]}