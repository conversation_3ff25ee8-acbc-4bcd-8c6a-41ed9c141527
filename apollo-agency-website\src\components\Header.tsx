'use client';

import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useLanguage } from '@/contexts/LanguageContext';

const Header = () => {
  const { language, setLanguage, t, isRTL } = useLanguage();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isServicesOpen, setIsServicesOpen] = useState(false);
  const [isPortfolioOpen, setIsPortfolioOpen] = useState(false);
  const [isBlogOpen, setIsBlogOpen] = useState(false);

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);

  const handleLanguageChange = (lang: 'en' | 'ar') => {
    setLanguage(lang);
    setIsMenuOpen(false);
  };

  return (
    <header className="bg-white shadow-lg fixed w-full top-0 z-50">
      <div className="container mx-auto px-4">


        {/* Main navigation */}
        <nav className="flex items-center justify-between py-4">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center">
              <div className="flex items-center">
                {/* Infinity Symbol */}
                <div className="relative w-10 h-6 mr-3">
                  <svg viewBox="0 0 100 40" className="w-full h-full">
                    <path
                      d="M20 20 C20 10, 30 10, 40 20 C50 30, 60 30, 70 20 C80 10, 90 10, 90 20 C90 30, 80 30, 70 20 C60 10, 50 10, 40 20 C30 30, 20 30, 20 20 Z"
                      fill="url(#gradient)"
                      stroke="none"
                    />
                    <defs>
                      <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                        <stop offset="0%" stopColor="#8B5CF6" />
                        <stop offset="100%" stopColor="#3B82F6" />
                      </linearGradient>
                    </defs>
                  </svg>
                </div>
                {/* Text */}
                <span className="text-2xl font-bold text-gray-800">
                  Serv Infinite
                </span>
              </div>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className={`hidden lg:flex items-center ${isRTL ? 'gap-8 flex-row-reverse' : 'space-x-8'}`}>
            <Link href="/" className="text-gray-700 hover:text-purple-600 transition-colors">
              {t('header.home')}
            </Link>

            <Link href="/about" className="text-gray-700 hover:text-purple-600 transition-colors">
              {t('header.about')}
            </Link>

            {/* Services Dropdown */}
            <div
              className="relative group"
              onMouseEnter={() => setIsServicesOpen(true)}
              onMouseLeave={() => setIsServicesOpen(false)}
            >
              <button
                className="text-gray-700 hover:text-purple-600 transition-colors flex items-center py-2"
              >
                {t('header.services')}
                <svg className={`w-4 h-4 ${isRTL ? 'mr-1' : 'ml-1'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>

              {isServicesOpen && (
                <div
                  className={`absolute top-full mt-0 w-64 bg-white shadow-lg rounded-lg py-2 z-50 border ${isRTL ? 'right-0' : 'left-0'}`}
                >
                  <Link href="/services/digital-marketing" className="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600 transition-colors">
                    {t('services.digitalMarketing')}
                  </Link>
                  <Link href="/services/seo" className="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600 transition-colors">
                    {t('services.seo')}
                  </Link>
                  <Link href="/services/graphic-design" className="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600 transition-colors">
                    {t('services.graphicDesign')}
                  </Link>
                  <Link href="/services/social-media" className="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600 transition-colors">
                    {t('services.socialMedia')}
                  </Link>
                  <Link href="/services/branding" className="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600 transition-colors">
                    {t('services.branding')}
                  </Link>
                  <Link href="/services/influencer-marketing" className="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600 transition-colors">
                    {t('services.influencerMarketing')}
                  </Link>
                  <Link href="/services/content-creation" className="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600 transition-colors">
                    {t('services.contentCreation')}
                  </Link>
                  <Link href="/services/marketing-strategy" className="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600 transition-colors">
                    {t('services.marketingStrategy')}
                  </Link>
                </div>
              )}
            </div>

            {/* Portfolio Dropdown */}
            <div
              className="relative group"
              onMouseEnter={() => setIsPortfolioOpen(true)}
              onMouseLeave={() => setIsPortfolioOpen(false)}
            >
              <button
                className="text-gray-700 hover:text-purple-600 transition-colors flex items-center py-2"
              >
                {t('header.portfolio')}
                <svg className={`w-4 h-4 ${isRTL ? 'mr-1' : 'ml-1'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>

              {isPortfolioOpen && (
                <div
                  className={`absolute top-full mt-0 w-56 bg-white shadow-lg rounded-lg py-2 z-50 border ${isRTL ? 'right-0' : 'left-0'}`}
                >
                  <Link href="/portfolio/social-media" className="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600 transition-colors">
                    {t('portfolio.categories.socialMedia')}
                  </Link>
                  <Link href="/portfolio/branding" className="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600 transition-colors">
                    {t('portfolio.categories.brandIdentity')}
                  </Link>
                  <Link href="/portfolio/graphic-design" className="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600 transition-colors">
                    {t('portfolio.categories.graphicDesign')}
                  </Link>
                  <Link href="/portfolio/landing-pages" className="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600 transition-colors">
                    {t('portfolio.categories.landingPages')}
                  </Link>
                  <Link href="/portfolio/marketing" className="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600 transition-colors">
                    {t('portfolio.categories.marketingCampaigns')}
                  </Link>
                </div>
              )}
            </div>

            <Link href="/clients" className="text-gray-700 hover:text-purple-600 transition-colors">
              {t('header.clients')}
            </Link>

            {/* Blog Dropdown */}
            <div
              className="relative group"
              onMouseEnter={() => setIsBlogOpen(true)}
              onMouseLeave={() => setIsBlogOpen(false)}
            >
              <button
                className="text-gray-700 hover:text-purple-600 transition-colors flex items-center py-2"
              >
                {t('header.blog')}
                <svg className={`w-4 h-4 ${isRTL ? 'mr-1' : 'ml-1'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>

              {isBlogOpen && (
                <div
                  className={`absolute top-full mt-0 w-56 bg-white shadow-lg rounded-lg py-2 z-50 border ${isRTL ? 'right-0' : 'left-0'}`}
                >
                  <Link href="/blog/social-media-marketing" className="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600 transition-colors">
                    {t('blog.categories.marketing')} - {t('portfolio.categories.socialMedia')}
                  </Link>
                  <Link href="/blog/search-engine-marketing" className="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600 transition-colors">
                    {t('services.seo')}
                  </Link>
                  <Link href="/blog/video-marketing" className="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600 transition-colors">
                    {t('services.video.title')}
                  </Link>
                  <Link href="/blog/digital-marketing" className="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600 transition-colors">
                    {t('services.digitalMarketing')}
                  </Link>
                  <Link href="/blog/graphic-design" className="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600 transition-colors">
                    {t('services.graphicDesign')}
                  </Link>
                  <Link href="/blog/seo" className="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600 transition-colors">
                    {t('blog.categories.seo')}
                  </Link>
                </div>
              )}
            </div>

            <Link href="/case-studies" className="text-gray-700 hover:text-purple-600 transition-colors">
              {t('header.caseStudies')}
            </Link>

            <Link href="/contact" className="text-gray-700 hover:text-purple-600 transition-colors">
              {t('header.contact')}
            </Link>
          </div>

          {/* Language Selection & CTA Button */}
          <div className="hidden lg:flex items-center gap-4">
            {/* Language Selection */}
            <div className="flex items-center gap-3">
              <button
                onClick={() => handleLanguageChange('ar')}
                className={`p-2 rounded-full transition-all duration-300 ${language === 'ar' ? 'bg-purple-100 scale-110 ring-2 ring-purple-300' : 'hover:bg-gray-100'}`}
                title="العربية"
              >
                <span className="text-2xl">🇸🇦</span>
              </button>
              <button
                onClick={() => handleLanguageChange('en')}
                className={`p-2 rounded-full transition-all duration-300 ${language === 'en' ? 'bg-purple-100 scale-110 ring-2 ring-purple-300' : 'hover:bg-gray-100'}`}
                title="English"
              >
                <span className="text-2xl">🇺🇸</span>
              </button>
            </div>

            {/* CTA Button */}
            <Link
              href="/consultation"
              className="bg-gradient-to-r from-purple-600 to-pink-500 text-white px-6 py-2 rounded-full hover:from-purple-700 hover:to-pink-600 transition-all duration-300 transform hover:scale-105"
            >
              {t('header.consultation')}
            </Link>
          </div>

          {/* Mobile menu button */}
          <button
            className="lg:hidden flex items-center px-3 py-2 border rounded text-gray-500 border-gray-600 hover:text-gray-700 hover:border-gray-700"
            onClick={toggleMenu}
          >
            <svg className="fill-current h-3 w-3" viewBox="0 0 20 20">
              <title>Menu</title>
              <path d="M0 3h20v2H0V3zm0 6h20v2H0V9zm0 6h20v2H0v-2z"/>
            </svg>
          </button>
        </nav>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="lg:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t">
              <Link href="/" className="block px-3 py-2 text-gray-700 hover:text-purple-600" onClick={() => setIsMenuOpen(false)}>
                {t('header.home')}
              </Link>
              <Link href="/about" className="block px-3 py-2 text-gray-700 hover:text-purple-600" onClick={() => setIsMenuOpen(false)}>
                {t('header.about')}
              </Link>
              <Link href="/services" className="block px-3 py-2 text-gray-700 hover:text-purple-600" onClick={() => setIsMenuOpen(false)}>
                {t('header.services')}
              </Link>
              <Link href="/portfolio" className="block px-3 py-2 text-gray-700 hover:text-purple-600" onClick={() => setIsMenuOpen(false)}>
                {t('header.portfolio')}
              </Link>
              <Link href="/clients" className="block px-3 py-2 text-gray-700 hover:text-purple-600" onClick={() => setIsMenuOpen(false)}>
                {t('header.clients')}
              </Link>
              <Link href="/blog" className="block px-3 py-2 text-gray-700 hover:text-purple-600" onClick={() => setIsMenuOpen(false)}>
                {t('header.blog')}
              </Link>
              <Link href="/case-studies" className="block px-3 py-2 text-gray-700 hover:text-purple-600" onClick={() => setIsMenuOpen(false)}>
                {t('header.caseStudies')}
              </Link>
              <Link href="/contact" className="block px-3 py-2 text-gray-700 hover:text-purple-600" onClick={() => setIsMenuOpen(false)}>
                {t('header.contact')}
              </Link>

              {/* Language Selection for Mobile */}
              <div className="px-3 py-2 border-t border-gray-200 mt-4">
                <div className="flex justify-center gap-6">
                  <button
                    onClick={() => handleLanguageChange('ar')}
                    className={`p-3 rounded-full transition-all duration-300 ${language === 'ar' ? 'bg-purple-100 scale-110 ring-2 ring-purple-300' : 'hover:bg-gray-100'}`}
                    title="العربية"
                  >
                    <span className="text-3xl">🇸🇦</span>
                  </button>
                  <button
                    onClick={() => handleLanguageChange('en')}
                    className={`p-3 rounded-full transition-all duration-300 ${language === 'en' ? 'bg-purple-100 scale-110 ring-2 ring-purple-300' : 'hover:bg-gray-100'}`}
                    title="English"
                  >
                    <span className="text-3xl">🇺🇸</span>
                  </button>
                </div>
              </div>

              <Link
                href="/consultation"
                className="block mx-3 mt-4 bg-gradient-to-r from-purple-600 to-pink-500 text-white px-4 py-2 rounded-full text-center"
                onClick={() => setIsMenuOpen(false)}
              >
                {t('header.consultation')}
              </Link>
            </div>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;
