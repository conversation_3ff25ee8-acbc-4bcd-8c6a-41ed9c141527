'use client';

import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useLanguage } from '@/contexts/LanguageContext';

const Header = () => {
  const { language, setLanguage, t, isRTL } = useLanguage();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isServicesOpen, setIsServicesOpen] = useState(false);
  const [isPortfolioOpen, setIsPortfolioOpen] = useState(false);
  const [isBlogOpen, setIsBlogOpen] = useState(false);

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);

  const handleLanguageChange = (lang: 'en' | 'ar') => {
    setLanguage(lang);
    setIsMenuOpen(false);
  };

  return (
    <header className="bg-white shadow-lg fixed w-full top-0 z-50">
      <div className="container mx-auto px-4">
        {/* Top bar with contact info */}
        <div className={`hidden md:flex py-2 text-sm text-gray-600 ${isRTL ? 'justify-start' : 'justify-end'}`}>
          <div className={`flex items-center ${isRTL ? 'space-x-reverse space-x-4' : 'space-x-4'}`}>
            <span>{t('header.consultation')}</span>
            <div className={`flex ${isRTL ? 'space-x-reverse space-x-2' : 'space-x-2'}`}>
              <button
                onClick={() => handleLanguageChange('ar')}
                className={`flex items-center ${language === 'ar' ? 'font-bold text-purple-600' : 'hover:text-purple-600'}`}
              >
                <span className="text-lg mr-1">🇸🇦</span>
                <span>{t('header.language.arabic')}</span>
              </button>
              <span>|</span>
              <button
                onClick={() => handleLanguageChange('en')}
                className={`flex items-center ${language === 'en' ? 'font-bold text-purple-600' : 'hover:text-purple-600'}`}
              >
                <span className="text-lg mr-1">🇺🇸</span>
                <span>{t('header.language.english')}</span>
              </button>
            </div>
          </div>
        </div>

        {/* Main navigation */}
        <nav className="flex items-center justify-between py-4">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/" className="text-2xl font-bold text-purple-800">
              Serv Infinity
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className={`hidden lg:flex items-center ${isRTL ? 'space-x-reverse space-x-8' : 'space-x-8'}`}>
            <Link href="/" className="text-gray-700 hover:text-purple-600 transition-colors">
              {t('header.home')}
            </Link>

            <Link href="/about" className="text-gray-700 hover:text-purple-600 transition-colors">
              {t('header.about')}
            </Link>

            {/* Services Dropdown */}
            <div className="relative group">
              <button
                className="text-gray-700 hover:text-purple-600 transition-colors flex items-center"
                onMouseEnter={() => setIsServicesOpen(true)}
                onMouseLeave={() => setIsServicesOpen(false)}
              >
                {t('header.services')}
                <svg className={`w-4 h-4 ${isRTL ? 'mr-1' : 'ml-1'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              
              {isServicesOpen && (
                <div
                  className={`absolute top-full mt-2 w-64 bg-white shadow-lg rounded-lg py-2 z-50 ${isRTL ? 'right-0' : 'left-0'}`}
                  onMouseEnter={() => setIsServicesOpen(true)}
                  onMouseLeave={() => setIsServicesOpen(false)}
                >
                  <Link href="/services/digital-marketing" className="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600">
                    {t('services.digitalMarketing')}
                  </Link>
                  <Link href="/services/seo" className="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600">
                    {t('services.seo')}
                  </Link>
                  <Link href="/services/graphic-design" className="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600">
                    {t('services.graphicDesign')}
                  </Link>
                  <Link href="/services/social-media" className="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600">
                    {t('services.socialMedia')}
                  </Link>
                  <Link href="/services/branding" className="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600">
                    {t('services.branding')}
                  </Link>
                  <Link href="/services/influencer-marketing" className="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600">
                    {t('services.influencerMarketing')}
                  </Link>
                  <Link href="/services/content-creation" className="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600">
                    {t('services.contentCreation')}
                  </Link>
                  <Link href="/services/marketing-strategy" className="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600">
                    {t('services.marketingStrategy')}
                  </Link>
                </div>
              )}
            </div>

            {/* Portfolio Dropdown */}
            <div className="relative group">
              <button
                className="text-gray-700 hover:text-purple-600 transition-colors flex items-center"
                onMouseEnter={() => setIsPortfolioOpen(true)}
                onMouseLeave={() => setIsPortfolioOpen(false)}
              >
                {t('header.portfolio')}
                <svg className={`w-4 h-4 ${isRTL ? 'mr-1' : 'ml-1'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              
              {isPortfolioOpen && (
                <div
                  className={`absolute top-full mt-2 w-56 bg-white shadow-lg rounded-lg py-2 z-50 ${isRTL ? 'right-0' : 'left-0'}`}
                  onMouseEnter={() => setIsPortfolioOpen(true)}
                  onMouseLeave={() => setIsPortfolioOpen(false)}
                >
                  <Link href="/portfolio/social-media" className="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600">
                    {t('portfolio.categories.socialMedia')}
                  </Link>
                  <Link href="/portfolio/branding" className="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600">
                    {t('portfolio.categories.brandIdentity')}
                  </Link>
                  <Link href="/portfolio/graphic-design" className="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600">
                    {t('portfolio.categories.graphicDesign')}
                  </Link>
                  <Link href="/portfolio/landing-pages" className="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600">
                    {t('portfolio.categories.landingPages')}
                  </Link>
                  <Link href="/portfolio/marketing" className="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600">
                    {t('portfolio.categories.marketingCampaigns')}
                  </Link>
                </div>
              )}
            </div>

            <Link href="/clients" className="text-gray-700 hover:text-purple-600 transition-colors">
              {t('header.clients')}
            </Link>

            {/* Blog Dropdown */}
            <div className="relative group">
              <button
                className="text-gray-700 hover:text-purple-600 transition-colors flex items-center"
                onMouseEnter={() => setIsBlogOpen(true)}
                onMouseLeave={() => setIsBlogOpen(false)}
              >
                {t('header.blog')}
                <svg className={`w-4 h-4 ${isRTL ? 'mr-1' : 'ml-1'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              
              {isBlogOpen && (
                <div 
                  className="absolute top-full left-0 mt-2 w-56 bg-white shadow-lg rounded-lg py-2 z-50"
                  onMouseEnter={() => setIsBlogOpen(true)}
                  onMouseLeave={() => setIsBlogOpen(false)}
                >
                  <Link href="/blog/social-media-marketing" className="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600">
                    Social Media Marketing
                  </Link>
                  <Link href="/blog/search-engine-marketing" className="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600">
                    Search Engine Marketing
                  </Link>
                  <Link href="/blog/video-marketing" className="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600">
                    Video Marketing
                  </Link>
                  <Link href="/blog/digital-marketing" className="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600">
                    Digital Marketing
                  </Link>
                  <Link href="/blog/graphic-design" className="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600">
                    Graphic Design
                  </Link>
                  <Link href="/blog/seo" className="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600">
                    SEO
                  </Link>
                </div>
              )}
            </div>

            <Link href="/case-studies" className="text-gray-700 hover:text-purple-600 transition-colors">
              {t('header.caseStudies')}
            </Link>

            <Link href="/contact" className="text-gray-700 hover:text-purple-600 transition-colors">
              {t('header.contact')}
            </Link>
          </div>

          {/* CTA Button */}
          <div className="hidden lg:flex">
            <Link
              href="/consultation"
              className="bg-gradient-to-r from-purple-600 to-pink-500 text-white px-6 py-2 rounded-full hover:from-purple-700 hover:to-pink-600 transition-all duration-300 transform hover:scale-105"
            >
              {t('header.consultation')}
            </Link>
          </div>

          {/* Mobile menu button */}
          <button
            className="lg:hidden flex items-center px-3 py-2 border rounded text-gray-500 border-gray-600 hover:text-gray-700 hover:border-gray-700"
            onClick={toggleMenu}
          >
            <svg className="fill-current h-3 w-3" viewBox="0 0 20 20">
              <title>Menu</title>
              <path d="M0 3h20v2H0V3zm0 6h20v2H0V9zm0 6h20v2H0v-2z"/>
            </svg>
          </button>
        </nav>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="lg:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t">
              <Link href="/" className="block px-3 py-2 text-gray-700 hover:text-purple-600" onClick={() => setIsMenuOpen(false)}>
                {t('header.home')}
              </Link>
              <Link href="/about" className="block px-3 py-2 text-gray-700 hover:text-purple-600" onClick={() => setIsMenuOpen(false)}>
                {t('header.about')}
              </Link>
              <Link href="/services" className="block px-3 py-2 text-gray-700 hover:text-purple-600" onClick={() => setIsMenuOpen(false)}>
                {t('header.services')}
              </Link>
              <Link href="/portfolio" className="block px-3 py-2 text-gray-700 hover:text-purple-600" onClick={() => setIsMenuOpen(false)}>
                {t('header.portfolio')}
              </Link>
              <Link href="/clients" className="block px-3 py-2 text-gray-700 hover:text-purple-600" onClick={() => setIsMenuOpen(false)}>
                {t('header.clients')}
              </Link>
              <Link href="/blog" className="block px-3 py-2 text-gray-700 hover:text-purple-600" onClick={() => setIsMenuOpen(false)}>
                {t('header.blog')}
              </Link>
              <Link href="/case-studies" className="block px-3 py-2 text-gray-700 hover:text-purple-600" onClick={() => setIsMenuOpen(false)}>
                {t('header.caseStudies')}
              </Link>
              <Link href="/contact" className="block px-3 py-2 text-gray-700 hover:text-purple-600" onClick={() => setIsMenuOpen(false)}>
                {t('header.contact')}
              </Link>

              {/* Language Selection for Mobile */}
              <div className="px-3 py-2 border-t border-gray-200 mt-4">
                <div className="flex space-x-4">
                  <button
                    onClick={() => handleLanguageChange('ar')}
                    className={`flex items-center ${language === 'ar' ? 'font-bold text-purple-600' : 'text-gray-700'}`}
                  >
                    <span className="text-lg mr-1">🇸🇦</span>
                    <span>{t('header.language.arabic')}</span>
                  </button>
                  <button
                    onClick={() => handleLanguageChange('en')}
                    className={`flex items-center ${language === 'en' ? 'font-bold text-purple-600' : 'text-gray-700'}`}
                  >
                    <span className="text-lg mr-1">🇺🇸</span>
                    <span>{t('header.language.english')}</span>
                  </button>
                </div>
              </div>

              <Link
                href="/consultation"
                className="block mx-3 mt-4 bg-gradient-to-r from-purple-600 to-pink-500 text-white px-4 py-2 rounded-full text-center"
                onClick={() => setIsMenuOpen(false)}
              >
                {t('header.consultation')}
              </Link>
            </div>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;
