{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/agency/apollo-agency-website/src/app/services/digital-marketing/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useLanguage } from '@/contexts/LanguageContext';\nimport Link from 'next/link';\n\nexport default function DigitalMarketingPage() {\n  const { t, isRTL } = useLanguage();\n\n  return (\n    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : ''}`}>\n      {/* Hero Section */}\n      <section className=\"bg-gradient-to-br from-blue-900 via-blue-800 to-purple-600 text-white py-20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-4xl mx-auto text-center\">\n            <div className=\"w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6\">\n              <span className=\"text-3xl\">📱</span>\n            </div>\n            <h1 className=\"text-4xl md:text-6xl font-bold mb-6\">\n              {t('services.digitalMarketing')}\n            </h1>\n            <p className=\"text-xl md:text-2xl mb-8 opacity-90\">\n              Transform your business with comprehensive digital marketing strategies\n            </p>\n            <Link\n              href=\"/contact\"\n              className=\"inline-block bg-gradient-to-r from-yellow-400 to-orange-500 text-gray-900 font-bold px-8 py-4 rounded-full hover:from-yellow-300 hover:to-orange-400 transition-all duration-300 transform hover:scale-105\"\n            >\n              Get Started Today\n            </Link>\n          </div>\n        </div>\n      </section>\n\n      {/* Services Overview */}\n      <section className=\"py-20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-6xl mx-auto\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-6\">\n                Our Digital Marketing Services\n              </h2>\n              <p className=\"text-xl text-gray-600\">\n                Comprehensive solutions to boost your online presence\n              </p>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n              {[\n                {\n                  icon: '📊',\n                  title: 'Social Media Marketing',\n                  description: 'Engage your audience across all major social platforms with targeted content and advertising campaigns.'\n                },\n                {\n                  icon: '🎯',\n                  title: 'Google Ads Management',\n                  description: 'Maximize your ROI with expertly managed Google Ads campaigns that drive qualified traffic.'\n                },\n                {\n                  icon: '📧',\n                  title: 'Email Marketing',\n                  description: 'Build lasting relationships with personalized email campaigns that convert prospects into customers.'\n                },\n                {\n                  icon: '📝',\n                  title: 'Content Marketing',\n                  description: 'Create valuable content that attracts, engages, and converts your target audience.'\n                },\n                {\n                  icon: '📈',\n                  title: 'Analytics & Reporting',\n                  description: 'Track performance and optimize campaigns with detailed analytics and regular reporting.'\n                },\n                {\n                  icon: '🔄',\n                  title: 'Marketing Automation',\n                  description: 'Streamline your marketing processes with automated workflows and lead nurturing.'\n                }\n              ].map((service, index) => (\n                <div key={index} className=\"bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300\">\n                  <div className=\"text-4xl mb-4\">{service.icon}</div>\n                  <h3 className=\"text-xl font-bold text-gray-900 mb-3\">{service.title}</h3>\n                  <p className=\"text-gray-600\">{service.description}</p>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Process Section */}\n      <section className=\"py-20 bg-white\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-4xl mx-auto text-center\">\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-16\">\n              Our Process\n            </h2>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n              {[\n                { step: '01', title: 'Strategy', description: 'We analyze your business and create a custom digital marketing strategy.' },\n                { step: '02', title: 'Implementation', description: 'Our team executes the strategy across all relevant digital channels.' },\n                { step: '03', title: 'Optimization', description: 'We continuously monitor and optimize campaigns for better performance.' },\n                { step: '04', title: 'Reporting', description: 'Regular reports keep you informed about progress and results.' }\n              ].map((item, index) => (\n                <div key={index} className=\"text-center\">\n                  <div className=\"w-16 h-16 bg-gradient-to-r from-purple-600 to-pink-500 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold\">\n                    {item.step}\n                  </div>\n                  <h3 className=\"text-xl font-bold text-gray-900 mb-3\">{item.title}</h3>\n                  <p className=\"text-gray-600\">{item.description}</p>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"bg-gradient-to-r from-purple-600 to-pink-500 text-white py-20\">\n        <div className=\"container mx-auto px-4 text-center\">\n          <h2 className=\"text-3xl md:text-4xl font-bold mb-6\">\n            Ready to Transform Your Digital Presence?\n          </h2>\n          <p className=\"text-xl mb-8 opacity-90 max-w-2xl mx-auto\">\n            Let's discuss how our digital marketing services can help your business grow and reach new heights.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Link\n              href=\"/contact\"\n              className=\"bg-white text-purple-600 font-bold px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 transform hover:scale-105\"\n            >\n              Get Free Consultation\n            </Link>\n            <Link\n              href=\"/portfolio\"\n              className=\"border-2 border-white text-white font-bold px-8 py-4 rounded-full hover:bg-white hover:text-purple-600 transition-all duration-300\"\n            >\n              View Our Work\n            </Link>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;IAE/B,qBACE,6LAAC;QAAI,WAAW,CAAC,wBAAwB,EAAE,QAAQ,QAAQ,IAAI;;0BAE7D,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAAW;;;;;;;;;;;0CAE7B,6LAAC;gCAAG,WAAU;0CACX,EAAE;;;;;;0CAEL,6LAAC;gCAAE,WAAU;0CAAsC;;;;;;0CAGnD,6LAAC,+JAAA,CAAA,UAAI;gCA<PERSON>,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;0BAQP,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAGlE,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAKvC,6LAAC;gCAAI,WAAU;0CACZ;oCACC;wCACE,MAAM;wCACN,OAAO;wCACP,aAAa;oCACf;oCACA;wCACE,MAAM;wCACN,OAAO;wCACP,aAAa;oCACf;oCACA;wCACE,MAAM;wCACN,OAAO;wCACP,aAAa;oCACf;oCACA;wCACE,MAAM;wCACN,OAAO;wCACP,aAAa;oCACf;oCACA;wCACE,MAAM;wCACN,OAAO;wCACP,aAAa;oCACf;oCACA;wCACE,MAAM;wCACN,OAAO;wCACP,aAAa;oCACf;iCACD,CAAC,GAAG,CAAC,CAAC,SAAS,sBACd,6LAAC;wCAAgB,WAAU;;0DACzB,6LAAC;gDAAI,WAAU;0DAAiB,QAAQ,IAAI;;;;;;0DAC5C,6LAAC;gDAAG,WAAU;0DAAwC,QAAQ,KAAK;;;;;;0DACnE,6LAAC;gDAAE,WAAU;0DAAiB,QAAQ,WAAW;;;;;;;uCAHzC;;;;;;;;;;;;;;;;;;;;;;;;;;0BAYpB,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAqD;;;;;;0CAInE,6LAAC;gCAAI,WAAU;0CACZ;oCACC;wCAAE,MAAM;wCAAM,OAAO;wCAAY,aAAa;oCAA2E;oCACzH;wCAAE,MAAM;wCAAM,OAAO;wCAAkB,aAAa;oCAAuE;oCAC3H;wCAAE,MAAM;wCAAM,OAAO;wCAAgB,aAAa;oCAAyE;oCAC3H;wCAAE,MAAM;wCAAM,OAAO;wCAAa,aAAa;oCAAgE;iCAChH,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,6LAAC;wCAAgB,WAAU;;0DACzB,6LAAC;gDAAI,WAAU;0DACZ,KAAK,IAAI;;;;;;0DAEZ,6LAAC;gDAAG,WAAU;0DAAwC,KAAK,KAAK;;;;;;0DAChE,6LAAC;gDAAE,WAAU;0DAAiB,KAAK,WAAW;;;;;;;uCALtC;;;;;;;;;;;;;;;;;;;;;;;;;;0BAcpB,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAsC;;;;;;sCAGpD,6LAAC;4BAAE,WAAU;sCAA4C;;;;;;sCAGzD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GA5IwB;;QACD,sIAAA,CAAA,cAAW;;;KADV", "debugId": null}}]}