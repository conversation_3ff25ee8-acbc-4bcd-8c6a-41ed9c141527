(()=>{var e={};e.id=977,e.ids=[977],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24624:(e,s,r)=>{Promise.resolve().then(r.bind(r,81581))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},38121:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>p,tree:()=>c});var a=r(65239),t=r(48088),l=r(88170),n=r.n(l),i=r(30893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);r.d(s,o);let c={children:["",{children:["contact",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,43839)),"C:\\Users\\<USER>\\Desktop\\agency\\apollo-agency-website\\src\\app\\contact\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Desktop\\agency\\apollo-agency-website\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Desktop\\agency\\apollo-agency-website\\src\\app\\contact\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:t.RouteKind.APP_PAGE,page:"/contact/page",pathname:"/contact",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},43839:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\agency\\\\apollo-agency-website\\\\src\\\\app\\\\contact\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\agency\\apollo-agency-website\\src\\app\\contact\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66480:(e,s,r)=>{Promise.resolve().then(r.bind(r,43839))},70440:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});var a=r(31658);let t=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},79551:e=>{"use strict";e.exports=require("url")},81581:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>n});var a=r(60687),t=r(34393),l=r(43210);function n(){let{t:e,isRTL:s}=(0,t.o)(),[r,n]=(0,l.useState)({name:"",email:"",phone:"",company:"",service:"",message:""}),i=e=>{n({...r,[e.target.name]:e.target.value})};return(0,a.jsxs)("div",{className:`min-h-screen bg-gray-50 ${s?"rtl":""}`,children:[(0,a.jsx)("section",{className:"bg-gradient-to-br from-blue-900 via-purple-800 to-pink-600 text-white py-20",children:(0,a.jsx)("div",{className:"container mx-auto px-4",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto text-center",children:[(0,a.jsx)("h1",{className:"text-4xl md:text-6xl font-bold mb-6",children:e("header.contact")}),(0,a.jsx)("p",{className:"text-xl md:text-2xl mb-8 opacity-90",children:"Ready to transform your digital presence? Let's start the conversation."})]})})}),(0,a.jsx)("section",{className:"py-20",children:(0,a.jsx)("div",{className:"container mx-auto px-4",children:(0,a.jsx)("div",{className:"max-w-6xl mx-auto",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12",children:[(0,a.jsxs)("div",{className:"bg-white rounded-xl p-8 shadow-lg",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Send Us a Message"}),(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),console.log("Form submitted:",r),alert("Thank you for your message! We will get back to you soon.")},className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2",children:"Full Name *"}),(0,a.jsx)("input",{type:"text",id:"name",name:"name",required:!0,value:r.name,onChange:i,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent",placeholder:"Your full name"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address *"}),(0,a.jsx)("input",{type:"email",id:"email",name:"email",required:!0,value:r.email,onChange:i,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent",placeholder:"<EMAIL>"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700 mb-2",children:"Phone Number"}),(0,a.jsx)("input",{type:"tel",id:"phone",name:"phone",value:r.phone,onChange:i,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent",placeholder:"+****************"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"company",className:"block text-sm font-medium text-gray-700 mb-2",children:"Company Name"}),(0,a.jsx)("input",{type:"text",id:"company",name:"company",value:r.company,onChange:i,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent",placeholder:"Your company"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"service",className:"block text-sm font-medium text-gray-700 mb-2",children:"Service Interested In"}),(0,a.jsxs)("select",{id:"service",name:"service",value:r.service,onChange:i,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"",children:"Select a service"}),(0,a.jsx)("option",{value:"digital-marketing",children:"Digital Marketing"}),(0,a.jsx)("option",{value:"seo",children:"SEO Services"}),(0,a.jsx)("option",{value:"branding",children:"Brand Development"}),(0,a.jsx)("option",{value:"social-media",children:"Social Media Management"}),(0,a.jsx)("option",{value:"web-development",children:"Website Development"}),(0,a.jsx)("option",{value:"content-creation",children:"Content Creation"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"message",className:"block text-sm font-medium text-gray-700 mb-2",children:"Message *"}),(0,a.jsx)("textarea",{id:"message",name:"message",required:!0,rows:6,value:r.message,onChange:i,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent",placeholder:"Tell us about your project and goals..."})]}),(0,a.jsx)("button",{type:"submit",className:"w-full bg-gradient-to-r from-purple-600 to-pink-500 text-white font-bold py-4 px-6 rounded-lg hover:from-purple-700 hover:to-pink-600 transition-all duration-300 transform hover:scale-105",children:"Send Message"})]})]}),(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{className:"bg-white rounded-xl p-8 shadow-lg",children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Get In Touch"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center flex-shrink-0",children:(0,a.jsx)("span",{className:"text-xl",children:"\uD83D\uDCDE"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold text-gray-900",children:"Phone"}),(0,a.jsx)("p",{className:"text-gray-600",children:"+90 ************"}),(0,a.jsx)("p",{className:"text-gray-600",children:"+966 537 774 368"})]})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center flex-shrink-0",children:(0,a.jsx)("span",{className:"text-xl",children:"✉️"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold text-gray-900",children:"Email"}),(0,a.jsx)("p",{className:"text-gray-600",children:"<EMAIL>"}),(0,a.jsx)("p",{className:"text-gray-600",children:"<EMAIL>"})]})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center flex-shrink-0",children:(0,a.jsx)("span",{className:"text-xl",children:"\uD83D\uDCAC"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold text-gray-900",children:"WhatsApp"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Quick support available"}),(0,a.jsx)("a",{href:"https://wa.me/905528330233",className:"text-purple-600 hover:text-purple-700 font-medium",children:"Chat with us"})]})]})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-xl p-8 shadow-lg",children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Our Offices"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold text-purple-600 mb-2",children:"Turkey Office"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"\xdcniversite Mah. E-5 Yan Yol \xdczeri, \xc7ınar Sk. No:1 D:4, 34320 Avcılar/İstanbul"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold text-purple-600 mb-2",children:"Saudi Arabia Office"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Jeddah, Al-Mohammadiyah District, Prince Sultan Street, Building 7163, Office 203"})]})]})]})]})]})})})})]})}}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),a=s.X(0,[447,584,658,845],()=>r(38121));module.exports=a})();