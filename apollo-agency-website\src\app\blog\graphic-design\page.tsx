'use client';

import { useLanguage } from '@/contexts/LanguageContext';
import Link from 'next/link';

export default function GraphicDesignBlogPage() {
  const { t, isRTL } = useLanguage();

  const posts = [
    {
      id: 1,
      title: 'Logo Design Trends That Will Define 2024',
      excerpt: 'Explore the latest logo design trends and learn how to create timeless brand identities.',
      author: 'Creative Team',
      date: '2024-01-24',
      readTime: '8 min read',
      category: 'Logo Design',
      image: '🎨'
    },
    {
      id: 2,
      title: 'Color Psychology in Graphic Design: A Complete Guide',
      excerpt: 'Understanding how colors affect emotions and behavior to create more effective designs.',
      author: 'Design Expert',
      date: '2024-01-18',
      readTime: '10 min read',
      category: 'Color Theory',
      image: '🌈'
    },
    {
      id: 3,
      title: 'Typography Best Practices for Digital and Print',
      excerpt: 'Master typography principles to create readable and visually appealing designs.',
      author: 'Typography Pro',
      date: '2024-01-13',
      readTime: '7 min read',
      category: 'Typography',
      image: '📝'
    },
    {
      id: 4,
      title: 'Creating Effective Infographics That Tell a Story',
      excerpt: 'Learn how to design infographics that communicate complex information clearly and engagingly.',
      author: 'Data Viz Expert',
      date: '2024-01-08',
      readTime: '9 min read',
      category: 'Infographics',
      image: '📊'
    },
    {
      id: 5,
      title: 'Brand Identity Design: From Concept to Completion',
      excerpt: 'Step-by-step process for creating comprehensive brand identity systems.',
      author: 'Brand Designer',
      date: '2024-01-03',
      readTime: '12 min read',
      category: 'Brand Identity',
      image: '🏷️'
    },
    {
      id: 6,
      title: 'Packaging Design That Sells: Psychology and Principles',
      excerpt: 'How to create packaging designs that attract customers and drive sales.',
      author: 'Package Designer',
      date: '2023-12-29',
      readTime: '8 min read',
      category: 'Packaging',
      image: '📦'
    }
  ];

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : ''}`}>
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-orange-900 via-red-800 to-pink-600 text-white py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6">
              <span className="text-3xl">🎨</span>
            </div>
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Graphic Design Blog
            </h1>
            <p className="text-xl md:text-2xl mb-8 opacity-90">
              Creative insights and design principles for visual excellence
            </p>
            <Link
              href="/contact"
              className="inline-block bg-gradient-to-r from-yellow-400 to-orange-500 text-gray-900 font-bold px-8 py-4 rounded-full hover:from-yellow-300 hover:to-orange-400 transition-all duration-300 transform hover:scale-105"
            >
              Get Design Help
            </Link>
          </div>
        </div>
      </section>

      {/* Blog Posts Grid */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Latest Graphic Design Articles
              </h2>
              <p className="text-xl text-gray-600">
                Expert insights on design trends and creative techniques
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {posts.map((post) => (
                <article key={post.id} className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300">
                  <div className="bg-gradient-to-r from-orange-100 to-red-100 p-8 text-center">
                    <div className="text-6xl mb-4">{post.image}</div>
                    <span className="bg-white/80 text-orange-600 px-3 py-1 rounded-full text-sm font-medium">
                      {post.category}
                    </span>
                  </div>

                  <div className="p-6">
                    <h3 className="text-xl font-bold text-gray-900 mb-3 line-clamp-2">
                      {post.title}
                    </h3>
                    <p className="text-gray-600 mb-4 line-clamp-3">
                      {post.excerpt}
                    </p>
                    
                    <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                      <span>{post.author}</span>
                      <span>{post.readTime}</span>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500">{post.date}</span>
                      <Link
                        href={`/blog/graphic-design/${post.id}`}
                        className="inline-flex items-center text-orange-600 hover:text-orange-700 font-medium"
                      >
                        Read More
                        <svg className={`w-4 h-4 ${isRTL ? 'mr-1 rotate-180' : 'ml-1'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                      </Link>
                    </div>
                  </div>
                </article>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-gradient-to-r from-orange-600 to-red-500 text-white py-20">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Need Professional Graphic Design?
          </h2>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Let our creative team bring your vision to life with stunning graphic design.
          </p>
          <Link
            href="/contact"
            className="bg-white text-orange-600 font-bold px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 transform hover:scale-105"
          >
            Start Design Project
          </Link>
        </div>
      </section>
    </div>
  );
}
