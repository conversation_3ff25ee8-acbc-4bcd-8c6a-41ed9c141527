'use client';

import { useLanguage } from '@/contexts/LanguageContext';
import Link from 'next/link';

export default function InfluencerMarketingPage() {
  const { t, isRTL } = useLanguage();

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : ''}`}>
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-pink-900 via-purple-800 to-indigo-600 text-white py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6">
              <span className="text-3xl">🤝</span>
            </div>
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              {t('services.influencerMarketing')}
            </h1>
            <p className="text-xl md:text-2xl mb-8 opacity-90">
              Connect with your audience through authentic influencer partnerships
            </p>
            <Link
              href="/contact"
              className="inline-block bg-gradient-to-r from-pink-500 to-purple-500 text-white font-bold px-8 py-4 rounded-full hover:from-pink-400 hover:to-purple-400 transition-all duration-300 transform hover:scale-105"
            >
              Start Influencer Campaign
            </Link>
          </div>
        </div>
      </section>

      {/* Services Overview */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Complete Influencer Marketing Solutions
              </h2>
              <p className="text-xl text-gray-600">
                From strategy to execution, we manage every aspect of your influencer campaigns
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  icon: '🔍',
                  title: 'Influencer Discovery',
                  description: 'Find the perfect influencers for your brand and audience.',
                  features: ['Audience analysis', 'Engagement rate review', 'Brand alignment check', 'Performance history']
                },
                {
                  icon: '🤝',
                  title: 'Campaign Management',
                  description: 'End-to-end campaign management from planning to execution.',
                  features: ['Campaign strategy', 'Content guidelines', 'Timeline management', 'Performance tracking']
                },
                {
                  icon: '📊',
                  title: 'Performance Analytics',
                  description: 'Detailed analytics and reporting on campaign performance.',
                  features: ['Reach & impressions', 'Engagement metrics', 'ROI analysis', 'Audience insights']
                },
                {
                  icon: '💰',
                  title: 'Budget Optimization',
                  description: 'Maximize your ROI with strategic budget allocation.',
                  features: ['Cost negotiation', 'Budget planning', 'Performance optimization', 'ROI maximization']
                },
                {
                  icon: '📱',
                  title: 'Multi-Platform Campaigns',
                  description: 'Campaigns across Instagram, TikTok, YouTube, and more.',
                  features: ['Platform strategy', 'Content adaptation', 'Cross-platform sync', 'Audience targeting']
                },
                {
                  icon: '🎯',
                  title: 'Brand Safety',
                  description: 'Ensure your brand is represented safely and authentically.',
                  features: ['Content approval', 'Brand guidelines', 'Risk assessment', 'Compliance monitoring']
                }
              ].map((service, index) => (
                <div key={index} className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300">
                  <div className="text-4xl mb-4">{service.icon}</div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{service.title}</h3>
                  <p className="text-gray-600 mb-4">{service.description}</p>
                  <ul className="space-y-2">
                    {service.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center text-gray-600 text-sm">
                        <span className="text-pink-600 mr-2">✓</span>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Influencer Types */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Types of Influencer Partnerships
              </h2>
              <p className="text-xl text-gray-600">
                We work with influencers of all sizes to match your goals and budget
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {[
                { 
                  type: 'Nano Influencers', 
                  followers: '1K - 10K',
                  description: 'High engagement rates and authentic connections',
                  icon: '👥'
                },
                { 
                  type: 'Micro Influencers', 
                  followers: '10K - 100K',
                  description: 'Niche expertise with engaged communities',
                  icon: '📱'
                },
                { 
                  type: 'Macro Influencers', 
                  followers: '100K - 1M',
                  description: 'Broad reach with professional content',
                  icon: '🌟'
                },
                { 
                  type: 'Mega Influencers', 
                  followers: '1M+',
                  description: 'Maximum reach and brand awareness',
                  icon: '🚀'
                }
              ].map((influencer, index) => (
                <div key={index} className="text-center p-6 bg-gray-50 rounded-lg">
                  <div className="text-4xl mb-4">{influencer.icon}</div>
                  <h3 className="text-lg font-bold text-gray-900 mb-2">{influencer.type}</h3>
                  <p className="text-pink-600 font-semibold mb-3">{influencer.followers}</p>
                  <p className="text-gray-600 text-sm">{influencer.description}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Campaign Process */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Our Influencer Marketing Process
              </h2>
              <p className="text-xl text-gray-600">
                Strategic approach to influencer marketing that delivers results
              </p>
            </div>
            
            <div className="space-y-8">
              {[
                { 
                  step: '01', 
                  title: 'Strategy Development', 
                  description: 'Define campaign goals, target audience, and key performance indicators.' 
                },
                { 
                  step: '02', 
                  title: 'Influencer Research', 
                  description: 'Identify and vet influencers that align with your brand values and audience.' 
                },
                { 
                  step: '03', 
                  title: 'Campaign Execution', 
                  description: 'Manage partnerships, content creation, and campaign timeline.' 
                },
                { 
                  step: '04', 
                  title: 'Performance Analysis', 
                  description: 'Track results, analyze performance, and optimize for future campaigns.' 
                }
              ].map((item, index) => (
                <div key={index} className="flex items-start space-x-6">
                  <div className="w-16 h-16 bg-gradient-to-r from-pink-600 to-purple-500 text-white rounded-full flex items-center justify-center text-xl font-bold flex-shrink-0">
                    {item.step}
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-3">{item.title}</h3>
                    <p className="text-gray-600 text-lg">{item.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Success Metrics */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Campaign Success Metrics
            </h2>
            <p className="text-xl text-gray-600 mb-12">
              We track what matters most to your business
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
              <div className="text-center">
                <div className="text-4xl md:text-5xl font-bold text-pink-600 mb-2">500%</div>
                <div className="text-gray-600">Average ROI Increase</div>
              </div>
              <div className="text-center">
                <div className="text-4xl md:text-5xl font-bold text-pink-600 mb-2">10M+</div>
                <div className="text-gray-600">Total Reach Generated</div>
              </div>
              <div className="text-center">
                <div className="text-4xl md:text-5xl font-bold text-pink-600 mb-2">85%</div>
                <div className="text-gray-600">Campaign Success Rate</div>
              </div>
              <div className="text-center">
                <div className="text-4xl md:text-5xl font-bold text-pink-600 mb-2">200+</div>
                <div className="text-gray-600">Influencer Partners</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-gradient-to-r from-pink-600 to-purple-500 text-white py-20">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Ready to Launch Your Influencer Campaign?
          </h2>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Connect with your audience through authentic influencer partnerships that drive real results.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/contact"
              className="bg-white text-pink-600 font-bold px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 transform hover:scale-105"
            >
              Start Influencer Campaign
            </Link>
            <Link
              href="/portfolio"
              className="border-2 border-white text-white font-bold px-8 py-4 rounded-full hover:bg-white hover:text-pink-600 transition-all duration-300"
            >
              View Campaign Examples
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
