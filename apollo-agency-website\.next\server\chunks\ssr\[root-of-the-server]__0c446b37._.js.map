{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/agency/apollo-agency-website/src/contexts/LanguageContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, useEffect } from 'react';\n\ntype Language = 'en' | 'ar';\n\ninterface LanguageContextType {\n  language: Language;\n  setLanguage: (lang: Language) => void;\n  t: (key: string) => string;\n  isRTL: boolean;\n}\n\nconst LanguageContext = createContext<LanguageContextType | undefined>(undefined);\n\n// Translations object\nconst translations = {\n  en: {\n    // Header\n    'header.home': 'Home',\n    'header.about': 'About Us',\n    'header.services': 'Our Services',\n    'header.portfolio': 'Portfolio',\n    'header.clients': 'Our Clients',\n    'header.blog': 'Blog',\n    'header.caseStudies': 'Case Studies',\n    'header.contact': 'Contact Us',\n    'header.consultation': 'Get Consultation',\n    'header.language.arabic': 'العربية',\n    'header.language.english': 'English',\n    \n    // Services\n    'services.digitalMarketing': 'Digital Marketing',\n    'services.seo': 'SEO Services',\n    'services.graphicDesign': 'Graphic Design',\n    'services.socialMedia': 'Social Media Management',\n    'services.branding': 'Brand Development',\n    'services.influencerMarketing': 'Influencer Marketing',\n    'services.contentCreation': 'Content Creation',\n    'services.marketingStrategy': 'Marketing Strategy',\n    'services.webDevelopment': 'Website Development',\n    'services.videoProduction': 'Video Production',\n    \n    // Hero Section\n    'hero.title1': 'We Create Together Your Marketing Success Formula',\n    'hero.subtitle1': 'Our services combine expertise and strategic vision to give you outstanding marketing success that achieves your goals and distinguishes you from competitors.',\n    'hero.title2': 'Digital Marketing Excellence',\n    'hero.subtitle2': 'Transform your business with our comprehensive digital marketing strategies designed to maximize your online presence and drive results.',\n    'hero.title3': 'Brand Development & Strategy',\n    'hero.subtitle3': 'Build a powerful brand identity that resonates with your audience and creates lasting connections in the digital landscape.',\n    'hero.cta1': 'Contact Us',\n    'hero.cta2': 'Get Started',\n    'hero.cta3': 'Learn More',\n    'hero.stats.projects': 'Successful Projects',\n    'hero.stats.clients': 'Happy Clients',\n    'hero.stats.experience': 'Years Experience',\n    \n    // Services Section\n    'services.title': 'Everything You Need to Launch Your Business in One Place!',\n    'services.subtitle': 'With Serv Infinity for Marketing Solutions',\n    'services.brandDev.title': 'Brand Development',\n    'services.brandDev.desc': 'We develop unique brands that enhance your digital presence',\n    'services.content.title': 'Content Creation',\n    'services.content.desc': 'We write content that aligns with the products and services offered',\n    'services.influencer.title': 'Influencer Marketing',\n    'services.influencer.desc': 'We ensure successful marketing through influential people',\n    'services.digital.title': 'Digital Marketing',\n    'services.digital.desc': 'We launch carefully studied and focused advertising campaigns to achieve your goals accurately',\n    'services.web.title': 'Website Development',\n    'services.web.desc': 'We design and develop websites using the latest programming technologies',\n    'services.graphic.title': 'Graphic Design',\n    'services.graphic.desc': 'We understand and analyze your needs and provide you with the best designs that serve your business',\n    'services.video.title': 'Video Production',\n    'services.video.desc': 'We shoot and create professional advertising videos',\n    'services.seoServices.title': 'SEO Services',\n    'services.seoServices.desc': 'We improve the appearance of websites on search engines',\n    'services.learnMore': 'Learn More',\n    'services.viewAll': 'View All Services',\n    \n    // Portfolio Section\n    'portfolio.title': 'Some of the Work We Have Executed',\n    'portfolio.subtitle': 'Explore our portfolio of successful projects and see how we\\'ve helped businesses achieve their goals',\n    'portfolio.viewProject': 'View Project',\n    'portfolio.viewFull': 'View Full Portfolio',\n    'portfolio.categories.brandIdentity': 'Brand Identity',\n    'portfolio.categories.socialMedia': 'Social Media',\n    'portfolio.categories.graphicDesign': 'Graphic Design',\n    'portfolio.categories.landingPages': 'Landing Pages',\n    'portfolio.categories.marketingCampaigns': 'Marketing Campaigns',\n    \n    // Clients Section\n    'clients.title': 'Our Clients',\n    'clients.subtitle': 'Trusted by leading companies worldwide',\n    'clients.testimonials.title': 'What Our Clients Say',\n    'clients.testimonials.subtitle': 'Don\\'t just take our word for it - hear from our satisfied clients',\n    'clients.stats.projects': 'Projects Completed',\n    'clients.stats.clients': 'Happy Clients',\n    'clients.stats.experience': 'Years Experience',\n    'clients.stats.satisfaction': 'Client Satisfaction',\n    \n    // Blog Section\n    'blog.title': 'Our Expertise at Your Fingertips',\n    'blog.subtitle': 'Stay updated with the latest insights, tips, and trends in digital marketing',\n    'blog.readMore': 'Read More',\n    'blog.readMoreArticles': 'Read More Articles',\n    'blog.share': 'Share:',\n    'blog.categories.all': 'All',\n    'blog.categories.seo': 'SEO',\n    'blog.categories.marketing': 'Marketing',\n    'blog.categories.design': 'Design',\n    'blog.categories.branding': 'Branding',\n    'blog.categories.strategy': 'Strategy',\n    \n    // Contact Section\n    'contact.title': 'It\\'s Time to Develop Your Investment',\n    'contact.subtitle': 'Contact us now and start your journey to success',\n    'contact.freeConsultation': 'Get Free Consultation',\n    'contact.callNow': 'Call Now: +90 552 833 0233',\n    'contact.whatsapp': 'WhatsApp',\n    'contact.whatsapp.desc': 'Get instant support',\n    'contact.whatsapp.cta': 'Chat Now',\n    'contact.email': 'Email',\n    'contact.email.desc': 'Send us a message',\n    'contact.email.cta': 'Send Email',\n    'contact.schedule': 'Schedule Meeting',\n    'contact.schedule.desc': 'Book a consultation',\n    'contact.schedule.cta': 'Book Now',\n    'contact.support': 'Support Available',\n    \n    // Footer\n    'footer.description': 'We create innovative marketing solutions for your business, to reach a wider segment of customers through effective and carefully studied marketing strategies.',\n    'footer.quickLinks': 'Quick Links',\n    'footer.services': 'Our Services',\n    'footer.contact': 'Contact Information',\n    'footer.turkey': 'Turkey Office',\n    'footer.saudi': 'Saudi Arabia Office',\n    'footer.newsletter.title': 'Ready to Develop Your Investment?',\n    'footer.newsletter.subtitle': 'Contact us now and start your journey to success',\n    'footer.newsletter.placeholder': 'Enter your email address',\n    'footer.newsletter.subscribe': 'Subscribe',\n    'footer.copyright': '© 2024 Serv Infinity. All rights reserved.',\n    'footer.privacy': 'Privacy Policy',\n    'footer.terms': 'Terms of Service',\n    'footer.cookies': 'Cookie Policy',\n    \n    // Common\n    'common.loading': 'Loading...',\n    'common.error': 'Error',\n    'common.success': 'Success',\n  },\n  ar: {\n    // Header\n    'header.home': 'الرئيسية',\n    'header.about': 'من نحن',\n    'header.services': 'خدماتنا',\n    'header.portfolio': 'أعمالنا',\n    'header.clients': 'عملاؤنا',\n    'header.blog': 'المدونة',\n    'header.caseStudies': 'دراسات الحالة',\n    'header.contact': 'اتصل بنا',\n    'header.consultation': 'احصل على استشارة',\n    'header.language.arabic': 'العربية',\n    'header.language.english': 'English',\n    \n    // Services\n    'services.digitalMarketing': 'التسويق الرقمي',\n    'services.seo': 'خدمات تحسين محركات البحث',\n    'services.graphicDesign': 'التصميم الجرافيكي',\n    'services.socialMedia': 'إدارة وسائل التواصل الاجتماعي',\n    'services.branding': 'تطوير العلامة التجارية',\n    'services.influencerMarketing': 'التسويق عبر المؤثرين',\n    'services.contentCreation': 'إنشاء المحتوى',\n    'services.marketingStrategy': 'استراتيجية التسويق',\n    'services.webDevelopment': 'تطوير المواقع الإلكترونية',\n    'services.videoProduction': 'إنتاج الفيديو',\n    \n    // Hero Section\n    'hero.title1': 'نصنع معاً معادلة نجاحك التسويقي',\n    'hero.subtitle1': 'تجمع خدماتنا بين الخبرة والرؤية الاستراتيجية لتمنحك نجاحاً تسويقياً متميزاً يحقق أهدافك ويميزك عن المنافسين.',\n    'hero.title2': 'التميز في التسويق الرقمي',\n    'hero.subtitle2': 'حوّل عملك باستراتيجيات التسويق الرقمي الشاملة المصممة لتعظيم حضورك الرقمي وتحقيق النتائج.',\n    'hero.title3': 'تطوير العلامة التجارية والاستراتيجية',\n    'hero.subtitle3': 'ابنِ هوية علامة تجارية قوية تتردد صداها مع جمهورك وتخلق روابط دائمة في المشهد الرقمي.',\n    'hero.cta1': 'اتصل بنا',\n    'hero.cta2': 'ابدأ الآن',\n    'hero.cta3': 'اعرف المزيد',\n    'hero.stats.projects': 'مشروع ناجح',\n    'hero.stats.clients': 'عميل سعيد',\n    'hero.stats.experience': 'سنوات خبرة',\n    \n    // Services Section\n    'services.title': 'كل ما تحتاجه لإطلاق عملك في مكان واحد!',\n    'services.subtitle': 'مع سيرف إنفينيتي للحلول التسويقية',\n    'services.brandDev.title': 'تطوير العلامة التجارية',\n    'services.brandDev.desc': 'نطور علامات تجارية فريدة تعزز حضورك الرقمي',\n    'services.content.title': 'إنشاء المحتوى',\n    'services.content.desc': 'نكتب محتوى يتماشى مع المنتجات والخدمات المقدمة',\n    'services.influencer.title': 'التسويق عبر المؤثرين',\n    'services.influencer.desc': 'نضمن التسويق الناجح من خلال الأشخاص المؤثرين',\n    'services.digital.title': 'التسويق الرقمي',\n    'services.digital.desc': 'نطلق حملات إعلانية مدروسة ومركزة لتحقيق أهدافك بدقة',\n    'services.web.title': 'تطوير المواقع الإلكترونية',\n    'services.web.desc': 'نصمم ونطور المواقع الإلكترونية باستخدام أحدث تقنيات البرمجة',\n    'services.graphic.title': 'التصميم الجرافيكي',\n    'services.graphic.desc': 'نفهم ونحلل احتياجاتك ونقدم لك أفضل التصاميم التي تخدم عملك',\n    'services.video.title': 'إنتاج الفيديو',\n    'services.video.desc': 'نصور وننشئ فيديوهات إعلانية احترافية',\n    'services.seoServices.title': 'خدمات تحسين محركات البحث',\n    'services.seoServices.desc': 'نحسن ظهور المواقع الإلكترونية في محركات البحث',\n    'services.learnMore': 'اعرف المزيد',\n    'services.viewAll': 'عرض جميع الخدمات',\n    \n    // Portfolio Section\n    'portfolio.title': 'بعض الأعمال التي نفذناها',\n    'portfolio.subtitle': 'استكشف محفظة أعمالنا من المشاريع الناجحة وشاهد كيف ساعدنا الشركات في تحقيق أهدافها',\n    'portfolio.viewProject': 'عرض المشروع',\n    'portfolio.viewFull': 'عرض المحفظة كاملة',\n    'portfolio.categories.brandIdentity': 'هوية العلامة التجارية',\n    'portfolio.categories.socialMedia': 'وسائل التواصل الاجتماعي',\n    'portfolio.categories.graphicDesign': 'التصميم الجرافيكي',\n    'portfolio.categories.landingPages': 'صفحات الهبوط',\n    'portfolio.categories.marketingCampaigns': 'الحملات التسويقية',\n    \n    // Clients Section\n    'clients.title': 'عملاؤنا',\n    'clients.subtitle': 'موثوق به من قبل الشركات الرائدة في جميع أنحاء العالم',\n    'clients.testimonials.title': 'ماذا يقول عملاؤنا',\n    'clients.testimonials.subtitle': 'لا تأخذ كلامنا فقط - استمع من عملائنا الراضين',\n    'clients.stats.projects': 'مشروع مكتمل',\n    'clients.stats.clients': 'عميل سعيد',\n    'clients.stats.experience': 'سنوات خبرة',\n    'clients.stats.satisfaction': 'رضا العملاء',\n    \n    // Blog Section\n    'blog.title': 'خبرتنا في متناول يدك',\n    'blog.subtitle': 'ابق على اطلاع بأحدث الرؤى والنصائح والاتجاهات في التسويق الرقمي',\n    'blog.readMore': 'اقرأ المزيد',\n    'blog.readMoreArticles': 'اقرأ المزيد من المقالات',\n    'blog.share': 'شارك:',\n    'blog.categories.all': 'الكل',\n    'blog.categories.seo': 'تحسين محركات البحث',\n    'blog.categories.marketing': 'التسويق',\n    'blog.categories.design': 'التصميم',\n    'blog.categories.branding': 'العلامة التجارية',\n    'blog.categories.strategy': 'الاستراتيجية',\n    \n    // Contact Section\n    'contact.title': 'حان الوقت لتطوير استثمارك',\n    'contact.subtitle': 'اتصل بنا الآن وابدأ رحلتك نحو النجاح',\n    'contact.freeConsultation': 'احصل على استشارة مجانية',\n    'contact.callNow': 'اتصل الآن: +90 552 833 0233',\n    'contact.whatsapp': 'واتساب',\n    'contact.whatsapp.desc': 'احصل على دعم فوري',\n    'contact.whatsapp.cta': 'تحدث الآن',\n    'contact.email': 'البريد الإلكتروني',\n    'contact.email.desc': 'أرسل لنا رسالة',\n    'contact.email.cta': 'أرسل بريد إلكتروني',\n    'contact.schedule': 'جدولة اجتماع',\n    'contact.schedule.desc': 'احجز استشارة',\n    'contact.schedule.cta': 'احجز الآن',\n    'contact.support': 'الدعم متاح',\n    \n    // Footer\n    'footer.description': 'نصنع حلول تسويقية مبتكرة لعملك، للوصول إلى شريحة أوسع من العملاء من خلال استراتيجيات تسويقية فعالة ومدروسة بعناية.',\n    'footer.quickLinks': 'روابط سريعة',\n    'footer.services': 'خدماتنا',\n    'footer.contact': 'معلومات الاتصال',\n    'footer.turkey': 'مكتب تركيا',\n    'footer.saudi': 'مكتب المملكة العربية السعودية',\n    'footer.newsletter.title': 'هل أنت مستعد لتطوير استثمارك؟',\n    'footer.newsletter.subtitle': 'اتصل بنا الآن وابدأ رحلتك نحو النجاح',\n    'footer.newsletter.placeholder': 'أدخل عنوان بريدك الإلكتروني',\n    'footer.newsletter.subscribe': 'اشترك',\n    'footer.copyright': '© 2024 سيرف إنفينيتي. جميع الحقوق محفوظة.',\n    'footer.privacy': 'سياسة الخصوصية',\n    'footer.terms': 'شروط الخدمة',\n    'footer.cookies': 'سياسة ملفات تعريف الارتباط',\n    \n    // Common\n    'common.loading': 'جاري التحميل...',\n    'common.error': 'خطأ',\n    'common.success': 'نجح',\n  }\n};\n\nexport const LanguageProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const [language, setLanguage] = useState<Language>('en');\n\n  useEffect(() => {\n    // Load saved language from localStorage\n    const savedLanguage = localStorage.getItem('language') as Language;\n    if (savedLanguage && (savedLanguage === 'en' || savedLanguage === 'ar')) {\n      setLanguage(savedLanguage);\n    }\n  }, []);\n\n  useEffect(() => {\n    // Save language to localStorage and update document direction\n    localStorage.setItem('language', language);\n    document.documentElement.lang = language;\n    document.documentElement.dir = language === 'ar' ? 'rtl' : 'ltr';\n    \n    // Update body class for RTL styling\n    if (language === 'ar') {\n      document.body.classList.add('rtl');\n    } else {\n      document.body.classList.remove('rtl');\n    }\n  }, [language]);\n\n  const t = (key: string): string => {\n    return translations[language][key] || key;\n  };\n\n  const isRTL = language === 'ar';\n\n  return (\n    <LanguageContext.Provider value={{ language, setLanguage, t, isRTL }}>\n      {children}\n    </LanguageContext.Provider>\n  );\n};\n\nexport const useLanguage = () => {\n  const context = useContext(LanguageContext);\n  if (context === undefined) {\n    throw new Error('useLanguage must be used within a LanguageProvider');\n  }\n  return context;\n};\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAaA,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAmC;AAEvE,sBAAsB;AACtB,MAAM,eAAe;IACnB,IAAI;QACF,SAAS;QACT,eAAe;QACf,gBAAgB;QAChB,mBAAmB;QACnB,oBAAoB;QACpB,kBAAkB;QAClB,eAAe;QACf,sBAAsB;QACtB,kBAAkB;QAClB,uBAAuB;QACvB,0BAA0B;QAC1B,2BAA2B;QAE3B,WAAW;QACX,6BAA6B;QAC7B,gBAAgB;QAChB,0BAA0B;QAC1B,wBAAwB;QACxB,qBAAqB;QACrB,gCAAgC;QAChC,4BAA4B;QAC5B,8BAA8B;QAC9B,2BAA2B;QAC3B,4BAA4B;QAE5B,eAAe;QACf,eAAe;QACf,kBAAkB;QAClB,eAAe;QACf,kBAAkB;QAClB,eAAe;QACf,kBAAkB;QAClB,aAAa;QACb,aAAa;QACb,aAAa;QACb,uBAAuB;QACvB,sBAAsB;QACtB,yBAAyB;QAEzB,mBAAmB;QACnB,kBAAkB;QAClB,qBAAqB;QACrB,2BAA2B;QAC3B,0BAA0B;QAC1B,0BAA0B;QAC1B,yBAAyB;QACzB,6BAA6B;QAC7B,4BAA4B;QAC5B,0BAA0B;QAC1B,yBAAyB;QACzB,sBAAsB;QACtB,qBAAqB;QACrB,0BAA0B;QAC1B,yBAAyB;QACzB,wBAAwB;QACxB,uBAAuB;QACvB,8BAA8B;QAC9B,6BAA6B;QAC7B,sBAAsB;QACtB,oBAAoB;QAEpB,oBAAoB;QACpB,mBAAmB;QACnB,sBAAsB;QACtB,yBAAyB;QACzB,sBAAsB;QACtB,sCAAsC;QACtC,oCAAoC;QACpC,sCAAsC;QACtC,qCAAqC;QACrC,2CAA2C;QAE3C,kBAAkB;QAClB,iBAAiB;QACjB,oBAAoB;QACpB,8BAA8B;QAC9B,iCAAiC;QACjC,0BAA0B;QAC1B,yBAAyB;QACzB,4BAA4B;QAC5B,8BAA8B;QAE9B,eAAe;QACf,cAAc;QACd,iBAAiB;QACjB,iBAAiB;QACjB,yBAAyB;QACzB,cAAc;QACd,uBAAuB;QACvB,uBAAuB;QACvB,6BAA6B;QAC7B,0BAA0B;QAC1B,4BAA4B;QAC5B,4BAA4B;QAE5B,kBAAkB;QAClB,iBAAiB;QACjB,oBAAoB;QACpB,4BAA4B;QAC5B,mBAAmB;QACnB,oBAAoB;QACpB,yBAAyB;QACzB,wBAAwB;QACxB,iBAAiB;QACjB,sBAAsB;QACtB,qBAAqB;QACrB,oBAAoB;QACpB,yBAAyB;QACzB,wBAAwB;QACxB,mBAAmB;QAEnB,SAAS;QACT,sBAAsB;QACtB,qBAAqB;QACrB,mBAAmB;QACnB,kBAAkB;QAClB,iBAAiB;QACjB,gBAAgB;QAChB,2BAA2B;QAC3B,8BAA8B;QAC9B,iCAAiC;QACjC,+BAA+B;QAC/B,oBAAoB;QACpB,kBAAkB;QAClB,gBAAgB;QAChB,kBAAkB;QAElB,SAAS;QACT,kBAAkB;QAClB,gBAAgB;QAChB,kBAAkB;IACpB;IACA,IAAI;QACF,SAAS;QACT,eAAe;QACf,gBAAgB;QAChB,mBAAmB;QACnB,oBAAoB;QACpB,kBAAkB;QAClB,eAAe;QACf,sBAAsB;QACtB,kBAAkB;QAClB,uBAAuB;QACvB,0BAA0B;QAC1B,2BAA2B;QAE3B,WAAW;QACX,6BAA6B;QAC7B,gBAAgB;QAChB,0BAA0B;QAC1B,wBAAwB;QACxB,qBAAqB;QACrB,gCAAgC;QAChC,4BAA4B;QAC5B,8BAA8B;QAC9B,2BAA2B;QAC3B,4BAA4B;QAE5B,eAAe;QACf,eAAe;QACf,kBAAkB;QAClB,eAAe;QACf,kBAAkB;QAClB,eAAe;QACf,kBAAkB;QAClB,aAAa;QACb,aAAa;QACb,aAAa;QACb,uBAAuB;QACvB,sBAAsB;QACtB,yBAAyB;QAEzB,mBAAmB;QACnB,kBAAkB;QAClB,qBAAqB;QACrB,2BAA2B;QAC3B,0BAA0B;QAC1B,0BAA0B;QAC1B,yBAAyB;QACzB,6BAA6B;QAC7B,4BAA4B;QAC5B,0BAA0B;QAC1B,yBAAyB;QACzB,sBAAsB;QACtB,qBAAqB;QACrB,0BAA0B;QAC1B,yBAAyB;QACzB,wBAAwB;QACxB,uBAAuB;QACvB,8BAA8B;QAC9B,6BAA6B;QAC7B,sBAAsB;QACtB,oBAAoB;QAEpB,oBAAoB;QACpB,mBAAmB;QACnB,sBAAsB;QACtB,yBAAyB;QACzB,sBAAsB;QACtB,sCAAsC;QACtC,oCAAoC;QACpC,sCAAsC;QACtC,qCAAqC;QACrC,2CAA2C;QAE3C,kBAAkB;QAClB,iBAAiB;QACjB,oBAAoB;QACpB,8BAA8B;QAC9B,iCAAiC;QACjC,0BAA0B;QAC1B,yBAAyB;QACzB,4BAA4B;QAC5B,8BAA8B;QAE9B,eAAe;QACf,cAAc;QACd,iBAAiB;QACjB,iBAAiB;QACjB,yBAAyB;QACzB,cAAc;QACd,uBAAuB;QACvB,uBAAuB;QACvB,6BAA6B;QAC7B,0BAA0B;QAC1B,4BAA4B;QAC5B,4BAA4B;QAE5B,kBAAkB;QAClB,iBAAiB;QACjB,oBAAoB;QACpB,4BAA4B;QAC5B,mBAAmB;QACnB,oBAAoB;QACpB,yBAAyB;QACzB,wBAAwB;QACxB,iBAAiB;QACjB,sBAAsB;QACtB,qBAAqB;QACrB,oBAAoB;QACpB,yBAAyB;QACzB,wBAAwB;QACxB,mBAAmB;QAEnB,SAAS;QACT,sBAAsB;QACtB,qBAAqB;QACrB,mBAAmB;QACnB,kBAAkB;QAClB,iBAAiB;QACjB,gBAAgB;QAChB,2BAA2B;QAC3B,8BAA8B;QAC9B,iCAAiC;QACjC,+BAA+B;QAC/B,oBAAoB;QACpB,kBAAkB;QAClB,gBAAgB;QAChB,kBAAkB;QAElB,SAAS;QACT,kBAAkB;QAClB,gBAAgB;QAChB,kBAAkB;IACpB;AACF;AAEO,MAAM,mBAA4D,CAAC,EAAE,QAAQ,EAAE;IACpF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;IAEnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,wCAAwC;QACxC,MAAM,gBAAgB,aAAa,OAAO,CAAC;QAC3C,IAAI,iBAAiB,CAAC,kBAAkB,QAAQ,kBAAkB,IAAI,GAAG;YACvE,YAAY;QACd;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,8DAA8D;QAC9D,aAAa,OAAO,CAAC,YAAY;QACjC,SAAS,eAAe,CAAC,IAAI,GAAG;QAChC,SAAS,eAAe,CAAC,GAAG,GAAG,aAAa,OAAO,QAAQ;QAE3D,oCAAoC;QACpC,IAAI,aAAa,MAAM;YACrB,SAAS,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;QAC9B,OAAO;YACL,SAAS,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;QACjC;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,IAAI,CAAC;QACT,OAAO,YAAY,CAAC,SAAS,CAAC,IAAI,IAAI;IACxC;IAEA,MAAM,QAAQ,aAAa;IAE3B,qBACE,8OAAC,gBAAgB,QAAQ;QAAC,OAAO;YAAE;YAAU;YAAa;YAAG;QAAM;kBAChE;;;;;;AAGP;AAEO,MAAM,cAAc;IACzB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 354, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/agency/apollo-agency-website/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { useLanguage } from '@/contexts/LanguageContext';\n\nconst Header = () => {\n  const { language, setLanguage, t, isRTL } = useLanguage();\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [isServicesOpen, setIsServicesOpen] = useState(false);\n  const [isPortfolioOpen, setIsPortfolioOpen] = useState(false);\n  const [isBlogOpen, setIsBlogOpen] = useState(false);\n\n  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);\n\n  const handleLanguageChange = (lang: 'en' | 'ar') => {\n    setLanguage(lang);\n    setIsMenuOpen(false);\n  };\n\n  return (\n    <header className=\"bg-white shadow-lg fixed w-full top-0 z-50\">\n      <div className=\"container mx-auto px-4\">\n        {/* Top bar with contact info */}\n        <div className={`hidden md:flex py-2 text-sm text-gray-600 ${isRTL ? 'justify-start' : 'justify-end'}`}>\n          <div className={`flex items-center ${isRTL ? 'space-x-reverse space-x-4' : 'space-x-4'}`}>\n            <span>{t('header.consultation')}</span>\n            <div className={`flex ${isRTL ? 'space-x-reverse space-x-2' : 'space-x-2'}`}>\n              <button\n                onClick={() => handleLanguageChange('ar')}\n                className={`flex items-center ${language === 'ar' ? 'font-bold text-purple-600' : 'hover:text-purple-600'}`}\n              >\n                <span className=\"text-lg mr-1\">🇸🇦</span>\n                <span>{t('header.language.arabic')}</span>\n              </button>\n              <span>|</span>\n              <button\n                onClick={() => handleLanguageChange('en')}\n                className={`flex items-center ${language === 'en' ? 'font-bold text-purple-600' : 'hover:text-purple-600'}`}\n              >\n                <span className=\"text-lg mr-1\">🇺🇸</span>\n                <span>{t('header.language.english')}</span>\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Main navigation */}\n        <nav className=\"flex items-center justify-between py-4\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"text-2xl font-bold text-purple-800\">\n              Serv Infinity\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <div className={`hidden lg:flex items-center ${isRTL ? 'space-x-reverse space-x-8' : 'space-x-8'}`}>\n            <Link href=\"/\" className=\"text-gray-700 hover:text-purple-600 transition-colors\">\n              {t('header.home')}\n            </Link>\n\n            <Link href=\"/about\" className=\"text-gray-700 hover:text-purple-600 transition-colors\">\n              {t('header.about')}\n            </Link>\n\n            {/* Services Dropdown */}\n            <div className=\"relative group\">\n              <button\n                className=\"text-gray-700 hover:text-purple-600 transition-colors flex items-center\"\n                onMouseEnter={() => setIsServicesOpen(true)}\n                onMouseLeave={() => setIsServicesOpen(false)}\n              >\n                {t('header.services')}\n                <svg className={`w-4 h-4 ${isRTL ? 'mr-1' : 'ml-1'}`} fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n                </svg>\n              </button>\n              \n              {isServicesOpen && (\n                <div\n                  className={`absolute top-full mt-2 w-64 bg-white shadow-lg rounded-lg py-2 z-50 ${isRTL ? 'right-0' : 'left-0'}`}\n                  onMouseEnter={() => setIsServicesOpen(true)}\n                  onMouseLeave={() => setIsServicesOpen(false)}\n                >\n                  <Link href=\"/services/digital-marketing\" className=\"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600\">\n                    {t('services.digitalMarketing')}\n                  </Link>\n                  <Link href=\"/services/seo\" className=\"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600\">\n                    {t('services.seo')}\n                  </Link>\n                  <Link href=\"/services/graphic-design\" className=\"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600\">\n                    {t('services.graphicDesign')}\n                  </Link>\n                  <Link href=\"/services/social-media\" className=\"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600\">\n                    {t('services.socialMedia')}\n                  </Link>\n                  <Link href=\"/services/branding\" className=\"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600\">\n                    {t('services.branding')}\n                  </Link>\n                  <Link href=\"/services/influencer-marketing\" className=\"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600\">\n                    {t('services.influencerMarketing')}\n                  </Link>\n                  <Link href=\"/services/content-creation\" className=\"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600\">\n                    {t('services.contentCreation')}\n                  </Link>\n                  <Link href=\"/services/marketing-strategy\" className=\"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600\">\n                    {t('services.marketingStrategy')}\n                  </Link>\n                </div>\n              )}\n            </div>\n\n            {/* Portfolio Dropdown */}\n            <div className=\"relative group\">\n              <button\n                className=\"text-gray-700 hover:text-purple-600 transition-colors flex items-center\"\n                onMouseEnter={() => setIsPortfolioOpen(true)}\n                onMouseLeave={() => setIsPortfolioOpen(false)}\n              >\n                {t('header.portfolio')}\n                <svg className={`w-4 h-4 ${isRTL ? 'mr-1' : 'ml-1'}`} fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n                </svg>\n              </button>\n              \n              {isPortfolioOpen && (\n                <div\n                  className={`absolute top-full mt-2 w-56 bg-white shadow-lg rounded-lg py-2 z-50 ${isRTL ? 'right-0' : 'left-0'}`}\n                  onMouseEnter={() => setIsPortfolioOpen(true)}\n                  onMouseLeave={() => setIsPortfolioOpen(false)}\n                >\n                  <Link href=\"/portfolio/social-media\" className=\"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600\">\n                    {t('portfolio.categories.socialMedia')}\n                  </Link>\n                  <Link href=\"/portfolio/branding\" className=\"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600\">\n                    {t('portfolio.categories.brandIdentity')}\n                  </Link>\n                  <Link href=\"/portfolio/graphic-design\" className=\"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600\">\n                    {t('portfolio.categories.graphicDesign')}\n                  </Link>\n                  <Link href=\"/portfolio/landing-pages\" className=\"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600\">\n                    {t('portfolio.categories.landingPages')}\n                  </Link>\n                  <Link href=\"/portfolio/marketing\" className=\"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600\">\n                    {t('portfolio.categories.marketingCampaigns')}\n                  </Link>\n                </div>\n              )}\n            </div>\n\n            <Link href=\"/clients\" className=\"text-gray-700 hover:text-purple-600 transition-colors\">\n              {t('header.clients')}\n            </Link>\n\n            {/* Blog Dropdown */}\n            <div className=\"relative group\">\n              <button\n                className=\"text-gray-700 hover:text-purple-600 transition-colors flex items-center\"\n                onMouseEnter={() => setIsBlogOpen(true)}\n                onMouseLeave={() => setIsBlogOpen(false)}\n              >\n                {t('header.blog')}\n                <svg className={`w-4 h-4 ${isRTL ? 'mr-1' : 'ml-1'}`} fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n                </svg>\n              </button>\n              \n              {isBlogOpen && (\n                <div \n                  className=\"absolute top-full left-0 mt-2 w-56 bg-white shadow-lg rounded-lg py-2 z-50\"\n                  onMouseEnter={() => setIsBlogOpen(true)}\n                  onMouseLeave={() => setIsBlogOpen(false)}\n                >\n                  <Link href=\"/blog/social-media-marketing\" className=\"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600\">\n                    Social Media Marketing\n                  </Link>\n                  <Link href=\"/blog/search-engine-marketing\" className=\"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600\">\n                    Search Engine Marketing\n                  </Link>\n                  <Link href=\"/blog/video-marketing\" className=\"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600\">\n                    Video Marketing\n                  </Link>\n                  <Link href=\"/blog/digital-marketing\" className=\"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600\">\n                    Digital Marketing\n                  </Link>\n                  <Link href=\"/blog/graphic-design\" className=\"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600\">\n                    Graphic Design\n                  </Link>\n                  <Link href=\"/blog/seo\" className=\"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600\">\n                    SEO\n                  </Link>\n                </div>\n              )}\n            </div>\n\n            <Link href=\"/case-studies\" className=\"text-gray-700 hover:text-purple-600 transition-colors\">\n              {t('header.caseStudies')}\n            </Link>\n\n            <Link href=\"/contact\" className=\"text-gray-700 hover:text-purple-600 transition-colors\">\n              {t('header.contact')}\n            </Link>\n          </div>\n\n          {/* CTA Button */}\n          <div className=\"hidden lg:flex\">\n            <Link\n              href=\"/consultation\"\n              className=\"bg-gradient-to-r from-purple-600 to-pink-500 text-white px-6 py-2 rounded-full hover:from-purple-700 hover:to-pink-600 transition-all duration-300 transform hover:scale-105\"\n            >\n              {t('header.consultation')}\n            </Link>\n          </div>\n\n          {/* Mobile menu button */}\n          <button\n            className=\"lg:hidden flex items-center px-3 py-2 border rounded text-gray-500 border-gray-600 hover:text-gray-700 hover:border-gray-700\"\n            onClick={toggleMenu}\n          >\n            <svg className=\"fill-current h-3 w-3\" viewBox=\"0 0 20 20\">\n              <title>Menu</title>\n              <path d=\"M0 3h20v2H0V3zm0 6h20v2H0V9zm0 6h20v2H0v-2z\"/>\n            </svg>\n          </button>\n        </nav>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"lg:hidden\">\n            <div className=\"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t\">\n              <Link href=\"/\" className=\"block px-3 py-2 text-gray-700 hover:text-purple-600\" onClick={() => setIsMenuOpen(false)}>\n                {t('header.home')}\n              </Link>\n              <Link href=\"/about\" className=\"block px-3 py-2 text-gray-700 hover:text-purple-600\" onClick={() => setIsMenuOpen(false)}>\n                {t('header.about')}\n              </Link>\n              <Link href=\"/services\" className=\"block px-3 py-2 text-gray-700 hover:text-purple-600\" onClick={() => setIsMenuOpen(false)}>\n                {t('header.services')}\n              </Link>\n              <Link href=\"/portfolio\" className=\"block px-3 py-2 text-gray-700 hover:text-purple-600\" onClick={() => setIsMenuOpen(false)}>\n                {t('header.portfolio')}\n              </Link>\n              <Link href=\"/clients\" className=\"block px-3 py-2 text-gray-700 hover:text-purple-600\" onClick={() => setIsMenuOpen(false)}>\n                {t('header.clients')}\n              </Link>\n              <Link href=\"/blog\" className=\"block px-3 py-2 text-gray-700 hover:text-purple-600\" onClick={() => setIsMenuOpen(false)}>\n                {t('header.blog')}\n              </Link>\n              <Link href=\"/case-studies\" className=\"block px-3 py-2 text-gray-700 hover:text-purple-600\" onClick={() => setIsMenuOpen(false)}>\n                {t('header.caseStudies')}\n              </Link>\n              <Link href=\"/contact\" className=\"block px-3 py-2 text-gray-700 hover:text-purple-600\" onClick={() => setIsMenuOpen(false)}>\n                {t('header.contact')}\n              </Link>\n\n              {/* Language Selection for Mobile */}\n              <div className=\"px-3 py-2 border-t border-gray-200 mt-4\">\n                <div className=\"flex space-x-4\">\n                  <button\n                    onClick={() => handleLanguageChange('ar')}\n                    className={`flex items-center ${language === 'ar' ? 'font-bold text-purple-600' : 'text-gray-700'}`}\n                  >\n                    <span className=\"text-lg mr-1\">🇸🇦</span>\n                    <span>{t('header.language.arabic')}</span>\n                  </button>\n                  <button\n                    onClick={() => handleLanguageChange('en')}\n                    className={`flex items-center ${language === 'en' ? 'font-bold text-purple-600' : 'text-gray-700'}`}\n                  >\n                    <span className=\"text-lg mr-1\">🇺🇸</span>\n                    <span>{t('header.language.english')}</span>\n                  </button>\n                </div>\n              </div>\n\n              <Link\n                href=\"/consultation\"\n                className=\"block mx-3 mt-4 bg-gradient-to-r from-purple-600 to-pink-500 text-white px-4 py-2 rounded-full text-center\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                {t('header.consultation')}\n              </Link>\n            </div>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,SAAS;IACb,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,cAAW,AAAD;IACtD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,aAAa,IAAM,cAAc,CAAC;IAExC,MAAM,uBAAuB,CAAC;QAC5B,YAAY;QACZ,cAAc;IAChB;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAW,CAAC,0CAA0C,EAAE,QAAQ,kBAAkB,eAAe;8BACpG,cAAA,8OAAC;wBAAI,WAAW,CAAC,kBAAkB,EAAE,QAAQ,8BAA8B,aAAa;;0CACtF,8OAAC;0CAAM,EAAE;;;;;;0CACT,8OAAC;gCAAI,WAAW,CAAC,KAAK,EAAE,QAAQ,8BAA8B,aAAa;;kDACzE,8OAAC;wCACC,SAAS,IAAM,qBAAqB;wCACpC,WAAW,CAAC,kBAAkB,EAAE,aAAa,OAAO,8BAA8B,yBAAyB;;0DAE3G,8OAAC;gDAAK,WAAU;0DAAe;;;;;;0DAC/B,8OAAC;0DAAM,EAAE;;;;;;;;;;;;kDAEX,8OAAC;kDAAK;;;;;;kDACN,8OAAC;wCACC,SAAS,IAAM,qBAAqB;wCACpC,WAAW,CAAC,kBAAkB,EAAE,aAAa,OAAO,8BAA8B,yBAAyB;;0DAE3G,8OAAC;gDAAK,WAAU;0DAAe;;;;;;0DAC/B,8OAAC;0DAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOjB,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CAAqC;;;;;;;;;;;sCAMhE,8OAAC;4BAAI,WAAW,CAAC,4BAA4B,EAAE,QAAQ,8BAA8B,aAAa;;8CAChG,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CACtB,EAAE;;;;;;8CAGL,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;8CAC3B,EAAE;;;;;;8CAIL,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,WAAU;4CACV,cAAc,IAAM,kBAAkB;4CACtC,cAAc,IAAM,kBAAkB;;gDAErC,EAAE;8DACH,8OAAC;oDAAI,WAAW,CAAC,QAAQ,EAAE,QAAQ,SAAS,QAAQ;oDAAE,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DAC9F,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;;wCAIxE,gCACC,8OAAC;4CACC,WAAW,CAAC,oEAAoE,EAAE,QAAQ,YAAY,UAAU;4CAChH,cAAc,IAAM,kBAAkB;4CACtC,cAAc,IAAM,kBAAkB;;8DAEtC,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAA8B,WAAU;8DAChD,EAAE;;;;;;8DAEL,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAgB,WAAU;8DAClC,EAAE;;;;;;8DAEL,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAA2B,WAAU;8DAC7C,EAAE;;;;;;8DAEL,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAyB,WAAU;8DAC3C,EAAE;;;;;;8DAEL,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAqB,WAAU;8DACvC,EAAE;;;;;;8DAEL,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAiC,WAAU;8DACnD,EAAE;;;;;;8DAEL,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAA6B,WAAU;8DAC/C,EAAE;;;;;;8DAEL,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAA+B,WAAU;8DACjD,EAAE;;;;;;;;;;;;;;;;;;8CAOX,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,WAAU;4CACV,cAAc,IAAM,mBAAmB;4CACvC,cAAc,IAAM,mBAAmB;;gDAEtC,EAAE;8DACH,8OAAC;oDAAI,WAAW,CAAC,QAAQ,EAAE,QAAQ,SAAS,QAAQ;oDAAE,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DAC9F,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;;wCAIxE,iCACC,8OAAC;4CACC,WAAW,CAAC,oEAAoE,EAAE,QAAQ,YAAY,UAAU;4CAChH,cAAc,IAAM,mBAAmB;4CACvC,cAAc,IAAM,mBAAmB;;8DAEvC,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAA0B,WAAU;8DAC5C,EAAE;;;;;;8DAEL,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAsB,WAAU;8DACxC,EAAE;;;;;;8DAEL,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAA4B,WAAU;8DAC9C,EAAE;;;;;;8DAEL,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAA2B,WAAU;8DAC7C,EAAE;;;;;;8DAEL,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAuB,WAAU;8DACzC,EAAE;;;;;;;;;;;;;;;;;;8CAMX,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAC7B,EAAE;;;;;;8CAIL,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,WAAU;4CACV,cAAc,IAAM,cAAc;4CAClC,cAAc,IAAM,cAAc;;gDAEjC,EAAE;8DACH,8OAAC;oDAAI,WAAW,CAAC,QAAQ,EAAE,QAAQ,SAAS,QAAQ;oDAAE,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DAC9F,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;;wCAIxE,4BACC,8OAAC;4CACC,WAAU;4CACV,cAAc,IAAM,cAAc;4CAClC,cAAc,IAAM,cAAc;;8DAElC,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAA+B,WAAU;8DAAyE;;;;;;8DAG7H,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAgC,WAAU;8DAAyE;;;;;;8DAG9H,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAwB,WAAU;8DAAyE;;;;;;8DAGtH,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAA0B,WAAU;8DAAyE;;;;;;8DAGxH,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAuB,WAAU;8DAAyE;;;;;;8DAGrH,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAY,WAAU;8DAAyE;;;;;;;;;;;;;;;;;;8CAOhH,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAgB,WAAU;8CAClC,EAAE;;;;;;8CAGL,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAC7B,EAAE;;;;;;;;;;;;sCAKP,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CAET,EAAE;;;;;;;;;;;sCAKP,8OAAC;4BACC,WAAU;4BACV,SAAS;sCAET,cAAA,8OAAC;gCAAI,WAAU;gCAAuB,SAAQ;;kDAC5C,8OAAC;kDAAM;;;;;;kDACP,8OAAC;wCAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;gBAMb,4BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;gCAAsD,SAAS,IAAM,cAAc;0CACzG,EAAE;;;;;;0CAEL,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAS,WAAU;gCAAsD,SAAS,IAAM,cAAc;0CAC9G,EAAE;;;;;;0CAEL,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAY,WAAU;gCAAsD,SAAS,IAAM,cAAc;0CACjH,EAAE;;;;;;0CAEL,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAa,WAAU;gCAAsD,SAAS,IAAM,cAAc;0CAClH,EAAE;;;;;;0CAEL,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAW,WAAU;gCAAsD,SAAS,IAAM,cAAc;0CAChH,EAAE;;;;;;0CAEL,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAQ,WAAU;gCAAsD,SAAS,IAAM,cAAc;0CAC7G,EAAE;;;;;;0CAEL,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAgB,WAAU;gCAAsD,SAAS,IAAM,cAAc;0CACrH,EAAE;;;;;;0CAEL,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAW,WAAU;gCAAsD,SAAS,IAAM,cAAc;0CAChH,EAAE;;;;;;0CAIL,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS,IAAM,qBAAqB;4CACpC,WAAW,CAAC,kBAAkB,EAAE,aAAa,OAAO,8BAA8B,iBAAiB;;8DAEnG,8OAAC;oDAAK,WAAU;8DAAe;;;;;;8DAC/B,8OAAC;8DAAM,EAAE;;;;;;;;;;;;sDAEX,8OAAC;4CACC,SAAS,IAAM,qBAAqB;4CACpC,WAAW,CAAC,kBAAkB,EAAE,aAAa,OAAO,8BAA8B,iBAAiB;;8DAEnG,8OAAC;oDAAK,WAAU;8DAAe;;;;;;8DAC/B,8OAAC;8DAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;0CAKf,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAE5B,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnB;uCAEe", "debugId": null}}, {"offset": {"line": 1116, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/agency/apollo-agency-website/src/components/Footer.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { useLanguage } from '@/contexts/LanguageContext';\n\nconst Footer = () => {\n  const { t, isRTL } = useLanguage();\n\n  return (\n    <footer className={`bg-gray-900 text-white ${isRTL ? 'rtl' : ''}`}>\n      {/* Main Footer Content */}\n      <div className=\"container mx-auto px-4 py-16\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {/* Company Info */}\n          <div className=\"lg:col-span-1\">\n            <Link href=\"/\" className=\"text-2xl font-bold text-white mb-4 block\">\n              Serv Infinity\n            </Link>\n            <p className=\"text-gray-300 mb-6 leading-relaxed\">\n              {t('footer.description')}\n            </p>\n            <div className=\"flex space-x-4\">\n              <a href=\"#\" className=\"w-10 h-10 bg-purple-600 hover:bg-purple-700 rounded-full flex items-center justify-center transition-colors duration-300\">\n                <span className=\"text-sm\">📘</span>\n              </a>\n              <a href=\"#\" className=\"w-10 h-10 bg-purple-600 hover:bg-purple-700 rounded-full flex items-center justify-center transition-colors duration-300\">\n                <span className=\"text-sm\">📷</span>\n              </a>\n              <a href=\"#\" className=\"w-10 h-10 bg-purple-600 hover:bg-purple-700 rounded-full flex items-center justify-center transition-colors duration-300\">\n                <span className=\"text-sm\">💼</span>\n              </a>\n              <a href=\"#\" className=\"w-10 h-10 bg-purple-600 hover:bg-purple-700 rounded-full flex items-center justify-center transition-colors duration-300\">\n                <span className=\"text-sm\">🐦</span>\n              </a>\n              <a href=\"#\" className=\"w-10 h-10 bg-purple-600 hover:bg-purple-700 rounded-full flex items-center justify-center transition-colors duration-300\">\n                <span className=\"text-sm\">📺</span>\n              </a>\n            </div>\n          </div>\n\n          {/* Quick Links */}\n          <div>\n            <h3 className=\"text-lg font-bold mb-6\">{t('footer.quickLinks')}</h3>\n            <ul className=\"space-y-3\">\n              <li>\n                <Link href=\"/\" className=\"text-gray-300 hover:text-white transition-colors duration-300\">\n                  {t('header.home')}\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/about\" className=\"text-gray-300 hover:text-white transition-colors duration-300\">\n                  {t('header.about')}\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/services\" className=\"text-gray-300 hover:text-white transition-colors duration-300\">\n                  {t('header.services')}\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/portfolio\" className=\"text-gray-300 hover:text-white transition-colors duration-300\">\n                  {t('header.portfolio')}\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/blog\" className=\"text-gray-300 hover:text-white transition-colors duration-300\">\n                  {t('header.blog')}\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/contact\" className=\"text-gray-300 hover:text-white transition-colors duration-300\">\n                  {t('header.contact')}\n                </Link>\n              </li>\n            </ul>\n          </div>\n\n          {/* Services */}\n          <div>\n            <h3 className=\"text-lg font-bold mb-6\">{t('footer.services')}</h3>\n            <ul className=\"space-y-3\">\n              <li>\n                <Link href=\"/services/digital-marketing\" className=\"text-gray-300 hover:text-white transition-colors duration-300\">\n                  {t('services.digitalMarketing')}\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/services/seo\" className=\"text-gray-300 hover:text-white transition-colors duration-300\">\n                  {t('services.seo')}\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/services/graphic-design\" className=\"text-gray-300 hover:text-white transition-colors duration-300\">\n                  {t('services.graphicDesign')}\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/services/social-media\" className=\"text-gray-300 hover:text-white transition-colors duration-300\">\n                  {t('services.socialMedia')}\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/services/branding\" className=\"text-gray-300 hover:text-white transition-colors duration-300\">\n                  {t('services.branding')}\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/services/content-creation\" className=\"text-gray-300 hover:text-white transition-colors duration-300\">\n                  {t('services.contentCreation')}\n                </Link>\n              </li>\n            </ul>\n          </div>\n\n          {/* Contact Info */}\n          <div>\n            <h3 className=\"text-lg font-bold mb-6\">{t('footer.contact')}</h3>\n\n            {/* Turkey Office */}\n            <div className=\"mb-6\">\n              <h4 className=\"font-semibold text-purple-400 mb-2\">{t('footer.turkey')}</h4>\n              <div className=\"space-y-2 text-gray-300\">\n                <p className={`flex items-start ${isRTL ? 'flex-row-reverse' : ''}`}>\n                  <span className={isRTL ? 'ml-2' : 'mr-2'}>📍</span>\n                  <span className=\"text-sm\">Üniversite Mah. E-5 Yan Yol Üzeri, Çınar Sk. No:1 D:4, 34320 Avcılar/İstanbul</span>\n                </p>\n                <p className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>\n                  <span className={isRTL ? 'ml-2' : 'mr-2'}>📞</span>\n                  <span className=\"text-sm\">+90 552 833 0233</span>\n                </p>\n                <p className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>\n                  <span className={isRTL ? 'ml-2' : 'mr-2'}>✉️</span>\n                  <span className=\"text-sm\"><EMAIL></span>\n                </p>\n              </div>\n            </div>\n\n            {/* Saudi Arabia Office */}\n            <div>\n              <h4 className=\"font-semibold text-purple-400 mb-2\">{t('footer.saudi')}</h4>\n              <div className=\"space-y-2 text-gray-300\">\n                <p className={`flex items-start ${isRTL ? 'flex-row-reverse' : ''}`}>\n                  <span className={isRTL ? 'ml-2' : 'mr-2'}>📍</span>\n                  <span className=\"text-sm\">Jeddah, Al-Mohammadiyah District, Prince Sultan Street, Building 7163, Office 203</span>\n                </p>\n                <p className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>\n                  <span className={isRTL ? 'ml-2' : 'mr-2'}>📞</span>\n                  <span className=\"text-sm\">+966 537 774 368</span>\n                </p>\n                <p className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>\n                  <span className={isRTL ? 'ml-2' : 'mr-2'}>✉️</span>\n                  <span className=\"text-sm\"><EMAIL></span>\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Newsletter Section */}\n      <div className=\"bg-gray-800 py-12\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-4xl mx-auto text-center\">\n            <h3 className=\"text-2xl font-bold mb-4\">{t('footer.newsletter.title')}</h3>\n            <p className=\"text-gray-300 mb-8\">{t('footer.newsletter.subtitle')}</p>\n            <div className={`flex flex-col sm:flex-row gap-4 justify-center items-center ${isRTL ? 'sm:flex-row-reverse' : ''}`}>\n              <input\n                type=\"email\"\n                placeholder={t('footer.newsletter.placeholder')}\n                className=\"px-6 py-3 rounded-full text-gray-900 w-full sm:w-auto sm:min-w-[300px] focus:outline-none focus:ring-2 focus:ring-purple-500\"\n              />\n              <button className=\"bg-gradient-to-r from-purple-600 to-pink-500 text-white px-8 py-3 rounded-full font-semibold hover:from-purple-700 hover:to-pink-600 transition-all duration-300 transform hover:scale-105\">\n                {t('footer.newsletter.subscribe')}\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Bottom Footer */}\n      <div className=\"bg-gray-950 py-6\">\n        <div className=\"container mx-auto px-4\">\n          <div className={`flex flex-col md:flex-row justify-between items-center ${isRTL ? 'md:flex-row-reverse' : ''}`}>\n            <div className=\"text-gray-400 text-sm mb-4 md:mb-0\">\n              {t('footer.copyright')}\n            </div>\n            <div className={`flex text-sm ${isRTL ? 'space-x-reverse space-x-6' : 'space-x-6'}`}>\n              <Link href=\"/privacy\" className=\"text-gray-400 hover:text-white transition-colors duration-300\">\n                {t('footer.privacy')}\n              </Link>\n              <Link href=\"/terms\" className=\"text-gray-400 hover:text-white transition-colors duration-300\">\n                {t('footer.terms')}\n              </Link>\n              <Link href=\"/cookies\" className=\"text-gray-400 hover:text-white transition-colors duration-300\">\n                {t('footer.cookies')}\n              </Link>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,SAAS;IACb,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,cAAW,AAAD;IAE/B,qBACE,8OAAC;QAAO,WAAW,CAAC,uBAAuB,EAAE,QAAQ,QAAQ,IAAI;;0BAE/D,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CAA2C;;;;;;8CAGpE,8OAAC;oCAAE,WAAU;8CACV,EAAE;;;;;;8CAEL,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,MAAK;4CAAI,WAAU;sDACpB,cAAA,8OAAC;gDAAK,WAAU;0DAAU;;;;;;;;;;;sDAE5B,8OAAC;4CAAE,MAAK;4CAAI,WAAU;sDACpB,cAAA,8OAAC;gDAAK,WAAU;0DAAU;;;;;;;;;;;sDAE5B,8OAAC;4CAAE,MAAK;4CAAI,WAAU;sDACpB,cAAA,8OAAC;gDAAK,WAAU;0DAAU;;;;;;;;;;;sDAE5B,8OAAC;4CAAE,MAAK;4CAAI,WAAU;sDACpB,cAAA,8OAAC;gDAAK,WAAU;0DAAU;;;;;;;;;;;sDAE5B,8OAAC;4CAAE,MAAK;4CAAI,WAAU;sDACpB,cAAA,8OAAC;gDAAK,WAAU;0DAAU;;;;;;;;;;;;;;;;;;;;;;;sCAMhC,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA0B,EAAE;;;;;;8CAC1C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAI,WAAU;0DACtB,EAAE;;;;;;;;;;;sDAGP,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAC3B,EAAE;;;;;;;;;;;sDAGP,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAY,WAAU;0DAC9B,EAAE;;;;;;;;;;;sDAGP,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAC/B,EAAE;;;;;;;;;;;sDAGP,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAC1B,EAAE;;;;;;;;;;;sDAGP,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAC7B,EAAE;;;;;;;;;;;;;;;;;;;;;;;sCAOX,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA0B,EAAE;;;;;;8CAC1C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAA8B,WAAU;0DAChD,EAAE;;;;;;;;;;;sDAGP,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAgB,WAAU;0DAClC,EAAE;;;;;;;;;;;sDAGP,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAA2B,WAAU;0DAC7C,EAAE;;;;;;;;;;;sDAGP,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAyB,WAAU;0DAC3C,EAAE;;;;;;;;;;;sDAGP,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAqB,WAAU;0DACvC,EAAE;;;;;;;;;;;sDAGP,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAA6B,WAAU;0DAC/C,EAAE;;;;;;;;;;;;;;;;;;;;;;;sCAOX,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA0B,EAAE;;;;;;8CAG1C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAsC,EAAE;;;;;;sDACtD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAW,CAAC,iBAAiB,EAAE,QAAQ,qBAAqB,IAAI;;sEACjE,8OAAC;4DAAK,WAAW,QAAQ,SAAS;sEAAQ;;;;;;sEAC1C,8OAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;8DAE5B,8OAAC;oDAAE,WAAW,CAAC,kBAAkB,EAAE,QAAQ,qBAAqB,IAAI;;sEAClE,8OAAC;4DAAK,WAAW,QAAQ,SAAS;sEAAQ;;;;;;sEAC1C,8OAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;8DAE5B,8OAAC;oDAAE,WAAW,CAAC,kBAAkB,EAAE,QAAQ,qBAAqB,IAAI;;sEAClE,8OAAC;4DAAK,WAAW,QAAQ,SAAS;sEAAQ;;;;;;sEAC1C,8OAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;;;;;;;;;;;;;8CAMhC,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAsC,EAAE;;;;;;sDACtD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAW,CAAC,iBAAiB,EAAE,QAAQ,qBAAqB,IAAI;;sEACjE,8OAAC;4DAAK,WAAW,QAAQ,SAAS;sEAAQ;;;;;;sEAC1C,8OAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;8DAE5B,8OAAC;oDAAE,WAAW,CAAC,kBAAkB,EAAE,QAAQ,qBAAqB,IAAI;;sEAClE,8OAAC;4DAAK,WAAW,QAAQ,SAAS;sEAAQ;;;;;;sEAC1C,8OAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;8DAE5B,8OAAC;oDAAE,WAAW,CAAC,kBAAkB,EAAE,QAAQ,qBAAqB,IAAI;;sEAClE,8OAAC;4DAAK,WAAW,QAAQ,SAAS;sEAAQ;;;;;;sEAC1C,8OAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAStC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA2B,EAAE;;;;;;0CAC3C,8OAAC;gCAAE,WAAU;0CAAsB,EAAE;;;;;;0CACrC,8OAAC;gCAAI,WAAW,CAAC,4DAA4D,EAAE,QAAQ,wBAAwB,IAAI;;kDACjH,8OAAC;wCACC,MAAK;wCACL,aAAa,EAAE;wCACf,WAAU;;;;;;kDAEZ,8OAAC;wCAAO,WAAU;kDACf,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAW,CAAC,uDAAuD,EAAE,QAAQ,wBAAwB,IAAI;;0CAC5G,8OAAC;gCAAI,WAAU;0CACZ,EAAE;;;;;;0CAEL,8OAAC;gCAAI,WAAW,CAAC,aAAa,EAAE,QAAQ,8BAA8B,aAAa;;kDACjF,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAC7B,EAAE;;;;;;kDAEL,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAC3B,EAAE;;;;;;kDAEL,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAC7B,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnB;uCAEe", "debugId": null}}]}