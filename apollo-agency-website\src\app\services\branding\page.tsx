'use client';

import { useLanguage } from '@/contexts/LanguageContext';
import Link from 'next/link';

export default function BrandingPage() {
  const { t, isRTL } = useLanguage();

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : ''}`}>
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-purple-900 via-pink-800 to-red-600 text-white py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6">
              <span className="text-3xl">🎯</span>
            </div>
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              {t('services.branding')}
            </h1>
            <p className="text-xl md:text-2xl mb-8 opacity-90">
              Build a powerful brand identity that resonates with your audience
            </p>
            <Link
              href="/contact"
              className="inline-block bg-gradient-to-r from-yellow-400 to-orange-500 text-gray-900 font-bold px-8 py-4 rounded-full hover:from-yellow-300 hover:to-orange-400 transition-all duration-300 transform hover:scale-105"
            >
              Start Your Brand Journey
            </Link>
          </div>
        </div>
      </section>

      {/* Branding Services */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Complete Brand Development Services
              </h2>
              <p className="text-xl text-gray-600">
                From strategy to visual identity, we create brands that make an impact
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  icon: '🧠',
                  title: 'Brand Strategy',
                  description: 'Define your brand positioning, values, and unique value proposition.',
                  features: ['Brand positioning', 'Target audience analysis', 'Competitive research', 'Brand messaging']
                },
                {
                  icon: '🎨',
                  title: 'Visual Identity',
                  description: 'Create a cohesive visual system that represents your brand.',
                  features: ['Logo design', 'Color palette', 'Typography selection', 'Visual guidelines']
                },
                {
                  icon: '📖',
                  title: 'Brand Guidelines',
                  description: 'Comprehensive guidelines to maintain brand consistency.',
                  features: ['Usage guidelines', 'Do\'s and don\'ts', 'Application examples', 'Brand standards']
                },
                {
                  icon: '💬',
                  title: 'Brand Messaging',
                  description: 'Develop compelling messaging that connects with your audience.',
                  features: ['Brand voice', 'Tone of voice', 'Key messages', 'Tagline development']
                },
                {
                  icon: '📦',
                  title: 'Brand Applications',
                  description: 'Apply your brand across all touchpoints and materials.',
                  features: ['Business cards', 'Letterheads', 'Marketing materials', 'Digital applications']
                },
                {
                  icon: '🔄',
                  title: 'Brand Refresh',
                  description: 'Modernize and revitalize your existing brand identity.',
                  features: ['Brand audit', 'Modernization', 'Evolution strategy', 'Implementation plan']
                }
              ].map((service, index) => (
                <div key={index} className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300">
                  <div className="text-4xl mb-4">{service.icon}</div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{service.title}</h3>
                  <p className="text-gray-600 mb-4">{service.description}</p>
                  <ul className="space-y-2">
                    {service.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center text-gray-600 text-sm">
                        <span className="text-purple-600 mr-2">✓</span>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Process */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Our Branding Process
              </h2>
              <p className="text-xl text-gray-600">
                A systematic approach to building powerful brands
              </p>
            </div>
            
            <div className="space-y-8">
              {[
                { 
                  step: '01', 
                  title: 'Discovery & Research', 
                  description: 'We dive deep into your business, industry, and target audience to understand your unique challenges and opportunities.' 
                },
                { 
                  step: '02', 
                  title: 'Strategy Development', 
                  description: 'Based on our research, we develop a comprehensive brand strategy that defines your positioning and messaging.' 
                },
                { 
                  step: '03', 
                  title: 'Visual Identity Creation', 
                  description: 'Our designers create a visual identity that brings your brand strategy to life through logos, colors, and typography.' 
                },
                { 
                  step: '04', 
                  title: 'Implementation & Launch', 
                  description: 'We help you implement your new brand across all touchpoints and launch it effectively to your audience.' 
                }
              ].map((item, index) => (
                <div key={index} className="flex items-start space-x-6">
                  <div className="w-16 h-16 bg-gradient-to-r from-purple-600 to-pink-500 text-white rounded-full flex items-center justify-center text-xl font-bold flex-shrink-0">
                    {item.step}
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-3">{item.title}</h3>
                    <p className="text-gray-600 text-lg">{item.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-gradient-to-r from-purple-600 to-pink-500 text-white py-20">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Ready to Build a Powerful Brand?
          </h2>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Let's create a brand identity that sets you apart from the competition and connects with your audience.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/contact"
              className="bg-white text-purple-600 font-bold px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 transform hover:scale-105"
            >
              Start Your Brand Project
            </Link>
            <Link
              href="/portfolio/branding"
              className="border-2 border-white text-white font-bold px-8 py-4 rounded-full hover:bg-white hover:text-purple-600 transition-all duration-300"
            >
              View Brand Portfolio
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
