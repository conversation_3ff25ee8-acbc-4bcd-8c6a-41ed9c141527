{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/agency/apollo-agency-website/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\n\nconst Header = () => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [isServicesOpen, setIsServicesOpen] = useState(false);\n  const [isPortfolioOpen, setIsPortfolioOpen] = useState(false);\n  const [isBlogOpen, setIsBlogOpen] = useState(false);\n\n  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);\n\n  return (\n    <header className=\"bg-white shadow-lg fixed w-full top-0 z-50\">\n      <div className=\"container mx-auto px-4\">\n        {/* Top bar with contact info */}\n        <div className=\"hidden md:flex justify-end py-2 text-sm text-gray-600\">\n          <div className=\"flex items-center space-x-4\">\n            <span>Get Consultation</span>\n            <div className=\"flex space-x-2\">\n              <span className=\"fi fi-sa\"></span>\n              <span>العربية</span>\n              <span>|</span>\n              <span className=\"fi fi-us\"></span>\n              <span>English</span>\n            </div>\n          </div>\n        </div>\n\n        {/* Main navigation */}\n        <nav className=\"flex items-center justify-between py-4\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"text-2xl font-bold text-purple-800\">\n              Apollo Agency\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden lg:flex items-center space-x-8\">\n            <Link href=\"/\" className=\"text-gray-700 hover:text-purple-600 transition-colors\">\n              Home\n            </Link>\n            \n            <Link href=\"/about\" className=\"text-gray-700 hover:text-purple-600 transition-colors\">\n              About Us\n            </Link>\n\n            {/* Services Dropdown */}\n            <div className=\"relative group\">\n              <button \n                className=\"text-gray-700 hover:text-purple-600 transition-colors flex items-center\"\n                onMouseEnter={() => setIsServicesOpen(true)}\n                onMouseLeave={() => setIsServicesOpen(false)}\n              >\n                Our Services\n                <svg className=\"ml-1 w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n                </svg>\n              </button>\n              \n              {isServicesOpen && (\n                <div \n                  className=\"absolute top-full left-0 mt-2 w-64 bg-white shadow-lg rounded-lg py-2 z-50\"\n                  onMouseEnter={() => setIsServicesOpen(true)}\n                  onMouseLeave={() => setIsServicesOpen(false)}\n                >\n                  <Link href=\"/services/digital-marketing\" className=\"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600\">\n                    Digital Marketing\n                  </Link>\n                  <Link href=\"/services/seo\" className=\"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600\">\n                    SEO Services\n                  </Link>\n                  <Link href=\"/services/graphic-design\" className=\"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600\">\n                    Graphic Design\n                  </Link>\n                  <Link href=\"/services/social-media\" className=\"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600\">\n                    Social Media Management\n                  </Link>\n                  <Link href=\"/services/branding\" className=\"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600\">\n                    Brand Development\n                  </Link>\n                  <Link href=\"/services/influencer-marketing\" className=\"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600\">\n                    Influencer Marketing\n                  </Link>\n                  <Link href=\"/services/content-creation\" className=\"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600\">\n                    Content Creation\n                  </Link>\n                  <Link href=\"/services/marketing-strategy\" className=\"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600\">\n                    Marketing Strategy\n                  </Link>\n                </div>\n              )}\n            </div>\n\n            {/* Portfolio Dropdown */}\n            <div className=\"relative group\">\n              <button \n                className=\"text-gray-700 hover:text-purple-600 transition-colors flex items-center\"\n                onMouseEnter={() => setIsPortfolioOpen(true)}\n                onMouseLeave={() => setIsPortfolioOpen(false)}\n              >\n                Portfolio\n                <svg className=\"ml-1 w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n                </svg>\n              </button>\n              \n              {isPortfolioOpen && (\n                <div \n                  className=\"absolute top-full left-0 mt-2 w-56 bg-white shadow-lg rounded-lg py-2 z-50\"\n                  onMouseEnter={() => setIsPortfolioOpen(true)}\n                  onMouseLeave={() => setIsPortfolioOpen(false)}\n                >\n                  <Link href=\"/portfolio/social-media\" className=\"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600\">\n                    Social Media Designs\n                  </Link>\n                  <Link href=\"/portfolio/branding\" className=\"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600\">\n                    Brand Identity\n                  </Link>\n                  <Link href=\"/portfolio/graphic-design\" className=\"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600\">\n                    Graphic Design\n                  </Link>\n                  <Link href=\"/portfolio/landing-pages\" className=\"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600\">\n                    Landing Pages\n                  </Link>\n                  <Link href=\"/portfolio/marketing\" className=\"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600\">\n                    Marketing Campaigns\n                  </Link>\n                </div>\n              )}\n            </div>\n\n            <Link href=\"/clients\" className=\"text-gray-700 hover:text-purple-600 transition-colors\">\n              Our Clients\n            </Link>\n\n            {/* Blog Dropdown */}\n            <div className=\"relative group\">\n              <button \n                className=\"text-gray-700 hover:text-purple-600 transition-colors flex items-center\"\n                onMouseEnter={() => setIsBlogOpen(true)}\n                onMouseLeave={() => setIsBlogOpen(false)}\n              >\n                Blog\n                <svg className=\"ml-1 w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n                </svg>\n              </button>\n              \n              {isBlogOpen && (\n                <div \n                  className=\"absolute top-full left-0 mt-2 w-56 bg-white shadow-lg rounded-lg py-2 z-50\"\n                  onMouseEnter={() => setIsBlogOpen(true)}\n                  onMouseLeave={() => setIsBlogOpen(false)}\n                >\n                  <Link href=\"/blog/social-media-marketing\" className=\"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600\">\n                    Social Media Marketing\n                  </Link>\n                  <Link href=\"/blog/search-engine-marketing\" className=\"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600\">\n                    Search Engine Marketing\n                  </Link>\n                  <Link href=\"/blog/video-marketing\" className=\"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600\">\n                    Video Marketing\n                  </Link>\n                  <Link href=\"/blog/digital-marketing\" className=\"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600\">\n                    Digital Marketing\n                  </Link>\n                  <Link href=\"/blog/graphic-design\" className=\"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600\">\n                    Graphic Design\n                  </Link>\n                  <Link href=\"/blog/seo\" className=\"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600\">\n                    SEO\n                  </Link>\n                </div>\n              )}\n            </div>\n\n            <Link href=\"/case-studies\" className=\"text-gray-700 hover:text-purple-600 transition-colors\">\n              Case Studies\n            </Link>\n\n            <Link href=\"/contact\" className=\"text-gray-700 hover:text-purple-600 transition-colors\">\n              Contact Us\n            </Link>\n          </div>\n\n          {/* CTA Button */}\n          <div className=\"hidden lg:flex\">\n            <Link \n              href=\"/consultation\" \n              className=\"bg-gradient-to-r from-purple-600 to-pink-500 text-white px-6 py-2 rounded-full hover:from-purple-700 hover:to-pink-600 transition-all duration-300 transform hover:scale-105\"\n            >\n              Get Consultation\n            </Link>\n          </div>\n\n          {/* Mobile menu button */}\n          <button\n            className=\"lg:hidden flex items-center px-3 py-2 border rounded text-gray-500 border-gray-600 hover:text-gray-700 hover:border-gray-700\"\n            onClick={toggleMenu}\n          >\n            <svg className=\"fill-current h-3 w-3\" viewBox=\"0 0 20 20\">\n              <title>Menu</title>\n              <path d=\"M0 3h20v2H0V3zm0 6h20v2H0V9zm0 6h20v2H0v-2z\"/>\n            </svg>\n          </button>\n        </nav>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"lg:hidden\">\n            <div className=\"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t\">\n              <Link href=\"/\" className=\"block px-3 py-2 text-gray-700 hover:text-purple-600\">\n                Home\n              </Link>\n              <Link href=\"/about\" className=\"block px-3 py-2 text-gray-700 hover:text-purple-600\">\n                About Us\n              </Link>\n              <Link href=\"/services\" className=\"block px-3 py-2 text-gray-700 hover:text-purple-600\">\n                Our Services\n              </Link>\n              <Link href=\"/portfolio\" className=\"block px-3 py-2 text-gray-700 hover:text-purple-600\">\n                Portfolio\n              </Link>\n              <Link href=\"/clients\" className=\"block px-3 py-2 text-gray-700 hover:text-purple-600\">\n                Our Clients\n              </Link>\n              <Link href=\"/blog\" className=\"block px-3 py-2 text-gray-700 hover:text-purple-600\">\n                Blog\n              </Link>\n              <Link href=\"/case-studies\" className=\"block px-3 py-2 text-gray-700 hover:text-purple-600\">\n                Case Studies\n              </Link>\n              <Link href=\"/contact\" className=\"block px-3 py-2 text-gray-700 hover:text-purple-600\">\n                Contact Us\n              </Link>\n              <Link \n                href=\"/consultation\" \n                className=\"block mx-3 mt-4 bg-gradient-to-r from-purple-600 to-pink-500 text-white px-4 py-2 rounded-full text-center\"\n              >\n                Get Consultation\n              </Link>\n            </div>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAMA,MAAM,SAAS;;IACb,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,aAAa,IAAM,cAAc,CAAC;IAExC,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;0CAAK;;;;;;0CACN,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;;;;;;kDAChB,6LAAC;kDAAK;;;;;;kDACN,6LAAC;kDAAK;;;;;;kDACN,6LAAC;wCAAK,WAAU;;;;;;kDAChB,6LAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;8BAMZ,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CAAqC;;;;;;;;;;;sCAMhE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAwD;;;;;;8CAIjF,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;8CAAwD;;;;;;8CAKtF,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,WAAU;4CACV,cAAc,IAAM,kBAAkB;4CACtC,cAAc,IAAM,kBAAkB;;gDACvC;8DAEC,6LAAC;oDAAI,WAAU;oDAAe,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACtE,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;;wCAIxE,gCACC,6LAAC;4CACC,WAAU;4CACV,cAAc,IAAM,kBAAkB;4CACtC,cAAc,IAAM,kBAAkB;;8DAEtC,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAA8B,WAAU;8DAAyE;;;;;;8DAG5H,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAgB,WAAU;8DAAyE;;;;;;8DAG9G,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAA2B,WAAU;8DAAyE;;;;;;8DAGzH,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAyB,WAAU;8DAAyE;;;;;;8DAGvH,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAqB,WAAU;8DAAyE;;;;;;8DAGnH,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAiC,WAAU;8DAAyE;;;;;;8DAG/H,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAA6B,WAAU;8DAAyE;;;;;;8DAG3H,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAA+B,WAAU;8DAAyE;;;;;;;;;;;;;;;;;;8CAQnI,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,WAAU;4CACV,cAAc,IAAM,mBAAmB;4CACvC,cAAc,IAAM,mBAAmB;;gDACxC;8DAEC,6LAAC;oDAAI,WAAU;oDAAe,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACtE,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;;wCAIxE,iCACC,6LAAC;4CACC,WAAU;4CACV,cAAc,IAAM,mBAAmB;4CACvC,cAAc,IAAM,mBAAmB;;8DAEvC,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAA0B,WAAU;8DAAyE;;;;;;8DAGxH,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAsB,WAAU;8DAAyE;;;;;;8DAGpH,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAA4B,WAAU;8DAAyE;;;;;;8DAG1H,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAA2B,WAAU;8DAAyE;;;;;;8DAGzH,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAuB,WAAU;8DAAyE;;;;;;;;;;;;;;;;;;8CAO3H,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAwD;;;;;;8CAKxF,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,WAAU;4CACV,cAAc,IAAM,cAAc;4CAClC,cAAc,IAAM,cAAc;;gDACnC;8DAEC,6LAAC;oDAAI,WAAU;oDAAe,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACtE,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;;wCAIxE,4BACC,6LAAC;4CACC,WAAU;4CACV,cAAc,IAAM,cAAc;4CAClC,cAAc,IAAM,cAAc;;8DAElC,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAA+B,WAAU;8DAAyE;;;;;;8DAG7H,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAgC,WAAU;8DAAyE;;;;;;8DAG9H,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAwB,WAAU;8DAAyE;;;;;;8DAGtH,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAA0B,WAAU;8DAAyE;;;;;;8DAGxH,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAuB,WAAU;8DAAyE;;;;;;8DAGrH,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAY,WAAU;8DAAyE;;;;;;;;;;;;;;;;;;8CAOhH,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAgB,WAAU;8CAAwD;;;;;;8CAI7F,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAwD;;;;;;;;;;;;sCAM1F,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;sCAMH,6LAAC;4BACC,WAAU;4BACV,SAAS;sCAET,cAAA,6LAAC;gCAAI,WAAU;gCAAuB,SAAQ;;kDAC5C,6LAAC;kDAAM;;;;;;kDACP,6LAAC;wCAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;gBAMb,4BACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CAAsD;;;;;;0CAG/E,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAS,WAAU;0CAAsD;;;;;;0CAGpF,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAY,WAAU;0CAAsD;;;;;;0CAGvF,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAa,WAAU;0CAAsD;;;;;;0CAGxF,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAW,WAAU;0CAAsD;;;;;;0CAGtF,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAQ,WAAU;0CAAsD;;;;;;0CAGnF,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAgB,WAAU;0CAAsD;;;;;;0CAG3F,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAW,WAAU;0CAAsD;;;;;;0CAGtF,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GArPM;KAAA;uCAuPS", "debugId": null}}, {"offset": {"line": 674, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/agency/apollo-agency-website/src/components/Footer.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\n\nconst Footer = () => {\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      {/* Main Footer Content */}\n      <div className=\"container mx-auto px-4 py-16\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {/* Company Info */}\n          <div className=\"lg:col-span-1\">\n            <Link href=\"/\" className=\"text-2xl font-bold text-white mb-4 block\">\n              Apollo Agency\n            </Link>\n            <p className=\"text-gray-300 mb-6 leading-relaxed\">\n              We create innovative marketing solutions for your business, to reach a wider segment of customers through effective and carefully studied marketing strategies.\n            </p>\n            <div className=\"flex space-x-4\">\n              <a href=\"#\" className=\"w-10 h-10 bg-purple-600 hover:bg-purple-700 rounded-full flex items-center justify-center transition-colors duration-300\">\n                <span className=\"text-sm\">📘</span>\n              </a>\n              <a href=\"#\" className=\"w-10 h-10 bg-purple-600 hover:bg-purple-700 rounded-full flex items-center justify-center transition-colors duration-300\">\n                <span className=\"text-sm\">📷</span>\n              </a>\n              <a href=\"#\" className=\"w-10 h-10 bg-purple-600 hover:bg-purple-700 rounded-full flex items-center justify-center transition-colors duration-300\">\n                <span className=\"text-sm\">💼</span>\n              </a>\n              <a href=\"#\" className=\"w-10 h-10 bg-purple-600 hover:bg-purple-700 rounded-full flex items-center justify-center transition-colors duration-300\">\n                <span className=\"text-sm\">🐦</span>\n              </a>\n              <a href=\"#\" className=\"w-10 h-10 bg-purple-600 hover:bg-purple-700 rounded-full flex items-center justify-center transition-colors duration-300\">\n                <span className=\"text-sm\">📺</span>\n              </a>\n            </div>\n          </div>\n\n          {/* Quick Links */}\n          <div>\n            <h3 className=\"text-lg font-bold mb-6\">Quick Links</h3>\n            <ul className=\"space-y-3\">\n              <li>\n                <Link href=\"/\" className=\"text-gray-300 hover:text-white transition-colors duration-300\">\n                  Home\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/about\" className=\"text-gray-300 hover:text-white transition-colors duration-300\">\n                  About Us\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/services\" className=\"text-gray-300 hover:text-white transition-colors duration-300\">\n                  Our Services\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/portfolio\" className=\"text-gray-300 hover:text-white transition-colors duration-300\">\n                  Portfolio\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/blog\" className=\"text-gray-300 hover:text-white transition-colors duration-300\">\n                  Blog\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/contact\" className=\"text-gray-300 hover:text-white transition-colors duration-300\">\n                  Contact Us\n                </Link>\n              </li>\n            </ul>\n          </div>\n\n          {/* Services */}\n          <div>\n            <h3 className=\"text-lg font-bold mb-6\">Our Services</h3>\n            <ul className=\"space-y-3\">\n              <li>\n                <Link href=\"/services/digital-marketing\" className=\"text-gray-300 hover:text-white transition-colors duration-300\">\n                  Digital Marketing\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/services/seo\" className=\"text-gray-300 hover:text-white transition-colors duration-300\">\n                  SEO Services\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/services/graphic-design\" className=\"text-gray-300 hover:text-white transition-colors duration-300\">\n                  Graphic Design\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/services/social-media\" className=\"text-gray-300 hover:text-white transition-colors duration-300\">\n                  Social Media Management\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/services/branding\" className=\"text-gray-300 hover:text-white transition-colors duration-300\">\n                  Brand Development\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/services/content-creation\" className=\"text-gray-300 hover:text-white transition-colors duration-300\">\n                  Content Creation\n                </Link>\n              </li>\n            </ul>\n          </div>\n\n          {/* Contact Info */}\n          <div>\n            <h3 className=\"text-lg font-bold mb-6\">Contact Information</h3>\n            \n            {/* Turkey Office */}\n            <div className=\"mb-6\">\n              <h4 className=\"font-semibold text-purple-400 mb-2\">Turkey Office</h4>\n              <div className=\"space-y-2 text-gray-300\">\n                <p className=\"flex items-start\">\n                  <span className=\"mr-2\">📍</span>\n                  <span className=\"text-sm\">Üniversite Mah. E-5 Yan Yol Üzeri, Çınar Sk. No:1 D:4, 34320 Avcılar/İstanbul</span>\n                </p>\n                <p className=\"flex items-center\">\n                  <span className=\"mr-2\">📞</span>\n                  <span className=\"text-sm\">+90 552 833 0233</span>\n                </p>\n                <p className=\"flex items-center\">\n                  <span className=\"mr-2\">✉️</span>\n                  <span className=\"text-sm\"><EMAIL></span>\n                </p>\n              </div>\n            </div>\n\n            {/* Saudi Arabia Office */}\n            <div>\n              <h4 className=\"font-semibold text-purple-400 mb-2\">Saudi Arabia Office</h4>\n              <div className=\"space-y-2 text-gray-300\">\n                <p className=\"flex items-start\">\n                  <span className=\"mr-2\">📍</span>\n                  <span className=\"text-sm\">Jeddah, Al-Mohammadiyah District, Prince Sultan Street, Building 7163, Office 203</span>\n                </p>\n                <p className=\"flex items-center\">\n                  <span className=\"mr-2\">📞</span>\n                  <span className=\"text-sm\">+966 537 774 368</span>\n                </p>\n                <p className=\"flex items-center\">\n                  <span className=\"mr-2\">✉️</span>\n                  <span className=\"text-sm\"><EMAIL></span>\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Newsletter Section */}\n      <div className=\"bg-gray-800 py-12\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-4xl mx-auto text-center\">\n            <h3 className=\"text-2xl font-bold mb-4\">Ready to Develop Your Investment?</h3>\n            <p className=\"text-gray-300 mb-8\">Contact us now and start your journey to success</p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\">\n              <input\n                type=\"email\"\n                placeholder=\"Enter your email address\"\n                className=\"px-6 py-3 rounded-full text-gray-900 w-full sm:w-auto sm:min-w-[300px] focus:outline-none focus:ring-2 focus:ring-purple-500\"\n              />\n              <button className=\"bg-gradient-to-r from-purple-600 to-pink-500 text-white px-8 py-3 rounded-full font-semibold hover:from-purple-700 hover:to-pink-600 transition-all duration-300 transform hover:scale-105\">\n                Subscribe\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Bottom Footer */}\n      <div className=\"bg-gray-950 py-6\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center\">\n            <div className=\"text-gray-400 text-sm mb-4 md:mb-0\">\n              © 2024 Apollo Agency. All rights reserved.\n            </div>\n            <div className=\"flex space-x-6 text-sm\">\n              <Link href=\"/privacy\" className=\"text-gray-400 hover:text-white transition-colors duration-300\">\n                Privacy Policy\n              </Link>\n              <Link href=\"/terms\" className=\"text-gray-400 hover:text-white transition-colors duration-300\">\n                Terms of Service\n              </Link>\n              <Link href=\"/cookies\" className=\"text-gray-400 hover:text-white transition-colors duration-300\">\n                Cookie Policy\n              </Link>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIA,MAAM,SAAS;IACb,qBACE,6LAAC;QAAO,WAAU;;0BAEhB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CAA2C;;;;;;8CAGpE,6LAAC;oCAAE,WAAU;8CAAqC;;;;;;8CAGlD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,MAAK;4CAAI,WAAU;sDACpB,cAAA,6LAAC;gDAAK,WAAU;0DAAU;;;;;;;;;;;sDAE5B,6LAAC;4CAAE,MAAK;4CAAI,WAAU;sDACpB,cAAA,6LAAC;gDAAK,WAAU;0DAAU;;;;;;;;;;;sDAE5B,6LAAC;4CAAE,MAAK;4CAAI,WAAU;sDACpB,cAAA,6LAAC;gDAAK,WAAU;0DAAU;;;;;;;;;;;sDAE5B,6LAAC;4CAAE,MAAK;4CAAI,WAAU;sDACpB,cAAA,6LAAC;gDAAK,WAAU;0DAAU;;;;;;;;;;;sDAE5B,6LAAC;4CAAE,MAAK;4CAAI,WAAU;sDACpB,cAAA,6LAAC;gDAAK,WAAU;0DAAU;;;;;;;;;;;;;;;;;;;;;;;sCAMhC,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAyB;;;;;;8CACvC,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAI,WAAU;0DAAgE;;;;;;;;;;;sDAI3F,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAgE;;;;;;;;;;;sDAIhG,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAY,WAAU;0DAAgE;;;;;;;;;;;sDAInG,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAgE;;;;;;;;;;;sDAIpG,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAgE;;;;;;;;;;;sDAI/F,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAgE;;;;;;;;;;;;;;;;;;;;;;;sCAQtG,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAyB;;;;;;8CACvC,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAA8B,WAAU;0DAAgE;;;;;;;;;;;sDAIrH,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAgB,WAAU;0DAAgE;;;;;;;;;;;sDAIvG,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAA2B,WAAU;0DAAgE;;;;;;;;;;;sDAIlH,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAyB,WAAU;0DAAgE;;;;;;;;;;;sDAIhH,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAqB,WAAU;0DAAgE;;;;;;;;;;;sDAI5G,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAA6B,WAAU;0DAAgE;;;;;;;;;;;;;;;;;;;;;;;sCAQxH,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAyB;;;;;;8CAGvC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAqC;;;;;;sDACnD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;;sEACX,6LAAC;4DAAK,WAAU;sEAAO;;;;;;sEACvB,6LAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;8DAE5B,6LAAC;oDAAE,WAAU;;sEACX,6LAAC;4DAAK,WAAU;sEAAO;;;;;;sEACvB,6LAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;8DAE5B,6LAAC;oDAAE,WAAU;;sEACX,6LAAC;4DAAK,WAAU;sEAAO;;;;;;sEACvB,6LAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;;;;;;;;;;;;;8CAMhC,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAqC;;;;;;sDACnD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;;sEACX,6LAAC;4DAAK,WAAU;sEAAO;;;;;;sEACvB,6LAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;8DAE5B,6LAAC;oDAAE,WAAU;;sEACX,6LAAC;4DAAK,WAAU;sEAAO;;;;;;sEACvB,6LAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;8DAE5B,6LAAC;oDAAE,WAAU;;sEACX,6LAAC;4DAAK,WAAU;sEAAO;;;;;;sEACvB,6LAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAStC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA0B;;;;;;0CACxC,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAClC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;kDAEZ,6LAAC;wCAAO,WAAU;kDAA6L;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASvN,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAqC;;;;;;0CAGpD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAgE;;;;;;kDAGhG,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAAgE;;;;;;kDAG9F,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAgE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9G;KAnMM;uCAqMS", "debugId": null}}]}