{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/agency/apollo-agency-website/src/app/portfolio/marketing/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useLanguage } from '@/contexts/LanguageContext';\nimport Link from 'next/link';\n\nexport default function MarketingCampaignsPortfolioPage() {\n  const { t, isRTL } = useLanguage();\n\n  const campaigns = [\n    {\n      id: 1,\n      title: 'TechCorp Product Launch Campaign',\n      description: 'Multi-channel marketing campaign for software product launch',\n      channels: ['Google Ads', 'Facebook Ads', 'LinkedIn', 'Email Marketing', 'Content Marketing'],\n      results: ['500% ROI', '50,000+ leads', '300% brand awareness increase', '$2M revenue generated'],\n      image: '🚀',\n      category: 'Product Launch',\n      duration: '6 months'\n    },\n    {\n      id: 2,\n      title: 'E-commerce Holiday Campaign',\n      description: 'Seasonal marketing campaign for online fashion retailer',\n      channels: ['Instagram Ads', 'Google Shopping', 'Email Campaigns', 'Influencer Marketing'],\n      results: ['400% sales increase', '200% website traffic', '150% customer acquisition', '60% repeat purchases'],\n      image: '🛍️',\n      category: 'E-commerce',\n      duration: '3 months'\n    },\n    {\n      id: 3,\n      title: 'Healthcare Awareness Campaign',\n      description: 'Educational campaign for healthcare service provider',\n      channels: ['Facebook Ads', 'Content Marketing', 'SEO', 'Local Marketing'],\n      results: ['1000+ new patients', '300% appointment bookings', '500% website traffic', '200% brand recognition'],\n      image: '🏥',\n      category: 'Healthcare',\n      duration: '12 months'\n    },\n    {\n      id: 4,\n      title: 'B2B Lead Generation Campaign',\n      description: 'Targeted lead generation for business software company',\n      channels: ['LinkedIn Ads', 'Google Ads', 'Email Marketing', 'Webinars'],\n      results: ['10,000+ qualified leads', '25% conversion rate', '400% pipeline growth', '$5M in opportunities'],\n      image: '💼',\n      category: 'B2B Marketing',\n      duration: '9 months'\n    }\n  ];\n\n  return (\n    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : ''}`}>\n      {/* Hero Section */}\n      <section className=\"bg-gradient-to-br from-green-900 via-teal-800 to-blue-600 text-white py-20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-4xl mx-auto text-center\">\n            <div className=\"w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6\">\n              <span className=\"text-3xl\">📊</span>\n            </div>\n            <h1 className=\"text-4xl md:text-6xl font-bold mb-6\">\n              {t('portfolio.categories.marketingCampaigns')} Portfolio\n            </h1>\n            <p className=\"text-xl md:text-2xl mb-8 opacity-90\">\n              Data-driven marketing campaigns that deliver measurable results\n            </p>\n            <Link\n              href=\"/contact\"\n              className=\"inline-block bg-gradient-to-r from-green-500 to-blue-500 text-white font-bold px-8 py-4 rounded-full hover:from-green-400 hover:to-blue-400 transition-all duration-300 transform hover:scale-105\"\n            >\n              Launch Your Campaign\n            </Link>\n          </div>\n        </div>\n      </section>\n\n      {/* Campaigns Grid */}\n      <section className=\"py-20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-6xl mx-auto\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-6\">\n                Successful Marketing Campaigns\n              </h2>\n              <p className=\"text-xl text-gray-600\">\n                Campaigns that exceeded expectations and delivered ROI\n              </p>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8\">\n              {campaigns.map((campaign) => (\n                <div key={campaign.id} className=\"bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300\">\n                  {/* Campaign Header */}\n                  <div className=\"bg-gradient-to-r from-green-100 to-blue-100 p-8 text-center\">\n                    <div className=\"text-6xl mb-4\">{campaign.image}</div>\n                    <div className=\"flex justify-center items-center gap-4\">\n                      <span className=\"bg-white/80 text-green-600 px-3 py-1 rounded-full text-sm font-medium\">\n                        {campaign.category}\n                      </span>\n                      <span className=\"bg-white/80 text-blue-600 px-3 py-1 rounded-full text-sm font-medium\">\n                        {campaign.duration}\n                      </span>\n                    </div>\n                  </div>\n\n                  {/* Campaign Content */}\n                  <div className=\"p-8\">\n                    <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">{campaign.title}</h3>\n                    <p className=\"text-gray-600 mb-4\">{campaign.description}</p>\n                    \n                    <div className=\"mb-6\">\n                      <h4 className=\"font-semibold text-gray-900 mb-2\">Marketing Channels:</h4>\n                      <div className=\"flex flex-wrap gap-2\">\n                        {campaign.channels.map((channel, index) => (\n                          <span key={index} className=\"bg-green-100 text-green-600 px-3 py-1 rounded-full text-sm\">\n                            {channel}\n                          </span>\n                        ))}\n                      </div>\n                    </div>\n\n                    <div className=\"mb-6\">\n                      <h4 className=\"font-semibold text-gray-900 mb-3\">Campaign Results:</h4>\n                      <ul className=\"space-y-2\">\n                        {campaign.results.map((result, index) => (\n                          <li key={index} className=\"flex items-center text-green-600\">\n                            <span className=\"mr-2\">📈</span>\n                            {result}\n                          </li>\n                        ))}\n                      </ul>\n                    </div>\n\n                    <Link\n                      href={`/case-studies/${campaign.id}`}\n                      className=\"inline-flex items-center bg-gradient-to-r from-green-600 to-blue-500 text-white font-bold px-6 py-3 rounded-lg hover:from-green-700 hover:to-blue-600 transition-all duration-300\"\n                    >\n                      View Full Case Study\n                      <svg className={`w-4 h-4 ${isRTL ? 'mr-2 rotate-180' : 'ml-2'}`} fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                      </svg>\n                    </Link>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Marketing Services */}\n      <section className=\"py-20 bg-white\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-4xl mx-auto\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-6\">\n                Our Marketing Campaign Services\n              </h2>\n              <p className=\"text-xl text-gray-600\">\n                Full-service marketing campaigns that drive results\n              </p>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n              {[\n                { icon: '🎯', title: 'Strategy Development', description: 'Data-driven marketing strategies' },\n                { icon: '📱', title: 'Multi-Channel Campaigns', description: 'Integrated campaigns across all channels' },\n                { icon: '📊', title: 'Performance Tracking', description: 'Real-time analytics and reporting' },\n                { icon: '🎨', title: 'Creative Development', description: 'Compelling ad creatives and content' },\n                { icon: '💰', title: 'Budget Optimization', description: 'Maximum ROI from your marketing spend' },\n                { icon: '🔄', title: 'Campaign Optimization', description: 'Continuous improvement and testing' }\n              ].map((service, index) => (\n                <div key={index} className=\"text-center p-6 rounded-lg hover:bg-gray-50 transition-colors duration-300\">\n                  <div className=\"text-4xl mb-4\">{service.icon}</div>\n                  <h3 className=\"text-xl font-bold text-gray-900 mb-3\">{service.title}</h3>\n                  <p className=\"text-gray-600\">{service.description}</p>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Campaign Process */}\n      <section className=\"py-20 bg-gray-50\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-4xl mx-auto\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-6\">\n                Our Campaign Development Process\n              </h2>\n              <p className=\"text-xl text-gray-600\">\n                Systematic approach to creating successful marketing campaigns\n              </p>\n            </div>\n            \n            <div className=\"space-y-8\">\n              {[\n                { step: '01', title: 'Strategy & Planning', description: 'Define goals, target audience, and campaign strategy' },\n                { step: '02', title: 'Creative Development', description: 'Create compelling ad creatives and marketing materials' },\n                { step: '03', title: 'Campaign Launch', description: 'Execute campaigns across selected marketing channels' },\n                { step: '04', title: 'Monitor & Optimize', description: 'Track performance and optimize for better results' },\n                { step: '05', title: 'Report & Scale', description: 'Analyze results and scale successful campaigns' }\n              ].map((item, index) => (\n                <div key={index} className=\"flex items-start space-x-6\">\n                  <div className=\"w-16 h-16 bg-gradient-to-r from-green-600 to-blue-500 text-white rounded-full flex items-center justify-center text-xl font-bold flex-shrink-0\">\n                    {item.step}\n                  </div>\n                  <div>\n                    <h3 className=\"text-2xl font-bold text-gray-900 mb-3\">{item.title}</h3>\n                    <p className=\"text-gray-600 text-lg\">{item.description}</p>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"bg-gradient-to-r from-green-600 to-blue-500 text-white py-20\">\n        <div className=\"container mx-auto px-4 text-center\">\n          <h2 className=\"text-3xl md:text-4xl font-bold mb-6\">\n            Ready to Launch Your Next Campaign?\n          </h2>\n          <p className=\"text-xl mb-8 opacity-90 max-w-2xl mx-auto\">\n            Let's create a marketing campaign that drives real results and grows your business.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Link\n              href=\"/contact\"\n              className=\"bg-white text-green-600 font-bold px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 transform hover:scale-105\"\n            >\n              Start Your Campaign\n            </Link>\n            <Link\n              href=\"/portfolio\"\n              className=\"border-2 border-white text-white font-bold px-8 py-4 rounded-full hover:bg-white hover:text-green-600 transition-all duration-300\"\n            >\n              View All Projects\n            </Link>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,cAAW,AAAD;IAE/B,MAAM,YAAY;QAChB;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;gBAAC;gBAAc;gBAAgB;gBAAY;gBAAmB;aAAoB;YAC5F,SAAS;gBAAC;gBAAY;gBAAiB;gBAAiC;aAAwB;YAChG,OAAO;YACP,UAAU;YACV,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;gBAAC;gBAAiB;gBAAmB;gBAAmB;aAAuB;YACzF,SAAS;gBAAC;gBAAuB;gBAAwB;gBAA6B;aAAuB;YAC7G,OAAO;YACP,UAAU;YACV,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;gBAAC;gBAAgB;gBAAqB;gBAAO;aAAkB;YACzE,SAAS;gBAAC;gBAAsB;gBAA6B;gBAAwB;aAAyB;YAC9G,OAAO;YACP,UAAU;YACV,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;gBAAC;gBAAgB;gBAAc;gBAAmB;aAAW;YACvE,SAAS;gBAAC;gBAA2B;gBAAuB;gBAAwB;aAAuB;YAC3G,OAAO;YACP,UAAU;YACV,UAAU;QACZ;KACD;IAED,qBACE,8OAAC;QAAI,WAAW,CAAC,wBAAwB,EAAE,QAAQ,QAAQ,IAAI;;0BAE7D,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CAAW;;;;;;;;;;;0CAE7B,8OAAC;gCAAG,WAAU;;oCACX,EAAE;oCAA2C;;;;;;;0CAEhD,8OAAC;gCAAE,WAAU;0CAAsC;;;;;;0CAGnD,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;0BAQP,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAGlE,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAKvC,8OAAC;gCAAI,WAAU;0CACZ,UAAU,GAAG,CAAC,CAAC,yBACd,8OAAC;wCAAsB,WAAU;;0DAE/B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAiB,SAAS,KAAK;;;;;;kEAC9C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EACb,SAAS,QAAQ;;;;;;0EAEpB,8OAAC;gEAAK,WAAU;0EACb,SAAS,QAAQ;;;;;;;;;;;;;;;;;;0DAMxB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAyC,SAAS,KAAK;;;;;;kEACrE,8OAAC;wDAAE,WAAU;kEAAsB,SAAS,WAAW;;;;;;kEAEvD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAmC;;;;;;0EACjD,8OAAC;gEAAI,WAAU;0EACZ,SAAS,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC/B,8OAAC;wEAAiB,WAAU;kFACzB;uEADQ;;;;;;;;;;;;;;;;kEAOjB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAmC;;;;;;0EACjD,8OAAC;gEAAG,WAAU;0EACX,SAAS,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAC7B,8OAAC;wEAAe,WAAU;;0FACxB,8OAAC;gFAAK,WAAU;0FAAO;;;;;;4EACtB;;uEAFM;;;;;;;;;;;;;;;;kEAQf,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAM,CAAC,cAAc,EAAE,SAAS,EAAE,EAAE;wDACpC,WAAU;;4DACX;0EAEC,8OAAC;gEAAI,WAAW,CAAC,QAAQ,EAAE,QAAQ,oBAAoB,QAAQ;gEAAE,MAAK;gEAAO,QAAO;gEAAe,SAAQ;0EACzG,cAAA,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;uCAhDnE,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;0BA4D/B,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAGlE,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAKvC,8OAAC;gCAAI,WAAU;0CACZ;oCACC;wCAAE,MAAM;wCAAM,OAAO;wCAAwB,aAAa;oCAAmC;oCAC7F;wCAAE,MAAM;wCAAM,OAAO;wCAA2B,aAAa;oCAA2C;oCACxG;wCAAE,MAAM;wCAAM,OAAO;wCAAwB,aAAa;oCAAoC;oCAC9F;wCAAE,MAAM;wCAAM,OAAO;wCAAwB,aAAa;oCAAsC;oCAChG;wCAAE,MAAM;wCAAM,OAAO;wCAAuB,aAAa;oCAAwC;oCACjG;wCAAE,MAAM;wCAAM,OAAO;wCAAyB,aAAa;oCAAqC;iCACjG,CAAC,GAAG,CAAC,CAAC,SAAS,sBACd,8OAAC;wCAAgB,WAAU;;0DACzB,8OAAC;gDAAI,WAAU;0DAAiB,QAAQ,IAAI;;;;;;0DAC5C,8OAAC;gDAAG,WAAU;0DAAwC,QAAQ,KAAK;;;;;;0DACnE,8OAAC;gDAAE,WAAU;0DAAiB,QAAQ,WAAW;;;;;;;uCAHzC;;;;;;;;;;;;;;;;;;;;;;;;;;0BAYpB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAGlE,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAKvC,8OAAC;gCAAI,WAAU;0CACZ;oCACC;wCAAE,MAAM;wCAAM,OAAO;wCAAuB,aAAa;oCAAuD;oCAChH;wCAAE,MAAM;wCAAM,OAAO;wCAAwB,aAAa;oCAAyD;oCACnH;wCAAE,MAAM;wCAAM,OAAO;wCAAmB,aAAa;oCAAuD;oCAC5G;wCAAE,MAAM;wCAAM,OAAO;wCAAsB,aAAa;oCAAoD;oCAC5G;wCAAE,MAAM;wCAAM,OAAO;wCAAkB,aAAa;oCAAiD;iCACtG,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,8OAAC;wCAAgB,WAAU;;0DACzB,8OAAC;gDAAI,WAAU;0DACZ,KAAK,IAAI;;;;;;0DAEZ,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAyC,KAAK,KAAK;;;;;;kEACjE,8OAAC;wDAAE,WAAU;kEAAyB,KAAK,WAAW;;;;;;;;;;;;;uCANhD;;;;;;;;;;;;;;;;;;;;;;;;;;0BAgBpB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAsC;;;;;;sCAGpD,8OAAC;4BAAE,WAAU;sCAA4C;;;;;;sCAGzD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}]}