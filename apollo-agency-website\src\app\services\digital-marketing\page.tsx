'use client';

import { useLanguage } from '@/contexts/LanguageContext';
import Link from 'next/link';

export default function DigitalMarketingPage() {
  const { t, isRTL } = useLanguage();

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : ''}`}>
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-900 via-blue-800 to-purple-600 text-white py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6">
              <span className="text-3xl">📱</span>
            </div>
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              {t('services.digitalMarketing')}
            </h1>
            <p className="text-xl md:text-2xl mb-8 opacity-90">
              Transform your business with comprehensive digital marketing strategies
            </p>
            <Link
              href="/contact"
              className="inline-block bg-gradient-to-r from-yellow-400 to-orange-500 text-gray-900 font-bold px-8 py-4 rounded-full hover:from-yellow-300 hover:to-orange-400 transition-all duration-300 transform hover:scale-105"
            >
              Get Started Today
            </Link>
          </div>
        </div>
      </section>

      {/* Services Overview */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Our Digital Marketing Services
              </h2>
              <p className="text-xl text-gray-600">
                Comprehensive solutions to boost your online presence
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  icon: '📊',
                  title: 'Social Media Marketing',
                  description: 'Engage your audience across all major social platforms with targeted content and advertising campaigns.'
                },
                {
                  icon: '🎯',
                  title: 'Google Ads Management',
                  description: 'Maximize your ROI with expertly managed Google Ads campaigns that drive qualified traffic.'
                },
                {
                  icon: '📧',
                  title: 'Email Marketing',
                  description: 'Build lasting relationships with personalized email campaigns that convert prospects into customers.'
                },
                {
                  icon: '📝',
                  title: 'Content Marketing',
                  description: 'Create valuable content that attracts, engages, and converts your target audience.'
                },
                {
                  icon: '📈',
                  title: 'Analytics & Reporting',
                  description: 'Track performance and optimize campaigns with detailed analytics and regular reporting.'
                },
                {
                  icon: '🔄',
                  title: 'Marketing Automation',
                  description: 'Streamline your marketing processes with automated workflows and lead nurturing.'
                }
              ].map((service, index) => (
                <div key={index} className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300">
                  <div className="text-4xl mb-4">{service.icon}</div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{service.title}</h3>
                  <p className="text-gray-600">{service.description}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-16">
              Our Process
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
              {[
                { step: '01', title: 'Strategy', description: 'We analyze your business and create a custom digital marketing strategy.' },
                { step: '02', title: 'Implementation', description: 'Our team executes the strategy across all relevant digital channels.' },
                { step: '03', title: 'Optimization', description: 'We continuously monitor and optimize campaigns for better performance.' },
                { step: '04', title: 'Reporting', description: 'Regular reports keep you informed about progress and results.' }
              ].map((item, index) => (
                <div key={index} className="text-center">
                  <div className="w-16 h-16 bg-gradient-to-r from-purple-600 to-pink-500 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                    {item.step}
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{item.title}</h3>
                  <p className="text-gray-600">{item.description}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-gradient-to-r from-purple-600 to-pink-500 text-white py-20">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Ready to Transform Your Digital Presence?
          </h2>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Let's discuss how our digital marketing services can help your business grow and reach new heights.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/contact"
              className="bg-white text-purple-600 font-bold px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 transform hover:scale-105"
            >
              Get Free Consultation
            </Link>
            <Link
              href="/portfolio"
              className="border-2 border-white text-white font-bold px-8 py-4 rounded-full hover:bg-white hover:text-purple-600 transition-all duration-300"
            >
              View Our Work
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
