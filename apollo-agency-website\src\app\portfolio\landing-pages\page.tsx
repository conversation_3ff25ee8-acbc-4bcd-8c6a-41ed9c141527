'use client';

import { useLanguage } from '@/contexts/LanguageContext';
import Link from 'next/link';

export default function LandingPagesPortfolioPage() {
  const { t, isRTL } = useLanguage();

  const projects = [
    {
      id: 1,
      title: 'TechCorp Product Launch',
      description: 'High-converting landing page for new software product launch',
      metrics: ['45% conversion rate', '10,000+ leads generated', '300% ROI increase'],
      features: ['Responsive Design', 'A/B Testing', 'Lead Capture Forms', 'Analytics Integration'],
      image: '💻',
      category: 'Product Launch'
    },
    {
      id: 2,
      title: 'E-commerce Sales Page',
      description: 'Optimized sales page for online fashion retailer',
      metrics: ['60% increase in sales', '35% higher conversion', '200% more traffic'],
      features: ['Mobile Optimized', 'Fast Loading', 'Social Proof', 'Payment Integration'],
      image: '🛍️',
      category: 'E-commerce'
    },
    {
      id: 3,
      title: 'Healthcare Service Landing',
      description: 'Professional landing page for medical consultation service',
      metrics: ['500% increase in appointments', '40% conversion rate', '1000+ new patients'],
      features: ['Trust Signals', 'Appointment Booking', 'Patient Testimonials', 'HIPAA Compliant'],
      image: '🏥',
      category: 'Healthcare'
    },
    {
      id: 4,
      title: 'Event Registration Page',
      description: 'Registration landing page for business conference',
      metrics: ['2000+ registrations', '50% conversion rate', 'Sold out event'],
      features: ['Event Details', 'Speaker Profiles', 'Registration Form', 'Payment Processing'],
      image: '🎯',
      category: 'Event Marketing'
    }
  ];

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : ''}`}>
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-900 via-indigo-800 to-purple-600 text-white py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6">
              <span className="text-3xl">🚀</span>
            </div>
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              {t('portfolio.categories.landingPages')} Portfolio
            </h1>
            <p className="text-xl md:text-2xl mb-8 opacity-90">
              High-converting landing pages that turn visitors into customers
            </p>
            <Link
              href="/contact"
              className="inline-block bg-gradient-to-r from-blue-500 to-purple-500 text-white font-bold px-8 py-4 rounded-full hover:from-blue-400 hover:to-purple-400 transition-all duration-300 transform hover:scale-105"
            >
              Create Your Landing Page
            </Link>
          </div>
        </div>
      </section>

      {/* Projects Grid */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                High-Converting Landing Pages
              </h2>
              <p className="text-xl text-gray-600">
                Pages designed to convert visitors into customers
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {projects.map((project) => (
                <div key={project.id} className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300">
                  {/* Project Header */}
                  <div className="bg-gradient-to-r from-blue-100 to-purple-100 p-8 text-center">
                    <div className="text-6xl mb-4">{project.image}</div>
                    <span className="bg-white/80 text-blue-600 px-3 py-1 rounded-full text-sm font-medium">
                      {project.category}
                    </span>
                  </div>

                  {/* Project Content */}
                  <div className="p-8">
                    <h3 className="text-2xl font-bold text-gray-900 mb-4">{project.title}</h3>
                    <p className="text-gray-600 mb-4">{project.description}</p>
                    
                    <div className="mb-6">
                      <h4 className="font-semibold text-gray-900 mb-3">Key Metrics:</h4>
                      <ul className="space-y-2">
                        {project.metrics.map((metric, index) => (
                          <li key={index} className="flex items-center text-green-600">
                            <span className="mr-2">📈</span>
                            {metric}
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div className="mb-6">
                      <h4 className="font-semibold text-gray-900 mb-2">Features:</h4>
                      <div className="flex flex-wrap gap-2">
                        {project.features.map((feature, index) => (
                          <span key={index} className="bg-blue-100 text-blue-600 px-3 py-1 rounded-full text-sm">
                            {feature}
                          </span>
                        ))}
                      </div>
                    </div>

                    <Link
                      href={`/case-studies/${project.id}`}
                      className="inline-flex items-center bg-gradient-to-r from-blue-600 to-purple-500 text-white font-bold px-6 py-3 rounded-lg hover:from-blue-700 hover:to-purple-600 transition-all duration-300"
                    >
                      View Full Case Study
                      <svg className={`w-4 h-4 ${isRTL ? 'mr-2 rotate-180' : 'ml-2'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </Link>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Landing Page Features */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                What Makes Our Landing Pages Convert
              </h2>
              <p className="text-xl text-gray-600">
                Every element is designed with conversion in mind
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                { icon: '🎯', title: 'Clear Value Proposition', description: 'Compelling headlines that communicate benefits' },
                { icon: '📱', title: 'Mobile Responsive', description: 'Perfect experience on all devices' },
                { icon: '⚡', title: 'Fast Loading', description: 'Optimized for speed and performance' },
                { icon: '🔒', title: 'Trust Signals', description: 'Testimonials, reviews, and security badges' },
                { icon: '📊', title: 'A/B Tested', description: 'Data-driven optimization for best results' },
                { icon: '🎨', title: 'Conversion Focused', description: 'Every element designed to drive action' }
              ].map((feature, index) => (
                <div key={index} className="text-center p-6 rounded-lg hover:bg-gray-50 transition-colors duration-300">
                  <div className="text-4xl mb-4">{feature.icon}</div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{feature.title}</h3>
                  <p className="text-gray-600">{feature.description}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Conversion Optimization Process */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Our Landing Page Development Process
              </h2>
              <p className="text-xl text-gray-600">
                Systematic approach to creating high-converting pages
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
              {[
                { step: '01', title: 'Research', description: 'Understanding your audience and goals' },
                { step: '02', title: 'Design', description: 'Creating conversion-focused layouts' },
                { step: '03', title: 'Develop', description: 'Building fast, responsive pages' },
                { step: '04', title: 'Optimize', description: 'Testing and improving performance' }
              ].map((item, index) => (
                <div key={index} className="text-center">
                  <div className="w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-500 text-white rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4">
                    {item.step}
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{item.title}</h3>
                  <p className="text-gray-600">{item.description}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-gradient-to-r from-blue-600 to-purple-500 text-white py-20">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Ready to Boost Your Conversion Rates?
          </h2>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Let's create a landing page that turns your traffic into customers and grows your business.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/contact"
              className="bg-white text-blue-600 font-bold px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 transform hover:scale-105"
            >
              Get High-Converting Landing Page
            </Link>
            <Link
              href="/portfolio"
              className="border-2 border-white text-white font-bold px-8 py-4 rounded-full hover:bg-white hover:text-blue-600 transition-all duration-300"
            >
              View All Projects
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
