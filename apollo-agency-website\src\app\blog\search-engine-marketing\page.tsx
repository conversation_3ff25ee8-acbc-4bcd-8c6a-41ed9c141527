'use client';

import { useLanguage } from '@/contexts/LanguageContext';
import Link from 'next/link';

export default function SearchEngineMarketingBlogPage() {
  const { isRTL } = useLanguage();

  const posts = [
    {
      id: 1,
      title: 'Google Ads vs. SEO: Which Strategy is Right for Your Business?',
      excerpt: 'Compare the benefits of paid search advertising and organic SEO to determine the best approach for your goals.',
      author: '<PERSON>',
      date: '2024-01-18',
      readTime: '10 min read',
      category: 'Strategy',
      image: '🔍'
    },
    {
      id: 2,
      title: 'How to Optimize Your Google Ads for Maximum ROI',
      excerpt: 'Advanced techniques to improve your Google Ads performance and get more value from your advertising budget.',
      author: '<PERSON>',
      date: '2024-01-12',
      readTime: '8 min read',
      category: 'Google Ads',
      image: '💰'
    },
    {
      id: 3,
      title: 'Local SEO: Dominating Search Results in Your Area',
      excerpt: 'Complete guide to local SEO strategies that help your business rank higher in local search results.',
      author: '<PERSON>',
      date: '2024-01-08',
      readTime: '12 min read',
      category: 'Local SEO',
      image: '📍'
    },
    {
      id: 4,
      title: 'Keyword Research in 2024: Tools and Techniques',
      excerpt: 'Master keyword research with the latest tools and strategies for finding profitable search terms.',
      author: 'Anna Wilson',
      date: '2024-01-03',
      readTime: '9 min read',
      category: 'SEO',
      image: '🔑'
    },
    {
      id: 5,
      title: 'Microsoft Ads: The Underrated PPC Platform',
      excerpt: 'Discover why Microsoft Ads (Bing Ads) should be part of your search marketing strategy.',
      author: 'Robert Taylor',
      date: '2023-12-25',
      readTime: '7 min read',
      category: 'Microsoft Ads',
      image: '🎯'
    },
    {
      id: 6,
      title: 'Technical SEO Checklist for 2024',
      excerpt: 'Essential technical SEO elements to ensure your website is optimized for search engines.',
      author: 'Sophie Chen',
      date: '2023-12-18',
      readTime: '11 min read',
      category: 'Technical SEO',
      image: '⚙️'
    }
  ];

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : ''}`}>
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-green-900 via-blue-800 to-purple-600 text-white py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6">
              <span className="text-3xl">🔍</span>
            </div>
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Search Engine Marketing Blog
            </h1>
            <p className="text-xl md:text-2xl mb-8 opacity-90">
              Master SEO and PPC strategies to dominate search results
            </p>
            <Link
              href="/contact"
              className="inline-block bg-gradient-to-r from-green-500 to-blue-500 text-white font-bold px-8 py-4 rounded-full hover:from-green-400 hover:to-blue-400 transition-all duration-300 transform hover:scale-105"
            >
              Get SEO/PPC Help
            </Link>
          </div>
        </div>
      </section>

      {/* Blog Posts Grid */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Latest Search Marketing Articles
              </h2>
              <p className="text-xl text-gray-600">
                Expert insights on SEO and PPC strategies
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {posts.map((post) => (
                <article key={post.id} className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300">
                  <div className="bg-gradient-to-r from-green-100 to-blue-100 p-8 text-center">
                    <div className="text-6xl mb-4">{post.image}</div>
                    <span className="bg-white/80 text-green-600 px-3 py-1 rounded-full text-sm font-medium">
                      {post.category}
                    </span>
                  </div>

                  <div className="p-6">
                    <h3 className="text-xl font-bold text-gray-900 mb-3 line-clamp-2">
                      {post.title}
                    </h3>
                    <p className="text-gray-600 mb-4 line-clamp-3">
                      {post.excerpt}
                    </p>
                    
                    <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                      <span>{post.author}</span>
                      <span>{post.readTime}</span>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500">{post.date}</span>
                      <Link
                        href={`/blog/search-engine-marketing/${post.id}`}
                        className="inline-flex items-center text-green-600 hover:text-green-700 font-medium"
                      >
                        Read More
                        <svg className={`w-4 h-4 ${isRTL ? 'mr-1 rotate-180' : 'ml-1'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                      </Link>
                    </div>
                  </div>
                </article>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-gradient-to-r from-green-600 to-blue-500 text-white py-20">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Ready to Dominate Search Results?
          </h2>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Let our SEO and PPC experts help you rank higher and drive more qualified traffic.
          </p>
          <Link
            href="/contact"
            className="bg-white text-green-600 font-bold px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 transform hover:scale-105"
          >
            Get Search Marketing Help
          </Link>
        </div>
      </section>
    </div>
  );
}
