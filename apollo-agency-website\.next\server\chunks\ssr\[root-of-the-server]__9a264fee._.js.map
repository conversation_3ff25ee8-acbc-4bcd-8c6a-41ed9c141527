{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/agency/apollo-agency-website/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\n\nconst Header = () => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [isServicesOpen, setIsServicesOpen] = useState(false);\n  const [isPortfolioOpen, setIsPortfolioOpen] = useState(false);\n  const [isBlogOpen, setIsBlogOpen] = useState(false);\n\n  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);\n\n  return (\n    <header className=\"bg-white shadow-lg fixed w-full top-0 z-50\">\n      <div className=\"container mx-auto px-4\">\n        {/* Top bar with contact info */}\n        <div className=\"hidden md:flex justify-end py-2 text-sm text-gray-600\">\n          <div className=\"flex items-center space-x-4\">\n            <span>Get Consultation</span>\n            <div className=\"flex space-x-2\">\n              <span className=\"fi fi-sa\"></span>\n              <span>العربية</span>\n              <span>|</span>\n              <span className=\"fi fi-us\"></span>\n              <span>English</span>\n            </div>\n          </div>\n        </div>\n\n        {/* Main navigation */}\n        <nav className=\"flex items-center justify-between py-4\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"text-2xl font-bold text-purple-800\">\n              Apollo Agency\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden lg:flex items-center space-x-8\">\n            <Link href=\"/\" className=\"text-gray-700 hover:text-purple-600 transition-colors\">\n              Home\n            </Link>\n            \n            <Link href=\"/about\" className=\"text-gray-700 hover:text-purple-600 transition-colors\">\n              About Us\n            </Link>\n\n            {/* Services Dropdown */}\n            <div className=\"relative group\">\n              <button \n                className=\"text-gray-700 hover:text-purple-600 transition-colors flex items-center\"\n                onMouseEnter={() => setIsServicesOpen(true)}\n                onMouseLeave={() => setIsServicesOpen(false)}\n              >\n                Our Services\n                <svg className=\"ml-1 w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n                </svg>\n              </button>\n              \n              {isServicesOpen && (\n                <div \n                  className=\"absolute top-full left-0 mt-2 w-64 bg-white shadow-lg rounded-lg py-2 z-50\"\n                  onMouseEnter={() => setIsServicesOpen(true)}\n                  onMouseLeave={() => setIsServicesOpen(false)}\n                >\n                  <Link href=\"/services/digital-marketing\" className=\"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600\">\n                    Digital Marketing\n                  </Link>\n                  <Link href=\"/services/seo\" className=\"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600\">\n                    SEO Services\n                  </Link>\n                  <Link href=\"/services/graphic-design\" className=\"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600\">\n                    Graphic Design\n                  </Link>\n                  <Link href=\"/services/social-media\" className=\"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600\">\n                    Social Media Management\n                  </Link>\n                  <Link href=\"/services/branding\" className=\"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600\">\n                    Brand Development\n                  </Link>\n                  <Link href=\"/services/influencer-marketing\" className=\"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600\">\n                    Influencer Marketing\n                  </Link>\n                  <Link href=\"/services/content-creation\" className=\"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600\">\n                    Content Creation\n                  </Link>\n                  <Link href=\"/services/marketing-strategy\" className=\"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600\">\n                    Marketing Strategy\n                  </Link>\n                </div>\n              )}\n            </div>\n\n            {/* Portfolio Dropdown */}\n            <div className=\"relative group\">\n              <button \n                className=\"text-gray-700 hover:text-purple-600 transition-colors flex items-center\"\n                onMouseEnter={() => setIsPortfolioOpen(true)}\n                onMouseLeave={() => setIsPortfolioOpen(false)}\n              >\n                Portfolio\n                <svg className=\"ml-1 w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n                </svg>\n              </button>\n              \n              {isPortfolioOpen && (\n                <div \n                  className=\"absolute top-full left-0 mt-2 w-56 bg-white shadow-lg rounded-lg py-2 z-50\"\n                  onMouseEnter={() => setIsPortfolioOpen(true)}\n                  onMouseLeave={() => setIsPortfolioOpen(false)}\n                >\n                  <Link href=\"/portfolio/social-media\" className=\"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600\">\n                    Social Media Designs\n                  </Link>\n                  <Link href=\"/portfolio/branding\" className=\"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600\">\n                    Brand Identity\n                  </Link>\n                  <Link href=\"/portfolio/graphic-design\" className=\"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600\">\n                    Graphic Design\n                  </Link>\n                  <Link href=\"/portfolio/landing-pages\" className=\"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600\">\n                    Landing Pages\n                  </Link>\n                  <Link href=\"/portfolio/marketing\" className=\"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600\">\n                    Marketing Campaigns\n                  </Link>\n                </div>\n              )}\n            </div>\n\n            <Link href=\"/clients\" className=\"text-gray-700 hover:text-purple-600 transition-colors\">\n              Our Clients\n            </Link>\n\n            {/* Blog Dropdown */}\n            <div className=\"relative group\">\n              <button \n                className=\"text-gray-700 hover:text-purple-600 transition-colors flex items-center\"\n                onMouseEnter={() => setIsBlogOpen(true)}\n                onMouseLeave={() => setIsBlogOpen(false)}\n              >\n                Blog\n                <svg className=\"ml-1 w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n                </svg>\n              </button>\n              \n              {isBlogOpen && (\n                <div \n                  className=\"absolute top-full left-0 mt-2 w-56 bg-white shadow-lg rounded-lg py-2 z-50\"\n                  onMouseEnter={() => setIsBlogOpen(true)}\n                  onMouseLeave={() => setIsBlogOpen(false)}\n                >\n                  <Link href=\"/blog/social-media-marketing\" className=\"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600\">\n                    Social Media Marketing\n                  </Link>\n                  <Link href=\"/blog/search-engine-marketing\" className=\"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600\">\n                    Search Engine Marketing\n                  </Link>\n                  <Link href=\"/blog/video-marketing\" className=\"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600\">\n                    Video Marketing\n                  </Link>\n                  <Link href=\"/blog/digital-marketing\" className=\"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600\">\n                    Digital Marketing\n                  </Link>\n                  <Link href=\"/blog/graphic-design\" className=\"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600\">\n                    Graphic Design\n                  </Link>\n                  <Link href=\"/blog/seo\" className=\"block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600\">\n                    SEO\n                  </Link>\n                </div>\n              )}\n            </div>\n\n            <Link href=\"/case-studies\" className=\"text-gray-700 hover:text-purple-600 transition-colors\">\n              Case Studies\n            </Link>\n\n            <Link href=\"/contact\" className=\"text-gray-700 hover:text-purple-600 transition-colors\">\n              Contact Us\n            </Link>\n          </div>\n\n          {/* CTA Button */}\n          <div className=\"hidden lg:flex\">\n            <Link \n              href=\"/consultation\" \n              className=\"bg-gradient-to-r from-purple-600 to-pink-500 text-white px-6 py-2 rounded-full hover:from-purple-700 hover:to-pink-600 transition-all duration-300 transform hover:scale-105\"\n            >\n              Get Consultation\n            </Link>\n          </div>\n\n          {/* Mobile menu button */}\n          <button\n            className=\"lg:hidden flex items-center px-3 py-2 border rounded text-gray-500 border-gray-600 hover:text-gray-700 hover:border-gray-700\"\n            onClick={toggleMenu}\n          >\n            <svg className=\"fill-current h-3 w-3\" viewBox=\"0 0 20 20\">\n              <title>Menu</title>\n              <path d=\"M0 3h20v2H0V3zm0 6h20v2H0V9zm0 6h20v2H0v-2z\"/>\n            </svg>\n          </button>\n        </nav>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"lg:hidden\">\n            <div className=\"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t\">\n              <Link href=\"/\" className=\"block px-3 py-2 text-gray-700 hover:text-purple-600\">\n                Home\n              </Link>\n              <Link href=\"/about\" className=\"block px-3 py-2 text-gray-700 hover:text-purple-600\">\n                About Us\n              </Link>\n              <Link href=\"/services\" className=\"block px-3 py-2 text-gray-700 hover:text-purple-600\">\n                Our Services\n              </Link>\n              <Link href=\"/portfolio\" className=\"block px-3 py-2 text-gray-700 hover:text-purple-600\">\n                Portfolio\n              </Link>\n              <Link href=\"/clients\" className=\"block px-3 py-2 text-gray-700 hover:text-purple-600\">\n                Our Clients\n              </Link>\n              <Link href=\"/blog\" className=\"block px-3 py-2 text-gray-700 hover:text-purple-600\">\n                Blog\n              </Link>\n              <Link href=\"/case-studies\" className=\"block px-3 py-2 text-gray-700 hover:text-purple-600\">\n                Case Studies\n              </Link>\n              <Link href=\"/contact\" className=\"block px-3 py-2 text-gray-700 hover:text-purple-600\">\n                Contact Us\n              </Link>\n              <Link \n                href=\"/consultation\" \n                className=\"block mx-3 mt-4 bg-gradient-to-r from-purple-600 to-pink-500 text-white px-4 py-2 rounded-full text-center\"\n              >\n                Get Consultation\n              </Link>\n            </div>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAMA,MAAM,SAAS;IACb,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,aAAa,IAAM,cAAc,CAAC;IAExC,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAK;;;;;;0CACN,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;;;;;;kDAChB,8OAAC;kDAAK;;;;;;kDACN,8OAAC;kDAAK;;;;;;kDACN,8OAAC;wCAAK,WAAU;;;;;;kDAChB,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;8BAMZ,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CAAqC;;;;;;;;;;;sCAMhE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAwD;;;;;;8CAIjF,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;8CAAwD;;;;;;8CAKtF,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,WAAU;4CACV,cAAc,IAAM,kBAAkB;4CACtC,cAAc,IAAM,kBAAkB;;gDACvC;8DAEC,8OAAC;oDAAI,WAAU;oDAAe,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACtE,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;;wCAIxE,gCACC,8OAAC;4CACC,WAAU;4CACV,cAAc,IAAM,kBAAkB;4CACtC,cAAc,IAAM,kBAAkB;;8DAEtC,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAA8B,WAAU;8DAAyE;;;;;;8DAG5H,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAgB,WAAU;8DAAyE;;;;;;8DAG9G,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAA2B,WAAU;8DAAyE;;;;;;8DAGzH,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAyB,WAAU;8DAAyE;;;;;;8DAGvH,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAqB,WAAU;8DAAyE;;;;;;8DAGnH,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAiC,WAAU;8DAAyE;;;;;;8DAG/H,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAA6B,WAAU;8DAAyE;;;;;;8DAG3H,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAA+B,WAAU;8DAAyE;;;;;;;;;;;;;;;;;;8CAQnI,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,WAAU;4CACV,cAAc,IAAM,mBAAmB;4CACvC,cAAc,IAAM,mBAAmB;;gDACxC;8DAEC,8OAAC;oDAAI,WAAU;oDAAe,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACtE,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;;wCAIxE,iCACC,8OAAC;4CACC,WAAU;4CACV,cAAc,IAAM,mBAAmB;4CACvC,cAAc,IAAM,mBAAmB;;8DAEvC,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAA0B,WAAU;8DAAyE;;;;;;8DAGxH,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAsB,WAAU;8DAAyE;;;;;;8DAGpH,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAA4B,WAAU;8DAAyE;;;;;;8DAG1H,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAA2B,WAAU;8DAAyE;;;;;;8DAGzH,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAuB,WAAU;8DAAyE;;;;;;;;;;;;;;;;;;8CAO3H,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAwD;;;;;;8CAKxF,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,WAAU;4CACV,cAAc,IAAM,cAAc;4CAClC,cAAc,IAAM,cAAc;;gDACnC;8DAEC,8OAAC;oDAAI,WAAU;oDAAe,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACtE,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;;wCAIxE,4BACC,8OAAC;4CACC,WAAU;4CACV,cAAc,IAAM,cAAc;4CAClC,cAAc,IAAM,cAAc;;8DAElC,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAA+B,WAAU;8DAAyE;;;;;;8DAG7H,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAgC,WAAU;8DAAyE;;;;;;8DAG9H,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAwB,WAAU;8DAAyE;;;;;;8DAGtH,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAA0B,WAAU;8DAAyE;;;;;;8DAGxH,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAuB,WAAU;8DAAyE;;;;;;8DAGrH,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAY,WAAU;8DAAyE;;;;;;;;;;;;;;;;;;8CAOhH,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAgB,WAAU;8CAAwD;;;;;;8CAI7F,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAwD;;;;;;;;;;;;sCAM1F,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;sCAMH,8OAAC;4BACC,WAAU;4BACV,SAAS;sCAET,cAAA,8OAAC;gCAAI,WAAU;gCAAuB,SAAQ;;kDAC5C,8OAAC;kDAAM;;;;;;kDACP,8OAAC;wCAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;gBAMb,4BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CAAsD;;;;;;0CAG/E,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAS,WAAU;0CAAsD;;;;;;0CAGpF,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAY,WAAU;0CAAsD;;;;;;0CAGvF,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAa,WAAU;0CAAsD;;;;;;0CAGxF,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAW,WAAU;0CAAsD;;;;;;0CAGtF,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAQ,WAAU;0CAAsD;;;;;;0CAGnF,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAgB,WAAU;0CAAsD;;;;;;0CAG3F,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAW,WAAU;0CAAsD;;;;;;0CAGtF,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;uCAEe", "debugId": null}}]}