'use client';

import { useLanguage } from '@/contexts/LanguageContext';

export default function ServicesPage() {
  const { t, isRTL } = useLanguage();

  const services = [
    {
      id: 'digital-marketing',
      icon: '📱',
      title: t('services.digitalMarketing'),
      description: t('services.digital.desc'),
      features: [
        'Social Media Advertising',
        'Google Ads Management',
        'Email Marketing Campaigns',
        'Content Marketing Strategy',
        'Analytics & Reporting'
      ]
    },
    {
      id: 'seo',
      icon: '🔍',
      title: t('services.seo'),
      description: t('services.seoServices.desc'),
      features: [
        'Keyword Research & Analysis',
        'On-Page SEO Optimization',
        'Technical SEO Audit',
        'Link Building Strategy',
        'Local SEO Services'
      ]
    },
    {
      id: 'graphic-design',
      icon: '🎨',
      title: t('services.graphicDesign'),
      description: t('services.graphic.desc'),
      features: [
        'Logo Design',
        'Brand Identity Design',
        'Print Design',
        'Digital Graphics',
        'Packaging Design'
      ]
    },
    {
      id: 'social-media',
      icon: '📲',
      title: t('services.socialMedia'),
      description: 'Complete social media management and strategy',
      features: [
        'Content Creation',
        'Community Management',
        'Social Media Strategy',
        'Influencer Partnerships',
        'Performance Analytics'
      ]
    },
    {
      id: 'branding',
      icon: '🎯',
      title: t('services.branding'),
      description: t('services.brandDev.desc'),
      features: [
        'Brand Strategy Development',
        'Visual Identity Design',
        'Brand Guidelines',
        'Brand Positioning',
        'Brand Messaging'
      ]
    },
    {
      id: 'content-creation',
      icon: '✍️',
      title: t('services.contentCreation'),
      description: t('services.content.desc'),
      features: [
        'Blog Writing',
        'Website Copy',
        'Social Media Content',
        'Video Scripts',
        'Email Content'
      ]
    }
  ];

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : ''}`}>
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-purple-900 via-purple-800 to-pink-600 text-white py-20">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            {t('header.services')}
          </h1>
          <p className="text-xl md:text-2xl mb-8 opacity-90 max-w-3xl mx-auto">
            {t('services.subtitle')}
          </p>
        </div>
      </section>

      {/* Services Grid */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.map((service) => (
              <div
                key={service.id}
                className="bg-white rounded-xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2"
              >
                {/* Icon */}
                <div className="text-center mb-6">
                  <div className="w-20 h-20 bg-gradient-to-r from-purple-100 to-pink-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-3xl">{service.icon}</span>
                  </div>
                </div>

                {/* Content */}
                <div className="text-center">
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">
                    {service.title}
                  </h3>
                  <p className="text-gray-600 mb-6 leading-relaxed">
                    {service.description}
                  </p>

                  {/* Features */}
                  <div className="text-left mb-6">
                    <h4 className="font-semibold text-gray-900 mb-3">Key Features:</h4>
                    <ul className="space-y-2">
                      {service.features.map((feature, index) => (
                        <li key={index} className="flex items-center text-gray-600">
                          <span className="text-purple-600 mr-2">✓</span>
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* CTA */}
                  <button className="w-full bg-gradient-to-r from-purple-600 to-pink-500 text-white font-bold py-3 px-6 rounded-full hover:from-purple-700 hover:to-pink-600 transition-all duration-300 transform hover:scale-105">
                    Get Started
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-gradient-to-r from-purple-600 to-pink-500 text-white py-20">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Ready to Get Started?
          </h2>
          <p className="text-xl mb-8 opacity-90">
            Contact us today for a free consultation
          </p>
          <button className="bg-white text-purple-600 font-bold px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 transform hover:scale-105">
            {t('header.consultation')}
          </button>
        </div>
      </section>
    </div>
  );
}
