'use client';

import Link from 'next/link';
import Image from 'next/image';
import { useLanguage } from '@/contexts/LanguageContext';

const PortfolioSection = () => {
  const { t, isRTL } = useLanguage();

  const portfolioItems = [
    {
      id: 1,
      title: "Brand Identity Design",
      category: "Branding",
      image: "/api/placeholder/400/500",
      description: "Complete brand identity design for a tech startup",
      link: "/portfolio/brand-identity"
    },
    {
      id: 2,
      title: "Social Media Designs",
      category: "Social Media",
      image: "/api/placeholder/400/500",
      description: "Creative social media content for various platforms",
      link: "/portfolio/social-media"
    },
    {
      id: 3,
      title: "SEO Optimization",
      category: "SEO",
      image: "/api/placeholder/400/500",
      description: "Search engine optimization for e-commerce website",
      link: "/portfolio/seo"
    },
    {
      id: 4,
      title: "Digital Marketing Campaign",
      category: "Marketing",
      image: "/api/placeholder/400/500",
      description: "Comprehensive digital marketing campaign management",
      link: "/portfolio/digital-marketing"
    }
  ];

  return (
    <section className={`py-20 bg-white ${isRTL ? 'rtl' : ''}`}>
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="flex justify-center mb-6">
            <div className="w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-500 rounded-full flex items-center justify-center">
              <span className="text-2xl">🎨</span>
            </div>
          </div>
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            {t('portfolio.title')}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {t('portfolio.subtitle')}
          </p>
        </div>

        {/* Portfolio Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {portfolioItems.map((item) => (
            <div
              key={item.id}
              className="group relative overflow-hidden rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2"
            >
              {/* Image Container */}
              <div className="relative h-80 bg-gradient-to-br from-purple-100 to-pink-100 overflow-hidden">
                {/* Placeholder for image */}
                <div className="w-full h-full bg-gradient-to-br from-purple-200 to-pink-200 flex items-center justify-center">
                  <div className="text-center">
                    <div className="text-4xl mb-2">
                      {item.category === 'Branding' && '🎯'}
                      {item.category === 'Social Media' && '📱'}
                      {item.category === 'SEO' && '🔍'}
                      {item.category === 'Marketing' && '📊'}
                    </div>
                    <p className="text-gray-600 font-medium">{item.category}</p>
                  </div>
                </div>
                
                {/* Overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div className="absolute bottom-4 left-4 right-4 text-white">
                    <h3 className="text-lg font-bold mb-2">{item.title}</h3>
                    <p className="text-sm opacity-90 mb-3">{item.description}</p>
                    <Link
                      href={item.link}
                      className="inline-flex items-center text-white bg-white/20 backdrop-blur-sm px-4 py-2 rounded-full text-sm font-medium hover:bg-white/30 transition-all duration-300"
                    >
                      {t('portfolio.viewProject')}
                      <svg className={`w-4 h-4 ${isRTL ? 'mr-2 rotate-180' : 'ml-2'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </Link>
                  </div>
                </div>
              </div>

              {/* Category Badge */}
              <div className="absolute top-4 left-4">
                <span className="bg-white/90 backdrop-blur-sm text-gray-800 px-3 py-1 rounded-full text-sm font-medium">
                  {item.category}
                </span>
              </div>
            </div>
          ))}
        </div>

        {/* Portfolio Categories */}
        <div className="mt-16 text-center">
          <h3 className="text-2xl font-bold text-gray-900 mb-8">{t('portfolio.browseByCategory')}</h3>
          <div className="flex flex-wrap justify-center gap-4">
            {[
              { key: 'brand-identity', label: t('portfolio.categories.brandIdentity') },
              { key: 'social-media', label: t('portfolio.categories.socialMedia') },
              { key: 'graphic-design', label: t('portfolio.categories.graphicDesign') },
              { key: 'landing-pages', label: t('portfolio.categories.landingPages') },
              { key: 'marketing', label: t('portfolio.categories.marketingCampaigns') }
            ].map((category) => (
              <Link
                key={category.key}
                href={`/portfolio/${category.key}`}
                className="bg-gray-100 hover:bg-purple-100 text-gray-700 hover:text-purple-700 px-6 py-3 rounded-full font-medium transition-all duration-300 transform hover:scale-105"
              >
                {category.label}
              </Link>
            ))}
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center mt-16">
          <Link
            href="/portfolio"
            className="inline-block bg-gradient-to-r from-blue-600 to-purple-500 text-white font-bold px-8 py-4 rounded-full text-lg hover:from-blue-700 hover:to-purple-600 transition-all duration-300 transform hover:scale-105 hover:shadow-xl"
          >
            {t('portfolio.viewFullPortfolio')}
          </Link>
        </div>
      </div>
    </section>
  );
};

export default PortfolioSection;
