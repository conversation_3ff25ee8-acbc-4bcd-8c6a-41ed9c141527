{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/agency/apollo-agency-website/src/app/services/marketing-strategy/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useLanguage } from '@/contexts/LanguageContext';\nimport Link from 'next/link';\n\nexport default function MarketingStrategyPage() {\n  const { t, isRTL } = useLanguage();\n\n  return (\n    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : ''}`}>\n      {/* Hero Section */}\n      <section className=\"bg-gradient-to-br from-purple-900 via-indigo-800 to-blue-600 text-white py-20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-4xl mx-auto text-center\">\n            <div className=\"w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6\">\n              <span className=\"text-3xl\">📊</span>\n            </div>\n            <h1 className=\"text-4xl md:text-6xl font-bold mb-6\">\n              {t('services.marketingStrategy')}\n            </h1>\n            <p className=\"text-xl md:text-2xl mb-8 opacity-90\">\n              {t('services.marketingStrategy.subtitle')}\n            </p>\n            <Link\n              href=\"/contact\"\n              className=\"inline-block bg-gradient-to-r from-purple-500 to-blue-500 text-white font-bold px-8 py-4 rounded-full hover:from-purple-400 hover:to-blue-400 transition-all duration-300 transform hover:scale-105\"\n            >\n              {t('services.marketingStrategy.cta')}\n            </Link>\n          </div>\n        </div>\n      </section>\n\n      {/* Strategy Services */}\n      <section className=\"py-20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-6xl mx-auto\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-6\">\n                {t('services.marketingStrategy.servicesTitle')}\n              </h2>\n              <p className=\"text-xl text-gray-600\">\n                {t('services.marketingStrategy.servicesSubtitle')}\n              </p>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n              {[\n                {\n                  icon: '🎯',\n                  title: t('services.marketingStrategy.services.analysis.title'),\n                  description: t('services.marketingStrategy.services.analysis.description'),\n                  features: [\n                    t('services.marketingStrategy.services.analysis.feature1'),\n                    t('services.marketingStrategy.services.analysis.feature2'),\n                    t('services.marketingStrategy.services.analysis.feature3'),\n                    t('services.marketingStrategy.services.analysis.feature4')\n                  ]\n                },\n                {\n                  icon: '📈',\n                  title: t('services.marketingStrategy.services.planning.title'),\n                  description: t('services.marketingStrategy.services.planning.description'),\n                  features: [\n                    t('services.marketingStrategy.services.planning.feature1'),\n                    t('services.marketingStrategy.services.planning.feature2'),\n                    t('services.marketingStrategy.services.planning.feature3'),\n                    t('services.marketingStrategy.services.planning.feature4')\n                  ]\n                },\n                {\n                  icon: '🚀',\n                  title: t('services.marketingStrategy.services.execution.title'),\n                  description: t('services.marketingStrategy.services.execution.description'),\n                  features: [\n                    t('services.marketingStrategy.services.execution.feature1'),\n                    t('services.marketingStrategy.services.execution.feature2'),\n                    t('services.marketingStrategy.services.execution.feature3'),\n                    t('services.marketingStrategy.services.execution.feature4')\n                  ]\n                },\n                {\n                  icon: '📊',\n                  title: t('services.marketingStrategy.services.optimization.title'),\n                  description: t('services.marketingStrategy.services.optimization.description'),\n                  features: [\n                    t('services.marketingStrategy.services.optimization.feature1'),\n                    t('services.marketingStrategy.services.optimization.feature2'),\n                    t('services.marketingStrategy.services.optimization.feature3'),\n                    t('services.marketingStrategy.services.optimization.feature4')\n                  ]\n                },\n                {\n                  icon: '💡',\n                  title: t('services.marketingStrategy.services.consulting.title'),\n                  description: t('services.marketingStrategy.services.consulting.description'),\n                  features: [\n                    t('services.marketingStrategy.services.consulting.feature1'),\n                    t('services.marketingStrategy.services.consulting.feature2'),\n                    t('services.marketingStrategy.services.consulting.feature3'),\n                    t('services.marketingStrategy.services.consulting.feature4')\n                  ]\n                },\n                {\n                  icon: '🔄',\n                  title: t('services.marketingStrategy.services.growth.title'),\n                  description: t('services.marketingStrategy.services.growth.description'),\n                  features: [\n                    t('services.marketingStrategy.services.growth.feature1'),\n                    t('services.marketingStrategy.services.growth.feature2'),\n                    t('services.marketingStrategy.services.growth.feature3'),\n                    t('services.marketingStrategy.services.growth.feature4')\n                  ]\n                }\n              ].map((service, index) => (\n                <div key={index} className=\"bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300\">\n                  <div className=\"text-4xl mb-4\">{service.icon}</div>\n                  <h3 className=\"text-xl font-bold text-gray-900 mb-3\">{service.title}</h3>\n                  <p className=\"text-gray-600 mb-4\">{service.description}</p>\n                  <ul className=\"space-y-2\">\n                    {service.features.map((feature, featureIndex) => (\n                      <li key={featureIndex} className=\"flex items-center text-gray-600 text-sm\">\n                        <span className=\"text-purple-600 mr-2\">✓</span>\n                        {feature}\n                      </li>\n                    ))}\n                  </ul>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Strategy Process */}\n      <section className=\"py-20 bg-white\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-4xl mx-auto\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-6\">\n                {t('services.marketingStrategy.processTitle')}\n              </h2>\n              <p className=\"text-xl text-gray-600\">\n                {t('services.marketingStrategy.processSubtitle')}\n              </p>\n            </div>\n            \n            <div className=\"space-y-8\">\n              {[\n                { \n                  step: '01', \n                  title: t('services.marketingStrategy.process.step1.title'),\n                  description: t('services.marketingStrategy.process.step1.description')\n                },\n                { \n                  step: '02', \n                  title: t('services.marketingStrategy.process.step2.title'),\n                  description: t('services.marketingStrategy.process.step2.description')\n                },\n                { \n                  step: '03', \n                  title: t('services.marketingStrategy.process.step3.title'),\n                  description: t('services.marketingStrategy.process.step3.description')\n                },\n                { \n                  step: '04', \n                  title: t('services.marketingStrategy.process.step4.title'),\n                  description: t('services.marketingStrategy.process.step4.description')\n                },\n                { \n                  step: '05', \n                  title: t('services.marketingStrategy.process.step5.title'),\n                  description: t('services.marketingStrategy.process.step5.description')\n                }\n              ].map((item, index) => (\n                <div key={index} className=\"flex items-start space-x-6\">\n                  <div className=\"w-16 h-16 bg-gradient-to-r from-purple-600 to-blue-500 text-white rounded-full flex items-center justify-center text-xl font-bold flex-shrink-0\">\n                    {item.step}\n                  </div>\n                  <div>\n                    <h3 className=\"text-2xl font-bold text-gray-900 mb-3\">{item.title}</h3>\n                    <p className=\"text-gray-600 text-lg\">{item.description}</p>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Success Metrics */}\n      <section className=\"py-20 bg-gray-50\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-4xl mx-auto text-center\">\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-6\">\n              {t('services.marketingStrategy.metricsTitle')}\n            </h2>\n            <p className=\"text-xl text-gray-600 mb-12\">\n              {t('services.marketingStrategy.metricsSubtitle')}\n            </p>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n              <div className=\"text-center\">\n                <div className=\"text-4xl md:text-5xl font-bold text-purple-600 mb-2\">400%</div>\n                <div className=\"text-gray-600\">{t('services.marketingStrategy.metrics.roi')}</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-4xl md:text-5xl font-bold text-purple-600 mb-2\">150+</div>\n                <div className=\"text-gray-600\">{t('services.marketingStrategy.metrics.strategies')}</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-4xl md:text-5xl font-bold text-purple-600 mb-2\">95%</div>\n                <div className=\"text-gray-600\">{t('services.marketingStrategy.metrics.success')}</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-4xl md:text-5xl font-bold text-purple-600 mb-2\">24/7</div>\n                <div className=\"text-gray-600\">{t('services.marketingStrategy.metrics.support')}</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"bg-gradient-to-r from-purple-600 to-blue-500 text-white py-20\">\n        <div className=\"container mx-auto px-4 text-center\">\n          <h2 className=\"text-3xl md:text-4xl font-bold mb-6\">\n            {t('services.marketingStrategy.ctaTitle')}\n          </h2>\n          <p className=\"text-xl mb-8 opacity-90 max-w-2xl mx-auto\">\n            {t('services.marketingStrategy.ctaSubtitle')}\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Link\n              href=\"/contact\"\n              className=\"bg-white text-purple-600 font-bold px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 transform hover:scale-105\"\n            >\n              {t('services.marketingStrategy.ctaButton')}\n            </Link>\n            <Link\n              href=\"/portfolio/marketing\"\n              className=\"border-2 border-white text-white font-bold px-8 py-4 rounded-full hover:bg-white hover:text-purple-600 transition-all duration-300\"\n            >\n              {t('services.marketingStrategy.portfolioButton')}\n            </Link>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;IAE/B,qBACE,6LAAC;QAAI,WAAW,CAAC,wBAAwB,EAAE,QAAQ,QAAQ,IAAI;;0BAE7D,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAAW;;;;;;;;;;;0CAE7B,6LAAC;gCAAG,WAAU;0CACX,EAAE;;;;;;0CAEL,6LAAC;gCAAE,WAAU;0CACV,EAAE;;;;;;0CAEL,6LAAC,+JAA<PERSON>,CAAA,UAAI;gCA<PERSON>,MAAK;gCACL,WAAU;0CAET,EAAE;;;;;;;;;;;;;;;;;;;;;;0BAOX,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDACX,EAAE;;;;;;kDAEL,6LAAC;wCAAE,WAAU;kDACV,EAAE;;;;;;;;;;;;0CAIP,6LAAC;gCAAI,WAAU;0CACZ;oCACC;wCACE,MAAM;wCACN,OAAO,EAAE;wCACT,aAAa,EAAE;wCACf,UAAU;4CACR,EAAE;4CACF,EAAE;4CACF,EAAE;4CACF,EAAE;yCACH;oCACH;oCACA;wCACE,MAAM;wCACN,OAAO,EAAE;wCACT,aAAa,EAAE;wCACf,UAAU;4CACR,EAAE;4CACF,EAAE;4CACF,EAAE;4CACF,EAAE;yCACH;oCACH;oCACA;wCACE,MAAM;wCACN,OAAO,EAAE;wCACT,aAAa,EAAE;wCACf,UAAU;4CACR,EAAE;4CACF,EAAE;4CACF,EAAE;4CACF,EAAE;yCACH;oCACH;oCACA;wCACE,MAAM;wCACN,OAAO,EAAE;wCACT,aAAa,EAAE;wCACf,UAAU;4CACR,EAAE;4CACF,EAAE;4CACF,EAAE;4CACF,EAAE;yCACH;oCACH;oCACA;wCACE,MAAM;wCACN,OAAO,EAAE;wCACT,aAAa,EAAE;wCACf,UAAU;4CACR,EAAE;4CACF,EAAE;4CACF,EAAE;4CACF,EAAE;yCACH;oCACH;oCACA;wCACE,MAAM;wCACN,OAAO,EAAE;wCACT,aAAa,EAAE;wCACf,UAAU;4CACR,EAAE;4CACF,EAAE;4CACF,EAAE;4CACF,EAAE;yCACH;oCACH;iCACD,CAAC,GAAG,CAAC,CAAC,SAAS,sBACd,6LAAC;wCAAgB,WAAU;;0DACzB,6LAAC;gDAAI,WAAU;0DAAiB,QAAQ,IAAI;;;;;;0DAC5C,6LAAC;gDAAG,WAAU;0DAAwC,QAAQ,KAAK;;;;;;0DACnE,6LAAC;gDAAE,WAAU;0DAAsB,QAAQ,WAAW;;;;;;0DACtD,6LAAC;gDAAG,WAAU;0DACX,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BAC9B,6LAAC;wDAAsB,WAAU;;0EAC/B,6LAAC;gEAAK,WAAU;0EAAuB;;;;;;4DACtC;;uDAFM;;;;;;;;;;;uCANL;;;;;;;;;;;;;;;;;;;;;;;;;;0BAoBpB,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDACX,EAAE;;;;;;kDAEL,6LAAC;wCAAE,WAAU;kDACV,EAAE;;;;;;;;;;;;0CAIP,6LAAC;gCAAI,WAAU;0CACZ;oCACC;wCACE,MAAM;wCACN,OAAO,EAAE;wCACT,aAAa,EAAE;oCACjB;oCACA;wCACE,MAAM;wCACN,OAAO,EAAE;wCACT,aAAa,EAAE;oCACjB;oCACA;wCACE,MAAM;wCACN,OAAO,EAAE;wCACT,aAAa,EAAE;oCACjB;oCACA;wCACE,MAAM;wCACN,OAAO,EAAE;wCACT,aAAa,EAAE;oCACjB;oCACA;wCACE,MAAM;wCACN,OAAO,EAAE;wCACT,aAAa,EAAE;oCACjB;iCACD,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,6LAAC;wCAAgB,WAAU;;0DACzB,6LAAC;gDAAI,WAAU;0DACZ,KAAK,IAAI;;;;;;0DAEZ,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAyC,KAAK,KAAK;;;;;;kEACjE,6LAAC;wDAAE,WAAU;kEAAyB,KAAK,WAAW;;;;;;;;;;;;;uCANhD;;;;;;;;;;;;;;;;;;;;;;;;;;0BAgBpB,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CACX,EAAE;;;;;;0CAEL,6LAAC;gCAAE,WAAU;0CACV,EAAE;;;;;;0CAGL,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAsD;;;;;;0DACrE,6LAAC;gDAAI,WAAU;0DAAiB,EAAE;;;;;;;;;;;;kDAEpC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAsD;;;;;;0DACrE,6LAAC;gDAAI,WAAU;0DAAiB,EAAE;;;;;;;;;;;;kDAEpC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAsD;;;;;;0DACrE,6LAAC;gDAAI,WAAU;0DAAiB,EAAE;;;;;;;;;;;;kDAEpC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAsD;;;;;;0DACrE,6LAAC;gDAAI,WAAU;0DAAiB,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ5C,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCACX,EAAE;;;;;;sCAEL,6LAAC;4BAAE,WAAU;sCACV,EAAE;;;;;;sCAEL,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CAET,EAAE;;;;;;8CAEL,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CAET,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOjB;GArPwB;;QACD,sIAAA,CAAA,cAAW;;;KADV", "debugId": null}}]}