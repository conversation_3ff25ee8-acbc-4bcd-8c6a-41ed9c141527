(()=>{var e={};e.id=563,e.ids=[563],e.modules={2393:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>d});var r=s(65239),i=s(48088),a=s(88170),n=s.n(a),o=s(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let d={children:["",{children:["portfolio",{children:["graphic-design",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,51339)),"C:\\Users\\<USER>\\Desktop\\agency\\apollo-agency-website\\src\\app\\portfolio\\graphic-design\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\Desktop\\agency\\apollo-agency-website\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\agency\\apollo-agency-website\\src\\app\\portfolio\\graphic-design\\page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/portfolio/graphic-design/page",pathname:"/portfolio/graphic-design",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14745:(e,t,s)=>{Promise.resolve().then(s.bind(s,51339))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},51193:(e,t,s)=>{Promise.resolve().then(s.bind(s,92349))},51339:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\agency\\\\apollo-agency-website\\\\src\\\\app\\\\portfolio\\\\graphic-design\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\agency\\apollo-agency-website\\src\\app\\portfolio\\graphic-design\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var r=s(31658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},79551:e=>{"use strict";e.exports=require("url")},92349:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});var r=s(60687),i=s(34393),a=s(85814),n=s.n(a);function o(){let{t:e,isRTL:t}=(0,i.o)();return(0,r.jsxs)("div",{className:`min-h-screen bg-gray-50 ${t?"rtl":""}`,children:[(0,r.jsx)("section",{className:"bg-gradient-to-br from-orange-900 via-red-800 to-pink-600 text-white py-20",children:(0,r.jsx)("div",{className:"container mx-auto px-4",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto text-center",children:[(0,r.jsx)("div",{className:"w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,r.jsx)("span",{className:"text-3xl",children:"\uD83C\uDFA8"})}),(0,r.jsxs)("h1",{className:"text-4xl md:text-6xl font-bold mb-6",children:[e("portfolio.categories.graphicDesign")," Portfolio"]}),(0,r.jsx)("p",{className:"text-xl md:text-2xl mb-8 opacity-90",children:"Creative designs that communicate your brand message effectively"}),(0,r.jsx)(n(),{href:"/contact",className:"inline-block bg-gradient-to-r from-yellow-400 to-orange-500 text-gray-900 font-bold px-8 py-4 rounded-full hover:from-yellow-300 hover:to-orange-400 transition-all duration-300 transform hover:scale-105",children:"Start Your Design Project"})]})})}),(0,r.jsx)("section",{className:"py-20",children:(0,r.jsx)("div",{className:"container mx-auto px-4",children:(0,r.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,r.jsxs)("div",{className:"text-center mb-16",children:[(0,r.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-6",children:"Featured Design Projects"}),(0,r.jsx)("p",{className:"text-xl text-gray-600",children:"Creative solutions that make an impact"})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[{id:1,title:"TechCorp Brand Identity",description:"Complete brand identity design including logo, business cards, and marketing materials",deliverables:["Logo Design","Business Cards","Letterhead","Brand Guidelines"],results:["200% increase in brand recognition","Professional brand image","Consistent visual identity"],image:"\uD83C\uDFA8",category:"Brand Identity"},{id:2,title:"Restaurant Menu Design",description:"Modern menu design with food photography and layout optimization",deliverables:["Menu Design","Food Photography","Table Tents","Digital Menu"],results:["30% increase in average order value","Improved customer experience","Modern brand image"],image:"\uD83C\uDF7D️",category:"Print Design"},{id:3,title:"E-commerce Product Catalog",description:"Product catalog design for online fashion retailer",deliverables:["Catalog Layout","Product Photography","Digital Brochure","Social Media Graphics"],results:["50% increase in product views","Higher conversion rates","Professional presentation"],image:"\uD83D\uDCD6",category:"Catalog Design"},{id:4,title:"Healthcare Infographics",description:"Educational infographics for healthcare awareness campaign",deliverables:["Infographic Design","Social Media Graphics","Poster Design","Digital Assets"],results:["1M+ views on social media","Increased health awareness","Professional credibility"],image:"\uD83D\uDCCA",category:"Infographic Design"}].map(e=>(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300",children:[(0,r.jsxs)("div",{className:"bg-gradient-to-r from-orange-100 to-red-100 p-8 text-center",children:[(0,r.jsx)("div",{className:"text-6xl mb-4",children:e.image}),(0,r.jsx)("span",{className:"bg-white/80 text-orange-600 px-3 py-1 rounded-full text-sm font-medium",children:e.category})]}),(0,r.jsxs)("div",{className:"p-8",children:[(0,r.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-4",children:e.title}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:e.description}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"Deliverables:"}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:e.deliverables.map((e,t)=>(0,r.jsx)("span",{className:"bg-orange-100 text-orange-600 px-3 py-1 rounded-full text-sm",children:e},t))})]}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h4",{className:"font-semibold text-gray-900 mb-3",children:"Results Achieved:"}),(0,r.jsx)("ul",{className:"space-y-2",children:e.results.map((e,t)=>(0,r.jsxs)("li",{className:"flex items-center text-green-600",children:[(0,r.jsx)("span",{className:"mr-2",children:"✓"}),e]},t))})]}),(0,r.jsxs)(n(),{href:`/case-studies/${e.id}`,className:"inline-flex items-center bg-gradient-to-r from-orange-600 to-red-500 text-white font-bold px-6 py-3 rounded-lg hover:from-orange-700 hover:to-red-600 transition-all duration-300",children:["View Full Case Study",(0,r.jsx)("svg",{className:`w-4 h-4 ${t?"mr-2 rotate-180":"ml-2"}`,fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})]})]})]},e.id))})]})})}),(0,r.jsx)("section",{className:"py-20 bg-white",children:(0,r.jsx)("div",{className:"container mx-auto px-4",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,r.jsxs)("div",{className:"text-center mb-16",children:[(0,r.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-6",children:"Our Graphic Design Services"}),(0,r.jsx)("p",{className:"text-xl text-gray-600",children:"Professional design solutions for all your business needs"})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:[{icon:"\uD83C\uDFF7️",title:"Logo Design",description:"Memorable logos that represent your brand"},{icon:"\uD83D\uDCC4",title:"Print Design",description:"Brochures, flyers, and marketing materials"},{icon:"\uD83D\uDCF1",title:"Digital Graphics",description:"Social media graphics and web assets"},{icon:"\uD83D\uDCCA",title:"Infographics",description:"Data visualization and information design"},{icon:"\uD83D\uDCE6",title:"Packaging Design",description:"Product packaging that stands out"},{icon:"\uD83C\uDFAF",title:"Brand Identity",description:"Complete brand identity systems"}].map((e,t)=>(0,r.jsxs)("div",{className:"text-center p-6 rounded-lg hover:bg-gray-50 transition-colors duration-300",children:[(0,r.jsx)("div",{className:"text-4xl mb-4",children:e.icon}),(0,r.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-3",children:e.title}),(0,r.jsx)("p",{className:"text-gray-600",children:e.description})]},t))})]})})}),(0,r.jsx)("section",{className:"py-20 bg-gray-50",children:(0,r.jsx)("div",{className:"container mx-auto px-4",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,r.jsxs)("div",{className:"text-center mb-16",children:[(0,r.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-6",children:"Our Design Process"}),(0,r.jsx)("p",{className:"text-xl text-gray-600",children:"From concept to completion, we ensure every design meets your goals"})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[{step:"01",title:"Discovery",description:"Understanding your brand and requirements"},{step:"02",title:"Concept",description:"Creating initial design concepts and ideas"},{step:"03",title:"Design",description:"Developing the final design with your feedback"},{step:"04",title:"Delivery",description:"Providing all files and formats you need"}].map((e,t)=>(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-gradient-to-r from-orange-600 to-red-500 text-white rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4",children:e.step}),(0,r.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-3",children:e.title}),(0,r.jsx)("p",{className:"text-gray-600",children:e.description})]},t))})]})})}),(0,r.jsx)("section",{className:"bg-gradient-to-r from-orange-600 to-red-500 text-white py-20",children:(0,r.jsxs)("div",{className:"container mx-auto px-4 text-center",children:[(0,r.jsx)("h2",{className:"text-3xl md:text-4xl font-bold mb-6",children:"Ready to Create Amazing Designs?"}),(0,r.jsx)("p",{className:"text-xl mb-8 opacity-90 max-w-2xl mx-auto",children:"Let's bring your vision to life with professional graphic design that makes an impact."}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,r.jsx)(n(),{href:"/contact",className:"bg-white text-orange-600 font-bold px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 transform hover:scale-105",children:"Start Your Design Project"}),(0,r.jsx)(n(),{href:"/portfolio",className:"border-2 border-white text-white font-bold px-8 py-4 rounded-full hover:bg-white hover:text-orange-600 transition-all duration-300",children:"View All Projects"})]})]})})]})}}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,584,658,845],()=>s(2393));module.exports=r})();