'use client';

import { useLanguage } from '@/contexts/LanguageContext';
import Link from 'next/link';

export default function BlogPage() {
  const { t, isRTL } = useLanguage();

  const blogPosts = [
    {
      id: 1,
      title: 'How Your Business Appears in Google\'s First Search Results',
      excerpt: 'Learn the essential strategies to improve your website\'s visibility and rank higher in Google search results.',
      category: 'SEO',
      date: 'June 15, 2024',
      readTime: '5 min read',
      image: '🔍',
      author: '<PERSON>'
    },
    {
      id: 2,
      title: 'Advertising and Marketing Office in Saudi Arabia',
      excerpt: 'Discover the best practices for establishing a successful marketing presence in the Saudi Arabian market.',
      category: 'Marketing',
      date: 'June 10, 2024',
      readTime: '7 min read',
      image: '🏢',
      author: '<PERSON>'
    },
    {
      id: 3,
      title: 'Search Engine Optimization Services',
      excerpt: 'Comprehensive guide to SEO services and how they can transform your online presence and drive organic traffic.',
      category: 'SEO',
      date: 'June 5, 2024',
      readTime: '6 min read',
      image: '📈',
      author: '<PERSON>'
    },
    {
      id: 4,
      title: 'Advertising Print Design',
      excerpt: 'Master the art of creating compelling print advertisements that capture attention and drive results.',
      category: 'Design',
      date: 'May 30, 2024',
      readTime: '4 min read',
      image: '🎨',
      author: 'David Chen'
    },
    {
      id: 5,
      title: 'Key Elements in Logo Creation',
      excerpt: 'Understand the fundamental principles of professional logo design and brand identity development.',
      category: 'Branding',
      date: 'May 25, 2024',
      readTime: '5 min read',
      image: '🎯',
      author: 'Lisa Thompson'
    },
    {
      id: 6,
      title: 'Understanding B2C vs B2B Marketing',
      excerpt: 'Explore the key differences between B2C and B2B marketing strategies and when to use each approach.',
      category: 'Strategy',
      date: 'May 20, 2024',
      readTime: '8 min read',
      image: '📊',
      author: 'Michael Brown'
    }
  ];

  const categories = ['All', 'SEO', 'Marketing', 'Design', 'Branding', 'Strategy'];

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : ''}`}>
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-green-900 via-blue-800 to-purple-600 text-white py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              {t('header.blog')}
            </h1>
            <p className="text-xl md:text-2xl mb-8 opacity-90">
              {t('blog.subtitle')}
            </p>
          </div>
        </div>
      </section>

      {/* Category Filter */}
      <section className="py-12 bg-white">
        <div className="container mx-auto px-4">
          <div className="flex flex-wrap justify-center gap-4">
            {categories.map((category) => (
              <button
                key={category}
                className="px-6 py-2 rounded-full border-2 border-green-200 text-green-600 hover:bg-green-600 hover:text-white transition-all duration-300 font-medium"
              >
                {category === 'All' ? t('blog.categories.all') : category}
              </button>
            ))}
          </div>
        </div>
      </section>

      {/* Blog Grid */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {blogPosts.map((post) => (
              <article
                key={post.id}
                className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 overflow-hidden"
              >
                {/* Image/Icon */}
                <div className="h-48 bg-gradient-to-br from-green-100 to-blue-100 flex items-center justify-center relative">
                  <div className="text-6xl">{post.image}</div>
                  {/* Category Badge */}
                  <div className="absolute top-4 left-4">
                    <span className="bg-white/90 backdrop-blur-sm text-green-600 px-3 py-1 rounded-full text-sm font-medium">
                      {post.category}
                    </span>
                  </div>
                </div>

                {/* Content */}
                <div className="p-6">
                  {/* Meta Info */}
                  <div className="flex items-center text-sm text-gray-500 mb-3">
                    <span>{post.date}</span>
                    <span className="mx-2">•</span>
                    <span>{post.readTime}</span>
                  </div>

                  {/* Title */}
                  <h3 className="text-xl font-bold text-gray-900 mb-3 line-clamp-2">
                    {post.title}
                  </h3>

                  {/* Excerpt */}
                  <p className="text-gray-600 mb-4 line-clamp-3">
                    {post.excerpt}
                  </p>

                  {/* Author */}
                  <div className="flex items-center mb-4">
                    <div className="w-8 h-8 bg-gradient-to-r from-green-100 to-blue-100 rounded-full flex items-center justify-center mr-3">
                      <span className="text-sm">👤</span>
                    </div>
                    <span className="text-sm text-gray-600">{post.author}</span>
                  </div>

                  {/* Read More Link */}
                  <Link
                    href={`/blog/${post.id}`}
                    className="inline-flex items-center text-green-600 hover:text-green-700 font-semibold transition-colors duration-300"
                  >
                    {t('blog.readMore')}
                    <svg className={`w-4 h-4 transform group-hover:translate-x-1 transition-transform duration-300 ${isRTL ? 'mr-2 rotate-180' : 'ml-2'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </Link>
                </div>
              </article>
            ))}
          </div>
        </div>
      </section>

      {/* Newsletter Signup */}
      <section className="bg-white py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Stay Updated
            </h2>
            <p className="text-xl text-gray-600 mb-8">
              Subscribe to our newsletter for the latest insights and tips
            </p>
            <div className={`flex flex-col sm:flex-row gap-4 justify-center max-w-md mx-auto ${isRTL ? 'sm:flex-row-reverse' : ''}`}>
              <input
                type="email"
                placeholder="Enter your email"
                className="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
              <button className="bg-gradient-to-r from-green-600 to-blue-500 text-white font-bold px-6 py-3 rounded-lg hover:from-green-700 hover:to-blue-600 transition-all duration-300">
                Subscribe
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-gradient-to-r from-green-600 to-blue-500 text-white py-20">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Need Marketing Help?
          </h2>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Our team of experts is ready to help you achieve your marketing goals.
          </p>
          <Link
            href="/contact"
            className="bg-white text-green-600 font-bold px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 transform hover:scale-105"
          >
            Get Expert Help
          </Link>
        </div>
      </section>
    </div>
  );
}
