{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/agency/apollo-agency-website/src/app/services/graphic-design/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useLanguage } from '@/contexts/LanguageContext';\nimport Link from 'next/link';\n\nexport default function GraphicDesignPage() {\n  const { t, isRTL } = useLanguage();\n\n  return (\n    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : ''}`}>\n      {/* Hero Section */}\n      <section className=\"bg-gradient-to-br from-orange-900 via-red-800 to-pink-600 text-white py-20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-4xl mx-auto text-center\">\n            <div className=\"w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6\">\n              <span className=\"text-3xl\">🎨</span>\n            </div>\n            <h1 className=\"text-4xl md:text-6xl font-bold mb-6\">\n              {t('services.graphicDesign')}\n            </h1>\n            <p className=\"text-xl md:text-2xl mb-8 opacity-90\">\n              Creative designs that communicate your brand message and captivate your audience\n            </p>\n            <Link\n              href=\"/contact\"\n              className=\"inline-block bg-gradient-to-r from-yellow-400 to-orange-500 text-gray-900 font-bold px-8 py-4 rounded-full hover:from-yellow-300 hover:to-orange-400 transition-all duration-300 transform hover:scale-105\"\n            >\n              Start Your Design Project\n            </Link>\n          </div>\n        </div>\n      </section>\n\n      {/* Design Services */}\n      <section className=\"py-20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-6xl mx-auto\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-6\">\n                Complete Graphic Design Solutions\n              </h2>\n              <p className=\"text-xl text-gray-600\">\n                From concept to completion, we create designs that make an impact\n              </p>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n              {[\n                {\n                  icon: '🏷️',\n                  title: 'Logo Design',\n                  description: 'Memorable logos that represent your brand essence and values.',\n                  features: ['Brand research', 'Concept development', 'Multiple variations', 'Vector files']\n                },\n                {\n                  icon: '📄',\n                  title: 'Print Design',\n                  description: 'Professional print materials that make a lasting impression.',\n                  features: ['Brochures & flyers', 'Business cards', 'Posters & banners', 'Print-ready files']\n                },\n                {\n                  icon: '📱',\n                  title: 'Digital Graphics',\n                  description: 'Eye-catching digital assets for web and social media.',\n                  features: ['Social media graphics', 'Web banners', 'Email templates', 'Digital ads']\n                },\n                {\n                  icon: '📊',\n                  title: 'Infographics',\n                  description: 'Data visualization that makes complex information easy to understand.',\n                  features: ['Data visualization', 'Statistical graphics', 'Process diagrams', 'Educational content']\n                },\n                {\n                  icon: '📦',\n                  title: 'Packaging Design',\n                  description: 'Product packaging that stands out on shelves and online.',\n                  features: ['Product packaging', 'Label design', 'Box design', '3D mockups']\n                },\n                {\n                  icon: '🎯',\n                  title: 'Brand Identity',\n                  description: 'Complete visual identity systems that build brand recognition.',\n                  features: ['Brand guidelines', 'Color palettes', 'Typography', 'Visual elements']\n                }\n              ].map((service, index) => (\n                <div key={index} className=\"bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300\">\n                  <div className=\"text-4xl mb-4\">{service.icon}</div>\n                  <h3 className=\"text-xl font-bold text-gray-900 mb-3\">{service.title}</h3>\n                  <p className=\"text-gray-600 mb-4\">{service.description}</p>\n                  <ul className=\"space-y-2\">\n                    {service.features.map((feature, featureIndex) => (\n                      <li key={featureIndex} className=\"flex items-center text-gray-600 text-sm\">\n                        <span className=\"text-orange-600 mr-2\">✓</span>\n                        {feature}\n                      </li>\n                    ))}\n                  </ul>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Design Specialties */}\n      <section className=\"py-20 bg-white\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-4xl mx-auto\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-6\">\n                Our Design Specialties\n              </h2>\n              <p className=\"text-xl text-gray-600\">\n                Expertise across various design disciplines and industries\n              </p>\n            </div>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n              {[\n                { specialty: 'Corporate Design', icon: '🏢', description: 'Professional business materials' },\n                { specialty: 'E-commerce Design', icon: '🛒', description: 'Product and promotional graphics' },\n                { specialty: 'Healthcare Design', icon: '🏥', description: 'Medical and wellness materials' },\n                { specialty: 'Restaurant Design', icon: '🍽️', description: 'Food and hospitality graphics' },\n                { specialty: 'Tech Design', icon: '💻', description: 'Software and app graphics' },\n                { specialty: 'Fashion Design', icon: '👗', description: 'Style and lifestyle graphics' },\n                { specialty: 'Real Estate Design', icon: '🏠', description: 'Property marketing materials' },\n                { specialty: 'Event Design', icon: '🎉', description: 'Event and celebration graphics' }\n              ].map((specialty, index) => (\n                <div key={index} className=\"text-center p-6 bg-gray-50 rounded-lg hover:bg-orange-50 transition-colors duration-300\">\n                  <div className=\"text-4xl mb-3\">{specialty.icon}</div>\n                  <h3 className=\"text-lg font-bold text-gray-900 mb-2\">{specialty.specialty}</h3>\n                  <p className=\"text-gray-600 text-sm\">{specialty.description}</p>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Design Process */}\n      <section className=\"py-20 bg-gray-50\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-4xl mx-auto\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-6\">\n                Our Design Process\n              </h2>\n              <p className=\"text-xl text-gray-600\">\n                From concept to completion, ensuring every design meets your goals\n              </p>\n            </div>\n            \n            <div className=\"space-y-8\">\n              {[\n                { \n                  step: '01', \n                  title: 'Discovery & Brief', \n                  description: 'Understanding your brand, goals, and design requirements in detail.' \n                },\n                { \n                  step: '02', \n                  title: 'Concept Development', \n                  description: 'Creating initial design concepts and exploring creative directions.' \n                },\n                { \n                  step: '03', \n                  title: 'Design & Refinement', \n                  description: 'Developing chosen concepts with your feedback and refinements.' \n                },\n                { \n                  step: '04', \n                  title: 'Finalization & Delivery', \n                  description: 'Preparing final files in all required formats for immediate use.' \n                }\n              ].map((item, index) => (\n                <div key={index} className=\"flex items-start space-x-6\">\n                  <div className=\"w-16 h-16 bg-gradient-to-r from-orange-600 to-red-500 text-white rounded-full flex items-center justify-center text-xl font-bold flex-shrink-0\">\n                    {item.step}\n                  </div>\n                  <div>\n                    <h3 className=\"text-2xl font-bold text-gray-900 mb-3\">{item.title}</h3>\n                    <p className=\"text-gray-600 text-lg\">{item.description}</p>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Design Tools & Software */}\n      <section className=\"py-20 bg-white\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-4xl mx-auto text-center\">\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-6\">\n              Professional Design Tools\n            </h2>\n            <p className=\"text-xl text-gray-600 mb-12\">\n              We use industry-leading software to create exceptional designs\n            </p>\n            \n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-8\">\n              {[\n                { tool: 'Adobe Illustrator', icon: '🎨' },\n                { tool: 'Adobe Photoshop', icon: '📸' },\n                { tool: 'Adobe InDesign', icon: '📄' },\n                { tool: 'Figma', icon: '🎯' },\n                { tool: 'Sketch', icon: '✏️' },\n                { tool: 'Canva Pro', icon: '🖌️' },\n                { tool: 'After Effects', icon: '🎬' },\n                { tool: 'Procreate', icon: '🎭' }\n              ].map((tool, index) => (\n                <div key={index} className=\"text-center p-4\">\n                  <div className=\"text-4xl mb-2\">{tool.icon}</div>\n                  <p className=\"text-gray-700 font-medium\">{tool.tool}</p>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Success Metrics */}\n      <section className=\"py-20 bg-gray-50\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-4xl mx-auto text-center\">\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-6\">\n              Design Success Metrics\n            </h2>\n            <p className=\"text-xl text-gray-600 mb-12\">\n              Quality designs that deliver measurable results\n            </p>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n              <div className=\"text-center\">\n                <div className=\"text-4xl md:text-5xl font-bold text-orange-600 mb-2\">1000+</div>\n                <div className=\"text-gray-600\">Designs Created</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-4xl md:text-5xl font-bold text-orange-600 mb-2\">98%</div>\n                <div className=\"text-gray-600\">Client Satisfaction</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-4xl md:text-5xl font-bold text-orange-600 mb-2\">48h</div>\n                <div className=\"text-gray-600\">Average Turnaround</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-4xl md:text-5xl font-bold text-orange-600 mb-2\">200%</div>\n                <div className=\"text-gray-600\">Brand Recognition Boost</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"bg-gradient-to-r from-orange-600 to-red-500 text-white py-20\">\n        <div className=\"container mx-auto px-4 text-center\">\n          <h2 className=\"text-3xl md:text-4xl font-bold mb-6\">\n            Ready to Create Amazing Designs?\n          </h2>\n          <p className=\"text-xl mb-8 opacity-90 max-w-2xl mx-auto\">\n            Let's bring your vision to life with professional graphic design that makes an impact.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Link\n              href=\"/contact\"\n              className=\"bg-white text-orange-600 font-bold px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 transform hover:scale-105\"\n            >\n              Start Your Design Project\n            </Link>\n            <Link\n              href=\"/portfolio/graphic-design\"\n              className=\"border-2 border-white text-white font-bold px-8 py-4 rounded-full hover:bg-white hover:text-orange-600 transition-all duration-300\"\n            >\n              View Design Portfolio\n            </Link>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,cAAW,AAAD;IAE/B,qBACE,8OAAC;QAAI,WAAW,CAAC,wBAAwB,EAAE,QAAQ,QAAQ,IAAI;;0BAE7D,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CAAW;;;;;;;;;;;0CAE7B,8OAAC;gCAAG,WAAU;0CACX,EAAE;;;;;;0CAEL,8OAAC;gCAAE,WAAU;0CAAsC;;;;;;0CAGnD,8OAAC,4JAAA,CAAA,UAAI;gCA<PERSON>,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;0BAQP,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAGlE,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAKvC,8OAAC;gCAAI,WAAU;0CACZ;oCACC;wCACE,MAAM;wCACN,OAAO;wCACP,aAAa;wCACb,UAAU;4CAAC;4CAAkB;4CAAuB;4CAAuB;yCAAe;oCAC5F;oCACA;wCACE,MAAM;wCACN,OAAO;wCACP,aAAa;wCACb,UAAU;4CAAC;4CAAsB;4CAAkB;4CAAqB;yCAAoB;oCAC9F;oCACA;wCACE,MAAM;wCACN,OAAO;wCACP,aAAa;wCACb,UAAU;4CAAC;4CAAyB;4CAAe;4CAAmB;yCAAc;oCACtF;oCACA;wCACE,MAAM;wCACN,OAAO;wCACP,aAAa;wCACb,UAAU;4CAAC;4CAAsB;4CAAwB;4CAAoB;yCAAsB;oCACrG;oCACA;wCACE,MAAM;wCACN,OAAO;wCACP,aAAa;wCACb,UAAU;4CAAC;4CAAqB;4CAAgB;4CAAc;yCAAa;oCAC7E;oCACA;wCACE,MAAM;wCACN,OAAO;wCACP,aAAa;wCACb,UAAU;4CAAC;4CAAoB;4CAAkB;4CAAc;yCAAkB;oCACnF;iCACD,CAAC,GAAG,CAAC,CAAC,SAAS,sBACd,8OAAC;wCAAgB,WAAU;;0DACzB,8OAAC;gDAAI,WAAU;0DAAiB,QAAQ,IAAI;;;;;;0DAC5C,8OAAC;gDAAG,WAAU;0DAAwC,QAAQ,KAAK;;;;;;0DACnE,8OAAC;gDAAE,WAAU;0DAAsB,QAAQ,WAAW;;;;;;0DACtD,8OAAC;gDAAG,WAAU;0DACX,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BAC9B,8OAAC;wDAAsB,WAAU;;0EAC/B,8OAAC;gEAAK,WAAU;0EAAuB;;;;;;4DACtC;;uDAFM;;;;;;;;;;;uCANL;;;;;;;;;;;;;;;;;;;;;;;;;;0BAoBpB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAGlE,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAKvC,8OAAC;gCAAI,WAAU;0CACZ;oCACC;wCAAE,WAAW;wCAAoB,MAAM;wCAAM,aAAa;oCAAkC;oCAC5F;wCAAE,WAAW;wCAAqB,MAAM;wCAAM,aAAa;oCAAmC;oCAC9F;wCAAE,WAAW;wCAAqB,MAAM;wCAAM,aAAa;oCAAiC;oCAC5F;wCAAE,WAAW;wCAAqB,MAAM;wCAAO,aAAa;oCAAgC;oCAC5F;wCAAE,WAAW;wCAAe,MAAM;wCAAM,aAAa;oCAA4B;oCACjF;wCAAE,WAAW;wCAAkB,MAAM;wCAAM,aAAa;oCAA+B;oCACvF;wCAAE,WAAW;wCAAsB,MAAM;wCAAM,aAAa;oCAA+B;oCAC3F;wCAAE,WAAW;wCAAgB,MAAM;wCAAM,aAAa;oCAAiC;iCACxF,CAAC,GAAG,CAAC,CAAC,WAAW,sBAChB,8OAAC;wCAAgB,WAAU;;0DACzB,8OAAC;gDAAI,WAAU;0DAAiB,UAAU,IAAI;;;;;;0DAC9C,8OAAC;gDAAG,WAAU;0DAAwC,UAAU,SAAS;;;;;;0DACzE,8OAAC;gDAAE,WAAU;0DAAyB,UAAU,WAAW;;;;;;;uCAHnD;;;;;;;;;;;;;;;;;;;;;;;;;;0BAYpB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAGlE,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAKvC,8OAAC;gCAAI,WAAU;0CACZ;oCACC;wCACE,MAAM;wCACN,OAAO;wCACP,aAAa;oCACf;oCACA;wCACE,MAAM;wCACN,OAAO;wCACP,aAAa;oCACf;oCACA;wCACE,MAAM;wCACN,OAAO;wCACP,aAAa;oCACf;oCACA;wCACE,MAAM;wCACN,OAAO;wCACP,aAAa;oCACf;iCACD,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,8OAAC;wCAAgB,WAAU;;0DACzB,8OAAC;gDAAI,WAAU;0DACZ,KAAK,IAAI;;;;;;0DAEZ,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAyC,KAAK,KAAK;;;;;;kEACjE,8OAAC;wDAAE,WAAU;kEAAyB,KAAK,WAAW;;;;;;;;;;;;;uCANhD;;;;;;;;;;;;;;;;;;;;;;;;;;0BAgBpB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,8OAAC;gCAAE,WAAU;0CAA8B;;;;;;0CAI3C,8OAAC;gCAAI,WAAU;0CACZ;oCACC;wCAAE,MAAM;wCAAqB,MAAM;oCAAK;oCACxC;wCAAE,MAAM;wCAAmB,MAAM;oCAAK;oCACtC;wCAAE,MAAM;wCAAkB,MAAM;oCAAK;oCACrC;wCAAE,MAAM;wCAAS,MAAM;oCAAK;oCAC5B;wCAAE,MAAM;wCAAU,MAAM;oCAAK;oCAC7B;wCAAE,MAAM;wCAAa,MAAM;oCAAM;oCACjC;wCAAE,MAAM;wCAAiB,MAAM;oCAAK;oCACpC;wCAAE,MAAM;wCAAa,MAAM;oCAAK;iCACjC,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,8OAAC;wCAAgB,WAAU;;0DACzB,8OAAC;gDAAI,WAAU;0DAAiB,KAAK,IAAI;;;;;;0DACzC,8OAAC;gDAAE,WAAU;0DAA6B,KAAK,IAAI;;;;;;;uCAF3C;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWpB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,8OAAC;gCAAE,WAAU;0CAA8B;;;;;;0CAI3C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAsD;;;;;;0DACrE,8OAAC;gDAAI,WAAU;0DAAgB;;;;;;;;;;;;kDAEjC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAsD;;;;;;0DACrE,8OAAC;gDAAI,WAAU;0DAAgB;;;;;;;;;;;;kDAEjC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAsD;;;;;;0DACrE,8OAAC;gDAAI,WAAU;0DAAgB;;;;;;;;;;;;kDAEjC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAsD;;;;;;0DACrE,8OAAC;gDAAI,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQzC,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAsC;;;;;;sCAGpD,8OAAC;4BAAE,WAAU;sCAA4C;;;;;;sCAGzD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}]}