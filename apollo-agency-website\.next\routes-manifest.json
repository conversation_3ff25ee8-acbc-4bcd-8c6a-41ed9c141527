{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:file((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/]+\\.\\w+)/", "destination": "/:file", "internal": true, "missing": [{"type": "header", "key": "x-nextjs-data"}], "statusCode": 308, "regex": "^(?:/((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/]+\\.\\w+))/$"}, {"source": "/:notfile((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/\\.]+)", "destination": "/:notfile/", "internal": true, "statusCode": 308, "regex": "^(?:/((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/\\.]+))$"}], "headers": [], "dynamicRoutes": [], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/about", "regex": "^/about(?:/)?$", "routeKeys": {}, "namedRegex": "^/about(?:/)?$"}, {"page": "/blog", "regex": "^/blog(?:/)?$", "routeKeys": {}, "namedRegex": "^/blog(?:/)?$"}, {"page": "/blog/digital-marketing", "regex": "^/blog/digital\\-marketing(?:/)?$", "routeKeys": {}, "namedRegex": "^/blog/digital\\-marketing(?:/)?$"}, {"page": "/blog/graphic-design", "regex": "^/blog/graphic\\-design(?:/)?$", "routeKeys": {}, "namedRegex": "^/blog/graphic\\-design(?:/)?$"}, {"page": "/blog/search-engine-marketing", "regex": "^/blog/search\\-engine\\-marketing(?:/)?$", "routeKeys": {}, "namedRegex": "^/blog/search\\-engine\\-marketing(?:/)?$"}, {"page": "/blog/seo", "regex": "^/blog/seo(?:/)?$", "routeKeys": {}, "namedRegex": "^/blog/seo(?:/)?$"}, {"page": "/blog/social-media-marketing", "regex": "^/blog/social\\-media\\-marketing(?:/)?$", "routeKeys": {}, "namedRegex": "^/blog/social\\-media\\-marketing(?:/)?$"}, {"page": "/blog/video-marketing", "regex": "^/blog/video\\-marketing(?:/)?$", "routeKeys": {}, "namedRegex": "^/blog/video\\-marketing(?:/)?$"}, {"page": "/case-studies", "regex": "^/case\\-studies(?:/)?$", "routeKeys": {}, "namedRegex": "^/case\\-studies(?:/)?$"}, {"page": "/clients", "regex": "^/clients(?:/)?$", "routeKeys": {}, "namedRegex": "^/clients(?:/)?$"}, {"page": "/consultation", "regex": "^/consultation(?:/)?$", "routeKeys": {}, "namedRegex": "^/consultation(?:/)?$"}, {"page": "/contact", "regex": "^/contact(?:/)?$", "routeKeys": {}, "namedRegex": "^/contact(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/portfolio", "regex": "^/portfolio(?:/)?$", "routeKeys": {}, "namedRegex": "^/portfolio(?:/)?$"}, {"page": "/portfolio/brand-identity", "regex": "^/portfolio/brand\\-identity(?:/)?$", "routeKeys": {}, "namedRegex": "^/portfolio/brand\\-identity(?:/)?$"}, {"page": "/portfolio/branding", "regex": "^/portfolio/branding(?:/)?$", "routeKeys": {}, "namedRegex": "^/portfolio/branding(?:/)?$"}, {"page": "/portfolio/graphic-design", "regex": "^/portfolio/graphic\\-design(?:/)?$", "routeKeys": {}, "namedRegex": "^/portfolio/graphic\\-design(?:/)?$"}, {"page": "/portfolio/landing-pages", "regex": "^/portfolio/landing\\-pages(?:/)?$", "routeKeys": {}, "namedRegex": "^/portfolio/landing\\-pages(?:/)?$"}, {"page": "/portfolio/marketing", "regex": "^/portfolio/marketing(?:/)?$", "routeKeys": {}, "namedRegex": "^/portfolio/marketing(?:/)?$"}, {"page": "/portfolio/social-media", "regex": "^/portfolio/social\\-media(?:/)?$", "routeKeys": {}, "namedRegex": "^/portfolio/social\\-media(?:/)?$"}, {"page": "/services", "regex": "^/services(?:/)?$", "routeKeys": {}, "namedRegex": "^/services(?:/)?$"}, {"page": "/services/branding", "regex": "^/services/branding(?:/)?$", "routeKeys": {}, "namedRegex": "^/services/branding(?:/)?$"}, {"page": "/services/content-creation", "regex": "^/services/content\\-creation(?:/)?$", "routeKeys": {}, "namedRegex": "^/services/content\\-creation(?:/)?$"}, {"page": "/services/digital-marketing", "regex": "^/services/digital\\-marketing(?:/)?$", "routeKeys": {}, "namedRegex": "^/services/digital\\-marketing(?:/)?$"}, {"page": "/services/graphic-design", "regex": "^/services/graphic\\-design(?:/)?$", "routeKeys": {}, "namedRegex": "^/services/graphic\\-design(?:/)?$"}, {"page": "/services/influencer-marketing", "regex": "^/services/influencer\\-marketing(?:/)?$", "routeKeys": {}, "namedRegex": "^/services/influencer\\-marketing(?:/)?$"}, {"page": "/services/marketing-strategy", "regex": "^/services/marketing\\-strategy(?:/)?$", "routeKeys": {}, "namedRegex": "^/services/marketing\\-strategy(?:/)?$"}, {"page": "/services/seo", "regex": "^/services/seo(?:/)?$", "routeKeys": {}, "namedRegex": "^/services/seo(?:/)?$"}, {"page": "/services/social-media", "regex": "^/services/social\\-media(?:/)?$", "routeKeys": {}, "namedRegex": "^/services/social\\-media(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}