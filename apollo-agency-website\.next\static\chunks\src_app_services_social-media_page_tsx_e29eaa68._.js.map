{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/agency/apollo-agency-website/src/app/services/social-media/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useLanguage } from '@/contexts/LanguageContext';\nimport Link from 'next/link';\n\nexport default function SocialMediaPage() {\n  const { t, isRTL } = useLanguage();\n\n  return (\n    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : ''}`}>\n      {/* Hero Section */}\n      <section className=\"bg-gradient-to-br from-blue-900 via-purple-800 to-pink-600 text-white py-20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-4xl mx-auto text-center\">\n            <div className=\"w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6\">\n              <span className=\"text-3xl\">📱</span>\n            </div>\n            <h1 className=\"text-4xl md:text-6xl font-bold mb-6\">\n              {t('services.socialMedia')}\n            </h1>\n            <p className=\"text-xl md:text-2xl mb-8 opacity-90\">\n              Build your brand presence and engage with your audience across all social platforms\n            </p>\n            <Link\n              href=\"/contact\"\n              className=\"inline-block bg-gradient-to-r from-blue-500 to-purple-500 text-white font-bold px-8 py-4 rounded-full hover:from-blue-400 hover:to-purple-400 transition-all duration-300 transform hover:scale-105\"\n            >\n              Boost Your Social Presence\n            </Link>\n          </div>\n        </div>\n      </section>\n\n      {/* Services Overview */}\n      <section className=\"py-20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-6xl mx-auto\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-6\">\n                Complete Social Media Management\n              </h2>\n              <p className=\"text-xl text-gray-600\">\n                From strategy to execution, we handle every aspect of your social media presence\n              </p>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n              {[\n                {\n                  icon: '📝',\n                  title: 'Content Creation',\n                  description: 'Engaging posts, stories, and visual content that resonates with your audience.',\n                  features: ['Custom graphics', 'Video content', 'Copywriting', 'Brand consistency']\n                },\n                {\n                  icon: '📊',\n                  title: 'Strategy Development',\n                  description: 'Data-driven social media strategies tailored to your business goals.',\n                  features: ['Audience analysis', 'Competitor research', 'Content planning', 'Growth strategy']\n                },\n                {\n                  icon: '👥',\n                  title: 'Community Management',\n                  description: 'Build and nurture your online community with active engagement.',\n                  features: ['Response management', 'Community building', 'Customer service', 'Reputation management']\n                },\n                {\n                  icon: '🎯',\n                  title: 'Paid Advertising',\n                  description: 'Targeted social media ad campaigns that drive results and ROI.',\n                  features: ['Campaign setup', 'Audience targeting', 'Ad optimization', 'Performance tracking']\n                },\n                {\n                  icon: '📈',\n                  title: 'Analytics & Reporting',\n                  description: 'Detailed insights and reports on your social media performance.',\n                  features: ['Performance metrics', 'Growth tracking', 'ROI analysis', 'Monthly reports']\n                },\n                {\n                  icon: '🤝',\n                  title: 'Influencer Partnerships',\n                  description: 'Connect with relevant influencers to expand your reach.',\n                  features: ['Influencer outreach', 'Partnership management', 'Campaign coordination', 'Performance tracking']\n                }\n              ].map((service, index) => (\n                <div key={index} className=\"bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300\">\n                  <div className=\"text-4xl mb-4\">{service.icon}</div>\n                  <h3 className=\"text-xl font-bold text-gray-900 mb-3\">{service.title}</h3>\n                  <p className=\"text-gray-600 mb-4\">{service.description}</p>\n                  <ul className=\"space-y-2\">\n                    {service.features.map((feature, featureIndex) => (\n                      <li key={featureIndex} className=\"flex items-center text-gray-600 text-sm\">\n                        <span className=\"text-blue-600 mr-2\">✓</span>\n                        {feature}\n                      </li>\n                    ))}\n                  </ul>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Platforms We Cover */}\n      <section className=\"py-20 bg-white\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-4xl mx-auto\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-6\">\n                Platforms We Specialize In\n              </h2>\n              <p className=\"text-xl text-gray-600\">\n                Expert management across all major social media platforms\n              </p>\n            </div>\n            \n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-8\">\n              {[\n                { platform: 'Instagram', icon: '📷', description: 'Visual storytelling and engagement' },\n                { platform: 'Facebook', icon: '👥', description: 'Community building and advertising' },\n                { platform: 'LinkedIn', icon: '💼', description: 'Professional networking and B2B' },\n                { platform: 'TikTok', icon: '🎵', description: 'Viral content and Gen Z reach' },\n                { platform: 'Twitter', icon: '🐦', description: 'Real-time engagement and news' },\n                { platform: 'YouTube', icon: '📺', description: 'Video content and education' },\n                { platform: 'Pinterest', icon: '📌', description: 'Visual discovery and inspiration' },\n                { platform: 'Snapchat', icon: '👻', description: 'Ephemeral content and AR' }\n              ].map((platform, index) => (\n                <div key={index} className=\"text-center p-6 bg-gray-50 rounded-lg hover:bg-blue-50 transition-colors duration-300\">\n                  <div className=\"text-4xl mb-3\">{platform.icon}</div>\n                  <h3 className=\"text-lg font-bold text-gray-900 mb-2\">{platform.platform}</h3>\n                  <p className=\"text-gray-600 text-sm\">{platform.description}</p>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Social Media Process */}\n      <section className=\"py-20 bg-gray-50\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-4xl mx-auto\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-6\">\n                Our Social Media Management Process\n              </h2>\n              <p className=\"text-xl text-gray-600\">\n                Strategic approach to building your social media presence\n              </p>\n            </div>\n            \n            <div className=\"space-y-8\">\n              {[\n                { \n                  step: '01', \n                  title: 'Audit & Strategy', \n                  description: 'Analyze current presence and develop comprehensive social media strategy.' \n                },\n                { \n                  step: '02', \n                  title: 'Content Planning', \n                  description: 'Create content calendar with engaging posts tailored to each platform.' \n                },\n                { \n                  step: '03', \n                  title: 'Content Creation', \n                  description: 'Produce high-quality visuals, videos, and copy that resonates with your audience.' \n                },\n                { \n                  step: '04', \n                  title: 'Publishing & Engagement', \n                  description: 'Schedule posts and actively engage with your community in real-time.' \n                },\n                { \n                  step: '05', \n                  title: 'Analytics & Optimization', \n                  description: 'Monitor performance and continuously optimize strategy for better results.' \n                }\n              ].map((item, index) => (\n                <div key={index} className=\"flex items-start space-x-6\">\n                  <div className=\"w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-500 text-white rounded-full flex items-center justify-center text-xl font-bold flex-shrink-0\">\n                    {item.step}\n                  </div>\n                  <div>\n                    <h3 className=\"text-2xl font-bold text-gray-900 mb-3\">{item.title}</h3>\n                    <p className=\"text-gray-600 text-lg\">{item.description}</p>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Success Metrics */}\n      <section className=\"py-20 bg-white\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-4xl mx-auto text-center\">\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-6\">\n              Social Media Success Metrics\n            </h2>\n            <p className=\"text-xl text-gray-600 mb-12\">\n              Results that matter to your business growth\n            </p>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n              <div className=\"text-center\">\n                <div className=\"text-4xl md:text-5xl font-bold text-blue-600 mb-2\">300%</div>\n                <div className=\"text-gray-600\">Average Engagement Increase</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-4xl md:text-5xl font-bold text-blue-600 mb-2\">500K+</div>\n                <div className=\"text-gray-600\">Followers Generated</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-4xl md:text-5xl font-bold text-blue-600 mb-2\">250%</div>\n                <div className=\"text-gray-600\">Brand Awareness Boost</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-4xl md:text-5xl font-bold text-blue-600 mb-2\">95%</div>\n                <div className=\"text-gray-600\">Client Satisfaction Rate</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"bg-gradient-to-r from-blue-600 to-purple-500 text-white py-20\">\n        <div className=\"container mx-auto px-4 text-center\">\n          <h2 className=\"text-3xl md:text-4xl font-bold mb-6\">\n            Ready to Dominate Social Media?\n          </h2>\n          <p className=\"text-xl mb-8 opacity-90 max-w-2xl mx-auto\">\n            Let's build a social media presence that engages your audience and grows your business.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Link\n              href=\"/contact\"\n              className=\"bg-white text-blue-600 font-bold px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 transform hover:scale-105\"\n            >\n              Start Social Media Strategy\n            </Link>\n            <Link\n              href=\"/portfolio/social-media\"\n              className=\"border-2 border-white text-white font-bold px-8 py-4 rounded-full hover:bg-white hover:text-blue-600 transition-all duration-300\"\n            >\n              View Social Media Work\n            </Link>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;IAE/B,qBACE,6LAAC;QAAI,WAAW,CAAC,wBAAwB,EAAE,QAAQ,QAAQ,IAAI;;0BAE7D,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAAW;;;;;;;;;;;0CAE7B,6LAAC;gCAAG,WAAU;0CACX,EAAE;;;;;;0CAEL,6LAAC;gCAAE,WAAU;0CAAsC;;;;;;0CAGnD,6LAAC,+JAAA,CAAA,UAAI;gCA<PERSON>,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;0BAQP,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAGlE,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAKvC,6LAAC;gCAAI,WAAU;0CACZ;oCACC;wCACE,MAAM;wCACN,OAAO;wCACP,aAAa;wCACb,UAAU;4CAAC;4CAAmB;4CAAiB;4CAAe;yCAAoB;oCACpF;oCACA;wCACE,MAAM;wCACN,OAAO;wCACP,aAAa;wCACb,UAAU;4CAAC;4CAAqB;4CAAuB;4CAAoB;yCAAkB;oCAC/F;oCACA;wCACE,MAAM;wCACN,OAAO;wCACP,aAAa;wCACb,UAAU;4CAAC;4CAAuB;4CAAsB;4CAAoB;yCAAwB;oCACtG;oCACA;wCACE,MAAM;wCACN,OAAO;wCACP,aAAa;wCACb,UAAU;4CAAC;4CAAkB;4CAAsB;4CAAmB;yCAAuB;oCAC/F;oCACA;wCACE,MAAM;wCACN,OAAO;wCACP,aAAa;wCACb,UAAU;4CAAC;4CAAuB;4CAAmB;4CAAgB;yCAAkB;oCACzF;oCACA;wCACE,MAAM;wCACN,OAAO;wCACP,aAAa;wCACb,UAAU;4CAAC;4CAAuB;4CAA0B;4CAAyB;yCAAuB;oCAC9G;iCACD,CAAC,GAAG,CAAC,CAAC,SAAS,sBACd,6LAAC;wCAAgB,WAAU;;0DACzB,6LAAC;gDAAI,WAAU;0DAAiB,QAAQ,IAAI;;;;;;0DAC5C,6LAAC;gDAAG,WAAU;0DAAwC,QAAQ,KAAK;;;;;;0DACnE,6LAAC;gDAAE,WAAU;0DAAsB,QAAQ,WAAW;;;;;;0DACtD,6LAAC;gDAAG,WAAU;0DACX,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BAC9B,6LAAC;wDAAsB,WAAU;;0EAC/B,6LAAC;gEAAK,WAAU;0EAAqB;;;;;;4DACpC;;uDAFM;;;;;;;;;;;uCANL;;;;;;;;;;;;;;;;;;;;;;;;;;0BAoBpB,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAGlE,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAKvC,6LAAC;gCAAI,WAAU;0CACZ;oCACC;wCAAE,UAAU;wCAAa,MAAM;wCAAM,aAAa;oCAAqC;oCACvF;wCAAE,UAAU;wCAAY,MAAM;wCAAM,aAAa;oCAAqC;oCACtF;wCAAE,UAAU;wCAAY,MAAM;wCAAM,aAAa;oCAAkC;oCACnF;wCAAE,UAAU;wCAAU,MAAM;wCAAM,aAAa;oCAAgC;oCAC/E;wCAAE,UAAU;wCAAW,MAAM;wCAAM,aAAa;oCAAgC;oCAChF;wCAAE,UAAU;wCAAW,MAAM;wCAAM,aAAa;oCAA8B;oCAC9E;wCAAE,UAAU;wCAAa,MAAM;wCAAM,aAAa;oCAAmC;oCACrF;wCAAE,UAAU;wCAAY,MAAM;wCAAM,aAAa;oCAA2B;iCAC7E,CAAC,GAAG,CAAC,CAAC,UAAU,sBACf,6LAAC;wCAAgB,WAAU;;0DACzB,6LAAC;gDAAI,WAAU;0DAAiB,SAAS,IAAI;;;;;;0DAC7C,6LAAC;gDAAG,WAAU;0DAAwC,SAAS,QAAQ;;;;;;0DACvE,6LAAC;gDAAE,WAAU;0DAAyB,SAAS,WAAW;;;;;;;uCAHlD;;;;;;;;;;;;;;;;;;;;;;;;;;0BAYpB,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAGlE,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAKvC,6LAAC;gCAAI,WAAU;0CACZ;oCACC;wCACE,MAAM;wCACN,OAAO;wCACP,aAAa;oCACf;oCACA;wCACE,MAAM;wCACN,OAAO;wCACP,aAAa;oCACf;oCACA;wCACE,MAAM;wCACN,OAAO;wCACP,aAAa;oCACf;oCACA;wCACE,MAAM;wCACN,OAAO;wCACP,aAAa;oCACf;oCACA;wCACE,MAAM;wCACN,OAAO;wCACP,aAAa;oCACf;iCACD,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,6LAAC;wCAAgB,WAAU;;0DACzB,6LAAC;gDAAI,WAAU;0DACZ,KAAK,IAAI;;;;;;0DAEZ,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAyC,KAAK,KAAK;;;;;;kEACjE,6LAAC;wDAAE,WAAU;kEAAyB,KAAK,WAAW;;;;;;;;;;;;;uCANhD;;;;;;;;;;;;;;;;;;;;;;;;;;0BAgBpB,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,6LAAC;gCAAE,WAAU;0CAA8B;;;;;;0CAI3C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAoD;;;;;;0DACnE,6LAAC;gDAAI,WAAU;0DAAgB;;;;;;;;;;;;kDAEjC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAoD;;;;;;0DACnE,6LAAC;gDAAI,WAAU;0DAAgB;;;;;;;;;;;;kDAEjC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAoD;;;;;;0DACnE,6LAAC;gDAAI,WAAU;0DAAgB;;;;;;;;;;;;kDAEjC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAoD;;;;;;0DACnE,6LAAC;gDAAI,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQzC,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAsC;;;;;;sCAGpD,6LAAC;4BAAE,WAAU;sCAA4C;;;;;;sCAGzD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GA1PwB;;QACD,sIAAA,CAAA,cAAW;;;KADV", "debugId": null}}]}