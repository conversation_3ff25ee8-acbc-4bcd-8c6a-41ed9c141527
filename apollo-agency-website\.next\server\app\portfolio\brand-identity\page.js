(()=>{var e={};e.id=956,e.ids=[956],e.modules={1134:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var i=r(60687),s=r(34393),a=r(85814),n=r.n(a);function o(){let{t:e,isRTL:t}=(0,s.o)();return(0,i.jsxs)("div",{className:`min-h-screen bg-gray-50 ${t?"rtl":""}`,children:[(0,i.jsx)("section",{className:"bg-gradient-to-br from-indigo-900 via-purple-800 to-pink-600 text-white py-20",children:(0,i.jsx)("div",{className:"container mx-auto px-4",children:(0,i.jsxs)("div",{className:"max-w-4xl mx-auto text-center",children:[(0,i.jsx)("div",{className:"w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,i.jsx)("span",{className:"text-3xl",children:"\uD83C\uDFAF"})}),(0,i.jsxs)("h1",{className:"text-4xl md:text-6xl font-bold mb-6",children:[e("portfolio.categories.brandIdentity")," Portfolio"]}),(0,i.jsx)("p",{className:"text-xl md:text-2xl mb-8 opacity-90",children:"Creating memorable brand identities that connect with your audience"}),(0,i.jsx)(n(),{href:"/contact",className:"inline-block bg-gradient-to-r from-purple-500 to-pink-500 text-white font-bold px-8 py-4 rounded-full hover:from-purple-400 hover:to-pink-400 transition-all duration-300 transform hover:scale-105",children:"Build Your Brand Identity"})]})})}),(0,i.jsx)("section",{className:"py-20",children:(0,i.jsx)("div",{className:"container mx-auto px-4",children:(0,i.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,i.jsxs)("div",{className:"text-center mb-16",children:[(0,i.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-6",children:"Featured Brand Identity Projects"}),(0,i.jsx)("p",{className:"text-xl text-gray-600",children:"Brands we've helped establish and grow"})]}),(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[{id:1,title:"TechCorp Complete Rebrand",description:"Full brand identity redesign for technology company",deliverables:["Logo Design","Brand Guidelines","Color Palette","Typography","Business Cards","Letterhead"],results:["300% increase in brand recognition","Improved market positioning","Consistent brand experience"],image:"\uD83C\uDFAF",category:"Complete Rebrand"},{id:2,title:"StartupHub Brand Launch",description:"Brand identity creation for new startup accelerator",deliverables:["Logo Design","Brand Strategy","Visual Identity","Marketing Materials","Website Design"],results:["Strong market entry","Professional credibility","Investor confidence"],image:"\uD83D\uDE80",category:"Brand Launch"},{id:3,title:"HealthPlus Medical Brand",description:"Healthcare brand identity with trust and professionalism focus",deliverables:["Medical Logo","Patient Materials","Signage Design","Digital Assets"],results:["Increased patient trust","Professional appearance","Clear communication"],image:"\uD83C\uDFE5",category:"Healthcare Branding"},{id:4,title:"FoodieApp Restaurant Brand",description:"Modern restaurant brand identity with appetite appeal",deliverables:["Restaurant Logo","Menu Design","Packaging","Interior Graphics"],results:["200% increase in customers","Premium brand perception","Social media buzz"],image:"\uD83C\uDF55",category:"Restaurant Branding"}].map(e=>(0,i.jsxs)("div",{className:"bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300",children:[(0,i.jsxs)("div",{className:"bg-gradient-to-r from-indigo-100 to-purple-100 p-8 text-center",children:[(0,i.jsx)("div",{className:"text-6xl mb-4",children:e.image}),(0,i.jsx)("span",{className:"bg-white/80 text-indigo-600 px-3 py-1 rounded-full text-sm font-medium",children:e.category})]}),(0,i.jsxs)("div",{className:"p-8",children:[(0,i.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-4",children:e.title}),(0,i.jsx)("p",{className:"text-gray-600 mb-4",children:e.description}),(0,i.jsxs)("div",{className:"mb-6",children:[(0,i.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"Deliverables:"}),(0,i.jsx)("div",{className:"flex flex-wrap gap-2",children:e.deliverables.map((e,t)=>(0,i.jsx)("span",{className:"bg-indigo-100 text-indigo-600 px-3 py-1 rounded-full text-sm",children:e},t))})]}),(0,i.jsxs)("div",{className:"mb-6",children:[(0,i.jsx)("h4",{className:"font-semibold text-gray-900 mb-3",children:"Results Achieved:"}),(0,i.jsx)("ul",{className:"space-y-2",children:e.results.map((e,t)=>(0,i.jsxs)("li",{className:"flex items-center text-green-600",children:[(0,i.jsx)("span",{className:"mr-2",children:"✓"}),e]},t))})]}),(0,i.jsxs)(n(),{href:`/case-studies/${e.id}`,className:"inline-flex items-center bg-gradient-to-r from-indigo-600 to-purple-500 text-white font-bold px-6 py-3 rounded-lg hover:from-indigo-700 hover:to-purple-600 transition-all duration-300",children:["View Full Case Study",(0,i.jsx)("svg",{className:`w-4 h-4 ${t?"mr-2 rotate-180":"ml-2"}`,fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,i.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})]})]})]},e.id))})]})})}),(0,i.jsx)("section",{className:"py-20 bg-white",children:(0,i.jsx)("div",{className:"container mx-auto px-4",children:(0,i.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,i.jsxs)("div",{className:"text-center mb-16",children:[(0,i.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-6",children:"Our Brand Identity Services"}),(0,i.jsx)("p",{className:"text-xl text-gray-600",children:"Complete brand identity solutions from concept to implementation"})]}),(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:[{icon:"\uD83C\uDFA8",title:"Logo Design",description:"Unique logos that represent your brand essence"},{icon:"\uD83D\uDCCB",title:"Brand Guidelines",description:"Comprehensive brand usage guidelines"},{icon:"\uD83C\uDFA8",title:"Visual Identity",description:"Color palettes, typography, and visual elements"},{icon:"\uD83D\uDCC4",title:"Stationery Design",description:"Business cards, letterheads, and documents"},{icon:"\uD83D\uDCE6",title:"Packaging Design",description:"Product packaging that reflects your brand"},{icon:"\uD83C\uDF10",title:"Digital Assets",description:"Web graphics and social media templates"}].map((e,t)=>(0,i.jsxs)("div",{className:"text-center p-6 rounded-lg hover:bg-gray-50 transition-colors duration-300",children:[(0,i.jsx)("div",{className:"text-4xl mb-4",children:e.icon}),(0,i.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-3",children:e.title}),(0,i.jsx)("p",{className:"text-gray-600",children:e.description})]},t))})]})})}),(0,i.jsx)("section",{className:"py-20 bg-gray-50",children:(0,i.jsx)("div",{className:"container mx-auto px-4",children:(0,i.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,i.jsxs)("div",{className:"text-center mb-16",children:[(0,i.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-6",children:"Our Brand Development Process"}),(0,i.jsx)("p",{className:"text-xl text-gray-600",children:"Strategic approach to building powerful brand identities"})]}),(0,i.jsx)("div",{className:"space-y-8",children:[{step:"01",title:"Brand Discovery",description:"Understanding your business, values, and target audience"},{step:"02",title:"Strategy Development",description:"Creating brand positioning and messaging strategy"},{step:"03",title:"Visual Identity",description:"Designing logo, colors, typography, and visual elements"},{step:"04",title:"Brand Guidelines",description:"Creating comprehensive brand usage guidelines"},{step:"05",title:"Implementation",description:"Applying brand across all touchpoints and materials"}].map((e,t)=>(0,i.jsxs)("div",{className:"flex items-start space-x-6",children:[(0,i.jsx)("div",{className:"w-16 h-16 bg-gradient-to-r from-indigo-600 to-purple-500 text-white rounded-full flex items-center justify-center text-xl font-bold flex-shrink-0",children:e.step}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-3",children:e.title}),(0,i.jsx)("p",{className:"text-gray-600 text-lg",children:e.description})]})]},t))})]})})}),(0,i.jsx)("section",{className:"bg-gradient-to-r from-indigo-600 to-purple-500 text-white py-20",children:(0,i.jsxs)("div",{className:"container mx-auto px-4 text-center",children:[(0,i.jsx)("h2",{className:"text-3xl md:text-4xl font-bold mb-6",children:"Ready to Build a Powerful Brand Identity?"}),(0,i.jsx)("p",{className:"text-xl mb-8 opacity-90 max-w-2xl mx-auto",children:"Let's create a brand identity that sets you apart from the competition and connects with your audience."}),(0,i.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,i.jsx)(n(),{href:"/contact",className:"bg-white text-indigo-600 font-bold px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 transform hover:scale-105",children:"Start Your Brand Journey"}),(0,i.jsx)(n(),{href:"/portfolio",className:"border-2 border-white text-white font-bold px-8 py-4 rounded-full hover:bg-white hover:text-indigo-600 transition-all duration-300",children:"View All Projects"})]})]})})]})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5140:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});let i=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\agency\\\\apollo-agency-website\\\\src\\\\app\\\\portfolio\\\\brand-identity\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\agency\\apollo-agency-website\\src\\app\\portfolio\\brand-identity\\page.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},52388:(e,t,r)=>{Promise.resolve().then(r.bind(r,5140))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var i=r(31658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,i.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},79551:e=>{"use strict";e.exports=require("url")},82197:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>x,tree:()=>d});var i=r(65239),s=r(48088),a=r(88170),n=r.n(a),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["portfolio",{children:["brand-identity",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,5140)),"C:\\Users\\<USER>\\Desktop\\agency\\apollo-agency-website\\src\\app\\portfolio\\brand-identity\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Desktop\\agency\\apollo-agency-website\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\agency\\apollo-agency-website\\src\\app\\portfolio\\brand-identity\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},x=new i.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/portfolio/brand-identity/page",pathname:"/portfolio/brand-identity",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},82244:(e,t,r)=>{Promise.resolve().then(r.bind(r,1134))}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[447,584,658,845],()=>r(82197));module.exports=i})();