'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';

type Language = 'en' | 'ar';

interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: string) => string;
  isRTL: boolean;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

// Translations object
const translations = {
  en: {
    // Header
    'header.home': 'Home',
    'header.about': 'About Us',
    'header.services': 'Our Services',
    'header.portfolio': 'Portfolio',
    'header.clients': 'Our Clients',
    'header.blog': 'Blog',
    'header.caseStudies': 'Case Studies',
    'header.contact': 'Contact Us',
    'header.consultation': 'Get Consultation',
    'header.language.arabic': 'العربية',
    'header.language.english': 'English',
    
    // Services
    'services.digitalMarketing': 'Digital Marketing',
    'services.seo': 'SEO Services',
    'services.graphicDesign': 'Graphic Design',
    'services.socialMedia': 'Social Media Management',
    'services.branding': 'Brand Development',
    'services.influencerMarketing': 'Influencer Marketing',
    'services.contentCreation': 'Content Creation',
    'services.marketingStrategy': 'Marketing Strategy',
    'services.webDevelopment': 'Website Development',
    'services.videoProduction': 'Video Production',
    
    // Hero Section
    'hero.title1': 'We Create Together Your Marketing Success Formula',
    'hero.subtitle1': 'Our services combine expertise and strategic vision to give you outstanding marketing success that achieves your goals and distinguishes you from competitors.',
    'hero.title2': 'Digital Marketing Excellence',
    'hero.subtitle2': 'Transform your business with our comprehensive digital marketing strategies designed to maximize your online presence and drive results.',
    'hero.title3': 'Brand Development & Strategy',
    'hero.subtitle3': 'Build a powerful brand identity that resonates with your audience and creates lasting connections in the digital landscape.',
    'hero.cta1': 'Contact Us',
    'hero.cta2': 'Get Started',
    'hero.cta3': 'Learn More',
    'hero.stats.projects': 'Successful Projects',
    'hero.stats.clients': 'Happy Clients',
    'hero.stats.experience': 'Years Experience',
    
    // Services Section
    'services.title': 'Everything You Need to Launch Your Business in One Place!',
    'services.subtitle': 'With Serv Infinity for Marketing Solutions',
    'services.brandDev.title': 'Brand Development',
    'services.brandDev.desc': 'We develop unique brands that enhance your digital presence',
    'services.content.title': 'Content Creation',
    'services.content.desc': 'We write content that aligns with the products and services offered',
    'services.influencer.title': 'Influencer Marketing',
    'services.influencer.desc': 'We ensure successful marketing through influential people',
    'services.digital.title': 'Digital Marketing',
    'services.digital.desc': 'We launch carefully studied and focused advertising campaigns to achieve your goals accurately',
    'services.web.title': 'Website Development',
    'services.web.desc': 'We design and develop websites using the latest programming technologies',
    'services.graphic.title': 'Graphic Design',
    'services.graphic.desc': 'We understand and analyze your needs and provide you with the best designs that serve your business',
    'services.video.title': 'Video Production',
    'services.video.desc': 'We shoot and create professional advertising videos',
    'services.seoServices.title': 'SEO Services',
    'services.seoServices.desc': 'We improve the appearance of websites on search engines',
    'services.learnMore': 'Learn More',
    'services.viewAll': 'View All Services',
    
    // Portfolio Section
    'portfolio.title': 'Some of the Work We Have Executed',
    'portfolio.subtitle': 'Explore our portfolio of successful projects and see how we\'ve helped businesses achieve their goals',
    'portfolio.viewProject': 'View Project',
    'portfolio.viewFull': 'View Full Portfolio',
    'portfolio.categories.brandIdentity': 'Brand Identity',
    'portfolio.categories.socialMedia': 'Social Media',
    'portfolio.categories.graphicDesign': 'Graphic Design',
    'portfolio.categories.landingPages': 'Landing Pages',
    'portfolio.categories.marketingCampaigns': 'Marketing Campaigns',
    
    // Clients Section
    'clients.title': 'Our Clients',
    'clients.subtitle': 'Trusted by leading companies worldwide',
    'clients.testimonials.title': 'What Our Clients Say',
    'clients.testimonials.subtitle': 'Don\'t just take our word for it - hear from our satisfied clients',
    'clients.stats.projects': 'Projects Completed',
    'clients.stats.clients': 'Happy Clients',
    'clients.stats.experience': 'Years Experience',
    'clients.stats.satisfaction': 'Client Satisfaction',
    
    // Blog Section
    'blog.title': 'Our Expertise at Your Fingertips',
    'blog.subtitle': 'Stay updated with the latest insights, tips, and trends in digital marketing',
    'blog.readMore': 'Read More',
    'blog.readMoreArticles': 'Read More Articles',
    'blog.share': 'Share:',
    'blog.categories.all': 'All',
    'blog.categories.seo': 'SEO',
    'blog.categories.marketing': 'Marketing',
    'blog.categories.design': 'Design',
    'blog.categories.branding': 'Branding',
    'blog.categories.strategy': 'Strategy',
    
    // Contact Section
    'contact.title': 'It\'s Time to Develop Your Investment',
    'contact.subtitle': 'Contact us now and start your journey to success',
    'contact.freeConsultation': 'Get Free Consultation',
    'contact.callNow': 'Call Now: +90 552 833 0233',
    'contact.whatsapp': 'WhatsApp',
    'contact.whatsapp.desc': 'Get instant support',
    'contact.whatsapp.cta': 'Chat Now',
    'contact.email': 'Email',
    'contact.email.desc': 'Send us a message',
    'contact.email.cta': 'Send Email',
    'contact.schedule': 'Schedule Meeting',
    'contact.schedule.desc': 'Book a consultation',
    'contact.schedule.cta': 'Book Now',
    'contact.support': 'Support Available',
    
    // Footer
    'footer.description': 'We create innovative marketing solutions for your business, to reach a wider segment of customers through effective and carefully studied marketing strategies.',
    'footer.quickLinks': 'Quick Links',
    'footer.services': 'Our Services',
    'footer.contact': 'Contact Information',
    'footer.turkey': 'Turkey Office',
    'footer.saudi': 'Saudi Arabia Office',
    'footer.newsletter.title': 'Ready to Develop Your Investment?',
    'footer.newsletter.subtitle': 'Contact us now and start your journey to success',
    'footer.newsletter.placeholder': 'Enter your email address',
    'footer.newsletter.subscribe': 'Subscribe',
    'footer.copyright': '© 2024 Serv Infinity. All rights reserved.',
    'footer.privacy': 'Privacy Policy',
    'footer.terms': 'Terms of Service',
    'footer.cookies': 'Cookie Policy',
    
    // Common
    'common.loading': 'Loading...',
    'common.error': 'Error',
    'common.success': 'Success',
  },
  ar: {
    // Header
    'header.home': 'الرئيسية',
    'header.about': 'من نحن',
    'header.services': 'خدماتنا',
    'header.portfolio': 'أعمالنا',
    'header.clients': 'عملاؤنا',
    'header.blog': 'المدونة',
    'header.caseStudies': 'دراسات الحالة',
    'header.contact': 'اتصل بنا',
    'header.consultation': 'احصل على استشارة',
    'header.language.arabic': 'العربية',
    'header.language.english': 'English',
    
    // Services
    'services.digitalMarketing': 'التسويق الرقمي',
    'services.seo': 'خدمات تحسين محركات البحث',
    'services.graphicDesign': 'التصميم الجرافيكي',
    'services.socialMedia': 'إدارة وسائل التواصل الاجتماعي',
    'services.branding': 'تطوير العلامة التجارية',
    'services.influencerMarketing': 'التسويق عبر المؤثرين',
    'services.contentCreation': 'إنشاء المحتوى',
    'services.marketingStrategy': 'استراتيجية التسويق',
    'services.webDevelopment': 'تطوير المواقع الإلكترونية',
    'services.videoProduction': 'إنتاج الفيديو',
    
    // Hero Section
    'hero.title1': 'نصنع معاً معادلة نجاحك التسويقي',
    'hero.subtitle1': 'تجمع خدماتنا بين الخبرة والرؤية الاستراتيجية لتمنحك نجاحاً تسويقياً متميزاً يحقق أهدافك ويميزك عن المنافسين.',
    'hero.title2': 'التميز في التسويق الرقمي',
    'hero.subtitle2': 'حوّل عملك باستراتيجيات التسويق الرقمي الشاملة المصممة لتعظيم حضورك الرقمي وتحقيق النتائج.',
    'hero.title3': 'تطوير العلامة التجارية والاستراتيجية',
    'hero.subtitle3': 'ابنِ هوية علامة تجارية قوية تتردد صداها مع جمهورك وتخلق روابط دائمة في المشهد الرقمي.',
    'hero.cta1': 'اتصل بنا',
    'hero.cta2': 'ابدأ الآن',
    'hero.cta3': 'اعرف المزيد',
    'hero.stats.projects': 'مشروع ناجح',
    'hero.stats.clients': 'عميل سعيد',
    'hero.stats.experience': 'سنوات خبرة',
    
    // Services Section
    'services.title': 'كل ما تحتاجه لإطلاق عملك في مكان واحد!',
    'services.subtitle': 'مع سيرف إنفينيتي للحلول التسويقية',
    'services.brandDev.title': 'تطوير العلامة التجارية',
    'services.brandDev.desc': 'نطور علامات تجارية فريدة تعزز حضورك الرقمي',
    'services.content.title': 'إنشاء المحتوى',
    'services.content.desc': 'نكتب محتوى يتماشى مع المنتجات والخدمات المقدمة',
    'services.influencer.title': 'التسويق عبر المؤثرين',
    'services.influencer.desc': 'نضمن التسويق الناجح من خلال الأشخاص المؤثرين',
    'services.digital.title': 'التسويق الرقمي',
    'services.digital.desc': 'نطلق حملات إعلانية مدروسة ومركزة لتحقيق أهدافك بدقة',
    'services.web.title': 'تطوير المواقع الإلكترونية',
    'services.web.desc': 'نصمم ونطور المواقع الإلكترونية باستخدام أحدث تقنيات البرمجة',
    'services.graphic.title': 'التصميم الجرافيكي',
    'services.graphic.desc': 'نفهم ونحلل احتياجاتك ونقدم لك أفضل التصاميم التي تخدم عملك',
    'services.video.title': 'إنتاج الفيديو',
    'services.video.desc': 'نصور وننشئ فيديوهات إعلانية احترافية',
    'services.seoServices.title': 'خدمات تحسين محركات البحث',
    'services.seoServices.desc': 'نحسن ظهور المواقع الإلكترونية في محركات البحث',
    'services.learnMore': 'اعرف المزيد',
    'services.viewAll': 'عرض جميع الخدمات',
    
    // Portfolio Section
    'portfolio.title': 'بعض الأعمال التي نفذناها',
    'portfolio.subtitle': 'استكشف محفظة أعمالنا من المشاريع الناجحة وشاهد كيف ساعدنا الشركات في تحقيق أهدافها',
    'portfolio.viewProject': 'عرض المشروع',
    'portfolio.viewFull': 'عرض المحفظة كاملة',
    'portfolio.categories.brandIdentity': 'هوية العلامة التجارية',
    'portfolio.categories.socialMedia': 'وسائل التواصل الاجتماعي',
    'portfolio.categories.graphicDesign': 'التصميم الجرافيكي',
    'portfolio.categories.landingPages': 'صفحات الهبوط',
    'portfolio.categories.marketingCampaigns': 'الحملات التسويقية',
    
    // Clients Section
    'clients.title': 'عملاؤنا',
    'clients.subtitle': 'موثوق به من قبل الشركات الرائدة في جميع أنحاء العالم',
    'clients.testimonials.title': 'ماذا يقول عملاؤنا',
    'clients.testimonials.subtitle': 'لا تأخذ كلامنا فقط - استمع من عملائنا الراضين',
    'clients.stats.projects': 'مشروع مكتمل',
    'clients.stats.clients': 'عميل سعيد',
    'clients.stats.experience': 'سنوات خبرة',
    'clients.stats.satisfaction': 'رضا العملاء',
    
    // Blog Section
    'blog.title': 'خبرتنا في متناول يدك',
    'blog.subtitle': 'ابق على اطلاع بأحدث الرؤى والنصائح والاتجاهات في التسويق الرقمي',
    'blog.readMore': 'اقرأ المزيد',
    'blog.readMoreArticles': 'اقرأ المزيد من المقالات',
    'blog.share': 'شارك:',
    'blog.categories.all': 'الكل',
    'blog.categories.seo': 'تحسين محركات البحث',
    'blog.categories.marketing': 'التسويق',
    'blog.categories.design': 'التصميم',
    'blog.categories.branding': 'العلامة التجارية',
    'blog.categories.strategy': 'الاستراتيجية',
    
    // Contact Section
    'contact.title': 'حان الوقت لتطوير استثمارك',
    'contact.subtitle': 'اتصل بنا الآن وابدأ رحلتك نحو النجاح',
    'contact.freeConsultation': 'احصل على استشارة مجانية',
    'contact.callNow': 'اتصل الآن: +90 552 833 0233',
    'contact.whatsapp': 'واتساب',
    'contact.whatsapp.desc': 'احصل على دعم فوري',
    'contact.whatsapp.cta': 'تحدث الآن',
    'contact.email': 'البريد الإلكتروني',
    'contact.email.desc': 'أرسل لنا رسالة',
    'contact.email.cta': 'أرسل بريد إلكتروني',
    'contact.schedule': 'جدولة اجتماع',
    'contact.schedule.desc': 'احجز استشارة',
    'contact.schedule.cta': 'احجز الآن',
    'contact.support': 'الدعم متاح',
    
    // Footer
    'footer.description': 'نصنع حلول تسويقية مبتكرة لعملك، للوصول إلى شريحة أوسع من العملاء من خلال استراتيجيات تسويقية فعالة ومدروسة بعناية.',
    'footer.quickLinks': 'روابط سريعة',
    'footer.services': 'خدماتنا',
    'footer.contact': 'معلومات الاتصال',
    'footer.turkey': 'مكتب تركيا',
    'footer.saudi': 'مكتب المملكة العربية السعودية',
    'footer.newsletter.title': 'هل أنت مستعد لتطوير استثمارك؟',
    'footer.newsletter.subtitle': 'اتصل بنا الآن وابدأ رحلتك نحو النجاح',
    'footer.newsletter.placeholder': 'أدخل عنوان بريدك الإلكتروني',
    'footer.newsletter.subscribe': 'اشترك',
    'footer.copyright': '© 2024 سيرف إنفينيتي. جميع الحقوق محفوظة.',
    'footer.privacy': 'سياسة الخصوصية',
    'footer.terms': 'شروط الخدمة',
    'footer.cookies': 'سياسة ملفات تعريف الارتباط',
    
    // Common
    'common.loading': 'جاري التحميل...',
    'common.error': 'خطأ',
    'common.success': 'نجح',
  }
};

export const LanguageProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [language, setLanguage] = useState<Language>('en');

  useEffect(() => {
    // Load saved language from localStorage
    const savedLanguage = localStorage.getItem('language') as Language;
    if (savedLanguage && (savedLanguage === 'en' || savedLanguage === 'ar')) {
      setLanguage(savedLanguage);
    }
  }, []);

  useEffect(() => {
    // Save language to localStorage and update document direction
    localStorage.setItem('language', language);
    document.documentElement.lang = language;
    document.documentElement.dir = language === 'ar' ? 'rtl' : 'ltr';
    
    // Update body class for RTL styling
    if (language === 'ar') {
      document.body.classList.add('rtl');
    } else {
      document.body.classList.remove('rtl');
    }
  }, [language]);

  const t = (key: string): string => {
    return translations[language][key] || key;
  };

  const isRTL = language === 'ar';

  return (
    <LanguageContext.Provider value={{ language, setLanguage, t, isRTL }}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};
