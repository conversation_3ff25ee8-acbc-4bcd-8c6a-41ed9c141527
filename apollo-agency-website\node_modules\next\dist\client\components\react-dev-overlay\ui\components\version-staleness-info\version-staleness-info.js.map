{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/ui/components/version-staleness-info/version-staleness-info.tsx"], "sourcesContent": ["import type { VersionInfo } from '../../../../../../server/dev/parse-version-info'\nimport { cx } from '../../utils/cx'\n\nexport function VersionStalenessInfo({\n  versionInfo,\n  bundlerName,\n}: {\n  versionInfo: VersionInfo\n  // Passed from parent for easier handling in Storybook.\n  bundlerName: 'Webpack' | 'Turbopack' | 'Rspack'\n}) {\n  const { staleness } = versionInfo\n  let { text, indicatorClass, title } = getStaleness(versionInfo)\n\n  const isTurbopack = bundlerName === 'Turbopack'\n  const shouldBeLink = staleness.startsWith('stale')\n  if (shouldBeLink) {\n    return (\n      <a\n        className=\"nextjs-container-build-error-version-status dialog-exclude-closing-from-outside-click\"\n        target=\"_blank\"\n        rel=\"noopener noreferrer\"\n        href=\"https://nextjs.org/docs/messages/version-staleness\"\n      >\n        <Eclipse\n          className={cx('version-staleness-indicator', indicatorClass)}\n        />\n        <span data-nextjs-version-checker title={title}>\n          {text}\n        </span>\n        <span className={cx(isTurbopack && 'turbopack-text')}>\n          {bundlerName}\n        </span>\n      </a>\n    )\n  }\n\n  return (\n    <span className=\"nextjs-container-build-error-version-status dialog-exclude-closing-from-outside-click\">\n      <Eclipse className={cx('version-staleness-indicator', indicatorClass)} />\n      <span data-nextjs-version-checker title={title}>\n        {text}\n      </span>\n      <span className={cx(isTurbopack && 'turbopack-text')}>{bundlerName}</span>\n    </span>\n  )\n}\n\nexport function getStaleness({ installed, staleness, expected }: VersionInfo) {\n  let text = ''\n  let title = ''\n  let indicatorClass = ''\n  const versionLabel = `Next.js ${installed}`\n  switch (staleness) {\n    case 'newer-than-npm':\n    case 'fresh':\n      text = versionLabel\n      title = `Latest available version is detected (${installed}).`\n      indicatorClass = 'fresh'\n      break\n    case 'stale-patch':\n    case 'stale-minor':\n      text = `${versionLabel} (stale)`\n      title = `There is a newer version (${expected}) available, upgrade recommended! `\n      indicatorClass = 'stale'\n      break\n    case 'stale-major': {\n      text = `${versionLabel} (outdated)`\n      title = `An outdated version detected (latest is ${expected}), upgrade is highly recommended!`\n      indicatorClass = 'outdated'\n      break\n    }\n    case 'stale-prerelease': {\n      text = `${versionLabel} (stale)`\n      title = `There is a newer canary version (${expected}) available, please upgrade! `\n      indicatorClass = 'stale'\n      break\n    }\n    case 'unknown':\n      text = `${versionLabel} (unknown)`\n      title = 'No Next.js version data was found.'\n      indicatorClass = 'unknown'\n      break\n    default:\n      break\n  }\n  return { text, indicatorClass, title }\n}\n\nexport const styles = `\n  .nextjs-container-build-error-version-status {\n    -webkit-font-smoothing: antialiased;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    gap: 4px;\n\n    height: var(--size-26);\n    padding: 6px 8px 6px 6px;\n    background: var(--color-background-100);\n    background-clip: padding-box;\n    border: 1px solid var(--color-gray-alpha-400);\n    box-shadow: var(--shadow-small);\n    border-radius: var(--rounded-full);\n\n    color: var(--color-gray-900);\n    font-size: var(--size-12);\n    font-weight: 500;\n    line-height: var(--size-16);\n  }\n\n  a.nextjs-container-build-error-version-status {\n    text-decoration: none;\n    color: var(--color-gray-900);\n\n    &:hover {\n      background: var(--color-gray-100);\n    }\n\n    &:focus {\n      outline: var(--focus-ring);\n    }\n  }\n\n  .version-staleness-indicator.fresh {\n    fill: var(--color-green-800);\n    stroke: var(--color-green-300);\n  }\n  .version-staleness-indicator.stale {\n    fill: var(--color-amber-800);\n    stroke: var(--color-amber-300);\n  }\n  .version-staleness-indicator.outdated {\n    fill: var(--color-red-800);\n    stroke: var(--color-red-300);\n  }\n  .version-staleness-indicator.unknown {\n    fill: var(--color-gray-800);\n    stroke: var(--color-gray-300);\n  }\n\n  .nextjs-container-build-error-version-status > .turbopack-text {\n    background: linear-gradient(\n      to right,\n      var(--color-turbopack-text-red) 0%,\n      var(--color-turbopack-text-blue) 100%\n    );\n    background-clip: text;\n    -webkit-background-clip: text;\n    -webkit-text-fill-color: transparent;\n  }\n`\n\nfunction Eclipse({ className }: { className: string }) {\n  return (\n    <svg\n      width=\"14\"\n      height=\"14\"\n      viewBox=\"0 0 14 14\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n    >\n      <circle className={className} cx=\"7\" cy=\"7\" r=\"5.5\" strokeWidth=\"3\" />\n    </svg>\n  )\n}\n"], "names": ["VersionStalenessInfo", "getStaleness", "styles", "versionInfo", "bundlerName", "staleness", "text", "indicatorClass", "title", "isTurbopack", "shouldBeLink", "startsWith", "a", "className", "target", "rel", "href", "Eclipse", "cx", "span", "data-nextjs-version-checker", "installed", "expected", "versionLabel", "svg", "width", "height", "viewBox", "fill", "xmlns", "circle", "cy", "r", "strokeWidth"], "mappings": ";;;;;;;;;;;;;;;;IAGgBA,oBAAoB;eAApBA;;IA6CAC,YAAY;eAAZA;;IAyCHC,MAAM;eAANA;;;;oBAxFM;AAEZ,SAASF,qBAAqB,KAOpC;IAPoC,IAAA,EACnCG,WAAW,EACXC,WAAW,EAKZ,GAPoC;IAQnC,MAAM,EAAEC,SAAS,EAAE,GAAGF;IACtB,IAAI,EAAEG,IAAI,EAAEC,cAAc,EAAEC,KAAK,EAAE,GAAGP,aAAaE;IAEnD,MAAMM,cAAcL,gBAAgB;IACpC,MAAMM,eAAeL,UAAUM,UAAU,CAAC;IAC1C,IAAID,cAAc;QAChB,qBACE,sBAACE;YACCC,WAAU;YACVC,QAAO;YACPC,KAAI;YACJC,MAAK;;8BAEL,qBAACC;oBACCJ,WAAWK,IAAAA,MAAE,EAAC,+BAA+BX;;8BAE/C,qBAACY;oBAAKC,6BAA2B;oBAACZ,OAAOA;8BACtCF;;8BAEH,qBAACa;oBAAKN,WAAWK,IAAAA,MAAE,EAACT,eAAe;8BAChCL;;;;IAIT;IAEA,qBACE,sBAACe;QAAKN,WAAU;;0BACd,qBAACI;gBAAQJ,WAAWK,IAAAA,MAAE,EAAC,+BAA+BX;;0BACtD,qBAACY;gBAAKC,6BAA2B;gBAACZ,OAAOA;0BACtCF;;0BAEH,qBAACa;gBAAKN,WAAWK,IAAAA,MAAE,EAACT,eAAe;0BAAoBL;;;;AAG7D;AAEO,SAASH,aAAa,KAA+C;IAA/C,IAAA,EAAEoB,SAAS,EAAEhB,SAAS,EAAEiB,QAAQ,EAAe,GAA/C;IAC3B,IAAIhB,OAAO;IACX,IAAIE,QAAQ;IACZ,IAAID,iBAAiB;IACrB,MAAMgB,eAAe,AAAC,aAAUF;IAChC,OAAQhB;QACN,KAAK;QACL,KAAK;YACHC,OAAOiB;YACPf,QAAQ,AAAC,2CAAwCa,YAAU;YAC3Dd,iBAAiB;YACjB;QACF,KAAK;QACL,KAAK;YACHD,OAAO,AAAC,KAAEiB,eAAa;YACvBf,QAAQ,AAAC,+BAA4Bc,WAAS;YAC9Cf,iBAAiB;YACjB;QACF,KAAK;YAAe;gBAClBD,OAAO,AAAC,KAAEiB,eAAa;gBACvBf,QAAQ,AAAC,6CAA0Cc,WAAS;gBAC5Df,iBAAiB;gBACjB;YACF;QACA,KAAK;YAAoB;gBACvBD,OAAO,AAAC,KAAEiB,eAAa;gBACvBf,QAAQ,AAAC,sCAAmCc,WAAS;gBACrDf,iBAAiB;gBACjB;YACF;QACA,KAAK;YACHD,OAAO,AAAC,KAAEiB,eAAa;YACvBf,QAAQ;YACRD,iBAAiB;YACjB;QACF;YACE;IACJ;IACA,OAAO;QAAED;QAAMC;QAAgBC;IAAM;AACvC;AAEO,MAAMN,SAAU;AAgEvB,SAASe,QAAQ,KAAoC;IAApC,IAAA,EAAEJ,SAAS,EAAyB,GAApC;IACf,qBACE,qBAACW;QACCC,OAAM;QACNC,QAAO;QACPC,SAAQ;QACRC,MAAK;QACLC,OAAM;kBAEN,cAAA,qBAACC;YAAOjB,WAAWA;YAAWK,IAAG;YAAIa,IAAG;YAAIC,GAAE;YAAMC,aAAY;;;AAGtE"}