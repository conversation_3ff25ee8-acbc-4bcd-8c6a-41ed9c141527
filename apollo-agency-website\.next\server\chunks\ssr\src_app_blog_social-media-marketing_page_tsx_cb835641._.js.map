{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/agency/apollo-agency-website/src/app/blog/social-media-marketing/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useLanguage } from '@/contexts/LanguageContext';\nimport Link from 'next/link';\n\nexport default function SocialMediaMarketingBlogPage() {\n  const { t, isRTL } = useLanguage();\n\n  const posts = [\n    {\n      id: 1,\n      title: '10 Social Media Marketing Trends to Watch in 2024',\n      excerpt: 'Discover the latest trends shaping social media marketing and how to leverage them for your business growth.',\n      author: '<PERSON>',\n      date: '2024-01-15',\n      readTime: '8 min read',\n      category: 'Trends',\n      image: '📱'\n    },\n    {\n      id: 2,\n      title: 'How to Create Engaging Instagram Stories That Convert',\n      excerpt: 'Learn the secrets to creating Instagram Stories that capture attention and drive meaningful engagement.',\n      author: '<PERSON>',\n      date: '2024-01-10',\n      readTime: '6 min read',\n      category: 'Instagram',\n      image: '📸'\n    },\n    {\n      id: 3,\n      title: 'The Complete Guide to TikTok Marketing for Businesses',\n      excerpt: 'Everything you need to know about marketing your business on TikTok and reaching Gen Z audiences.',\n      author: '<PERSON>',\n      date: '2024-01-05',\n      readTime: '12 min read',\n      category: 'TikTok',\n      image: '🎵'\n    },\n    {\n      id: 4,\n      title: 'LinkedIn Marketing: Building B2B Relationships That Matter',\n      excerpt: 'Master LinkedIn marketing strategies to build professional relationships and generate quality leads.',\n      author: 'David Chen',\n      date: '2023-12-28',\n      readTime: '10 min read',\n      category: 'LinkedIn',\n      image: '💼'\n    },\n    {\n      id: 5,\n      title: 'Social Media Analytics: Metrics That Actually Matter',\n      excerpt: 'Cut through the noise and focus on the social media metrics that truly impact your business goals.',\n      author: 'Lisa Thompson',\n      date: '2023-12-20',\n      readTime: '7 min read',\n      category: 'Analytics',\n      image: '📊'\n    },\n    {\n      id: 6,\n      title: 'Creating a Social Media Content Calendar That Works',\n      excerpt: 'Step-by-step guide to planning and organizing your social media content for maximum impact.',\n      author: 'Michael Brown',\n      date: '2023-12-15',\n      readTime: '9 min read',\n      category: 'Strategy',\n      image: '📅'\n    }\n  ];\n\n  return (\n    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : ''}`}>\n      {/* Hero Section */}\n      <section className=\"bg-gradient-to-br from-blue-900 via-purple-800 to-pink-600 text-white py-20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-4xl mx-auto text-center\">\n            <div className=\"w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6\">\n              <span className=\"text-3xl\">📱</span>\n            </div>\n            <h1 className=\"text-4xl md:text-6xl font-bold mb-6\">\n              Social Media Marketing Blog\n            </h1>\n            <p className=\"text-xl md:text-2xl mb-8 opacity-90\">\n              Expert insights, tips, and strategies for social media marketing success\n            </p>\n            <Link\n              href=\"/contact\"\n              className=\"inline-block bg-gradient-to-r from-blue-500 to-purple-500 text-white font-bold px-8 py-4 rounded-full hover:from-blue-400 hover:to-purple-400 transition-all duration-300 transform hover:scale-105\"\n            >\n              Get Social Media Strategy\n            </Link>\n          </div>\n        </div>\n      </section>\n\n      {/* Blog Posts Grid */}\n      <section className=\"py-20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-6xl mx-auto\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-6\">\n                Latest Social Media Marketing Articles\n              </h2>\n              <p className=\"text-xl text-gray-600\">\n                Stay updated with the latest trends and strategies\n              </p>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n              {posts.map((post) => (\n                <article key={post.id} className=\"bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300\">\n                  {/* Post Header */}\n                  <div className=\"bg-gradient-to-r from-blue-100 to-purple-100 p-8 text-center\">\n                    <div className=\"text-6xl mb-4\">{post.image}</div>\n                    <span className=\"bg-white/80 text-blue-600 px-3 py-1 rounded-full text-sm font-medium\">\n                      {post.category}\n                    </span>\n                  </div>\n\n                  {/* Post Content */}\n                  <div className=\"p-6\">\n                    <h3 className=\"text-xl font-bold text-gray-900 mb-3 line-clamp-2\">\n                      {post.title}\n                    </h3>\n                    <p className=\"text-gray-600 mb-4 line-clamp-3\">\n                      {post.excerpt}\n                    </p>\n                    \n                    <div className=\"flex items-center justify-between text-sm text-gray-500 mb-4\">\n                      <span>{post.author}</span>\n                      <span>{post.readTime}</span>\n                    </div>\n                    \n                    <div className=\"flex items-center justify-between\">\n                      <span className=\"text-sm text-gray-500\">{post.date}</span>\n                      <Link\n                        href={`/blog/social-media-marketing/${post.id}`}\n                        className=\"inline-flex items-center text-blue-600 hover:text-blue-700 font-medium\"\n                      >\n                        Read More\n                        <svg className={`w-4 h-4 ${isRTL ? 'mr-1 rotate-180' : 'ml-1'}`} fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                        </svg>\n                      </Link>\n                    </div>\n                  </div>\n                </article>\n              ))}\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Categories */}\n      <section className=\"py-20 bg-white\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-4xl mx-auto\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-6\">\n                Explore More Topics\n              </h2>\n              <p className=\"text-xl text-gray-600\">\n                Discover insights across different social media platforms\n              </p>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n              {[\n                { name: 'Instagram Marketing', icon: '📸', count: '25 articles' },\n                { name: 'Facebook Advertising', icon: '👥', count: '18 articles' },\n                { name: 'TikTok Strategy', icon: '🎵', count: '12 articles' },\n                { name: 'LinkedIn B2B', icon: '💼', count: '15 articles' },\n                { name: 'Twitter Engagement', icon: '🐦', count: '10 articles' },\n                { name: 'YouTube Marketing', icon: '📺', count: '8 articles' },\n                { name: 'Pinterest Strategy', icon: '📌', count: '6 articles' },\n                { name: 'Social Analytics', icon: '📊', count: '20 articles' }\n              ].map((category, index) => (\n                <Link\n                  key={index}\n                  href={`/blog/${category.name.toLowerCase().replace(' ', '-')}`}\n                  className=\"text-center p-6 bg-gray-50 rounded-lg hover:bg-blue-50 transition-colors duration-300\"\n                >\n                  <div className=\"text-4xl mb-3\">{category.icon}</div>\n                  <h3 className=\"text-lg font-bold text-gray-900 mb-2\">{category.name}</h3>\n                  <p className=\"text-gray-600 text-sm\">{category.count}</p>\n                </Link>\n              ))}\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Newsletter Signup */}\n      <section className=\"py-20 bg-gray-50\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-4xl mx-auto text-center\">\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-6\">\n              Stay Updated with Social Media Trends\n            </h2>\n            <p className=\"text-xl text-gray-600 mb-8\">\n              Get the latest social media marketing insights delivered to your inbox\n            </p>\n            \n            <div className=\"max-w-md mx-auto\">\n              <div className=\"flex gap-4\">\n                <input\n                  type=\"email\"\n                  placeholder=\"Enter your email\"\n                  className=\"flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n                <button className=\"bg-gradient-to-r from-blue-600 to-purple-500 text-white font-bold px-6 py-3 rounded-lg hover:from-blue-700 hover:to-purple-600 transition-all duration-300\">\n                  Subscribe\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"bg-gradient-to-r from-blue-600 to-purple-500 text-white py-20\">\n        <div className=\"container mx-auto px-4 text-center\">\n          <h2 className=\"text-3xl md:text-4xl font-bold mb-6\">\n            Need Help with Your Social Media Strategy?\n          </h2>\n          <p className=\"text-xl mb-8 opacity-90 max-w-2xl mx-auto\">\n            Let our experts help you create and execute a social media strategy that drives results.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Link\n              href=\"/contact\"\n              className=\"bg-white text-blue-600 font-bold px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 transform hover:scale-105\"\n            >\n              Get Social Media Help\n            </Link>\n            <Link\n              href=\"/services/social-media\"\n              className=\"border-2 border-white text-white font-bold px-8 py-4 rounded-full hover:bg-white hover:text-blue-600 transition-all duration-300\"\n            >\n              View Our Services\n            </Link>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,cAAW,AAAD;IAE/B,MAAM,QAAQ;QACZ;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,QAAQ;YACR,MAAM;YACN,UAAU;YACV,UAAU;YACV,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,QAAQ;YACR,MAAM;YACN,UAAU;YACV,UAAU;YACV,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,QAAQ;YACR,MAAM;YACN,UAAU;YACV,UAAU;YACV,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,QAAQ;YAC<PERSON>,MAAM;YACN,UAAU;YACV,UAAU;YACV,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,QAAQ;YACR,MAAM;YACN,UAAU;YACV,UAAU;YACV,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,QAAQ;YACR,MAAM;YACN,UAAU;YACV,UAAU;YACV,OAAO;QACT;KACD;IAED,qBACE,8OAAC;QAAI,WAAW,CAAC,wBAAwB,EAAE,QAAQ,QAAQ,IAAI;;0BAE7D,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CAAW;;;;;;;;;;;0CAE7B,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAGpD,8OAAC;gCAAE,WAAU;0CAAsC;;;;;;0CAGnD,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;0BAQP,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAGlE,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAKvC,8OAAC;gCAAI,WAAU;0CACZ,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;wCAAsB,WAAU;;0DAE/B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAiB,KAAK,KAAK;;;;;;kEAC1C,8OAAC;wDAAK,WAAU;kEACb,KAAK,QAAQ;;;;;;;;;;;;0DAKlB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEACX,KAAK,KAAK;;;;;;kEAEb,8OAAC;wDAAE,WAAU;kEACV,KAAK,OAAO;;;;;;kEAGf,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAM,KAAK,MAAM;;;;;;0EAClB,8OAAC;0EAAM,KAAK,QAAQ;;;;;;;;;;;;kEAGtB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAyB,KAAK,IAAI;;;;;;0EAClD,8OAAC,4JAAA,CAAA,UAAI;gEACH,MAAM,CAAC,6BAA6B,EAAE,KAAK,EAAE,EAAE;gEAC/C,WAAU;;oEACX;kFAEC,8OAAC;wEAAI,WAAW,CAAC,QAAQ,EAAE,QAAQ,oBAAoB,QAAQ;wEAAE,MAAK;wEAAO,QAAO;wEAAe,SAAQ;kFACzG,cAAA,8OAAC;4EAAK,eAAc;4EAAQ,gBAAe;4EAAQ,aAAa;4EAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uCA/BjE,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;0BA4C/B,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAGlE,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAKvC,8OAAC;gCAAI,WAAU;0CACZ;oCACC;wCAAE,MAAM;wCAAuB,MAAM;wCAAM,OAAO;oCAAc;oCAChE;wCAAE,MAAM;wCAAwB,MAAM;wCAAM,OAAO;oCAAc;oCACjE;wCAAE,MAAM;wCAAmB,MAAM;wCAAM,OAAO;oCAAc;oCAC5D;wCAAE,MAAM;wCAAgB,MAAM;wCAAM,OAAO;oCAAc;oCACzD;wCAAE,MAAM;wCAAsB,MAAM;wCAAM,OAAO;oCAAc;oCAC/D;wCAAE,MAAM;wCAAqB,MAAM;wCAAM,OAAO;oCAAa;oCAC7D;wCAAE,MAAM;wCAAsB,MAAM;wCAAM,OAAO;oCAAa;oCAC9D;wCAAE,MAAM;wCAAoB,MAAM;wCAAM,OAAO;oCAAc;iCAC9D,CAAC,GAAG,CAAC,CAAC,UAAU,sBACf,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,CAAC,MAAM,EAAE,SAAS,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,KAAK,MAAM;wCAC9D,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;0DAAiB,SAAS,IAAI;;;;;;0DAC7C,8OAAC;gDAAG,WAAU;0DAAwC,SAAS,IAAI;;;;;;0DACnE,8OAAC;gDAAE,WAAU;0DAAyB,SAAS,KAAK;;;;;;;uCAN/C;;;;;;;;;;;;;;;;;;;;;;;;;;0BAejB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;0CAI1C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,WAAU;;;;;;sDAEZ,8OAAC;4CAAO,WAAU;sDAA6J;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUzL,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAsC;;;;;;sCAGpD,8OAAC;4BAAE,WAAU;sCAA4C;;;;;;sCAGzD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}]}