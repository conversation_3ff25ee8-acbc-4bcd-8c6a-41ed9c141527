{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/agency/apollo-agency-website/src/app/portfolio/social-media/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useLanguage } from '@/contexts/LanguageContext';\nimport Link from 'next/link';\n\nexport default function SocialMediaPortfolioPage() {\n  const { t, isRTL } = useLanguage();\n\n  const projects = [\n    {\n      id: 1,\n      title: 'TechCorp Social Media Campaign',\n      description: 'Complete social media strategy and content creation for a tech startup',\n      platform: 'Instagram, Facebook, LinkedIn',\n      results: ['300% increase in followers', '500% boost in engagement', '200% more leads'],\n      image: '📱',\n      category: 'Social Media Management'\n    },\n    {\n      id: 2,\n      title: 'Restaurant Chain Social Presence',\n      description: 'Food photography and social media marketing for restaurant chain',\n      platform: 'Instagram, TikTok, Facebook',\n      results: ['400% increase in foot traffic', '250% growth in online orders', '600% more social engagement'],\n      image: '🍕',\n      category: 'Content Creation'\n    },\n    {\n      id: 3,\n      title: 'Fashion Brand Influencer Campaign',\n      description: 'Influencer marketing campaign for fashion brand launch',\n      platform: 'Instagram, YouTube, TikTok',\n      results: ['1M+ campaign reach', '50K new followers', '300% sales increase'],\n      image: '👗',\n      category: 'Influencer Marketing'\n    },\n    {\n      id: 4,\n      title: 'Healthcare Social Media Strategy',\n      description: 'Educational content and community building for healthcare provider',\n      platform: 'Facebook, LinkedIn, YouTube',\n      results: ['500% increase in patient inquiries', '200% growth in community', '150% more appointments'],\n      image: '🏥',\n      category: 'Healthcare Marketing'\n    }\n  ];\n\n  return (\n    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : ''}`}>\n      {/* Hero Section */}\n      <section className=\"bg-gradient-to-br from-pink-900 via-purple-800 to-indigo-600 text-white py-20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-4xl mx-auto text-center\">\n            <div className=\"w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6\">\n              <span className=\"text-3xl\">📱</span>\n            </div>\n            <h1 className=\"text-4xl md:text-6xl font-bold mb-6\">\n              {t('portfolio.categories.socialMedia')} Portfolio\n            </h1>\n            <p className=\"text-xl md:text-2xl mb-8 opacity-90\">\n              Discover our social media success stories and campaigns that drove real results\n            </p>\n            <Link\n              href=\"/contact\"\n              className=\"inline-block bg-gradient-to-r from-pink-500 to-purple-500 text-white font-bold px-8 py-4 rounded-full hover:from-pink-400 hover:to-purple-400 transition-all duration-300 transform hover:scale-105\"\n            >\n              Start Your Social Media Journey\n            </Link>\n          </div>\n        </div>\n      </section>\n\n      {/* Projects Grid */}\n      <section className=\"py-20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-6xl mx-auto\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-6\">\n                Featured Social Media Projects\n              </h2>\n              <p className=\"text-xl text-gray-600\">\n                Real campaigns, real results, real impact\n              </p>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8\">\n              {projects.map((project) => (\n                <div key={project.id} className=\"bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300\">\n                  {/* Project Header */}\n                  <div className=\"bg-gradient-to-r from-pink-100 to-purple-100 p-8 text-center\">\n                    <div className=\"text-6xl mb-4\">{project.image}</div>\n                    <span className=\"bg-white/80 text-purple-600 px-3 py-1 rounded-full text-sm font-medium\">\n                      {project.category}\n                    </span>\n                  </div>\n\n                  {/* Project Content */}\n                  <div className=\"p-8\">\n                    <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">{project.title}</h3>\n                    <p className=\"text-gray-600 mb-4\">{project.description}</p>\n                    \n                    <div className=\"mb-6\">\n                      <h4 className=\"font-semibold text-gray-900 mb-2\">Platforms:</h4>\n                      <p className=\"text-purple-600\">{project.platform}</p>\n                    </div>\n\n                    <div className=\"mb-6\">\n                      <h4 className=\"font-semibold text-gray-900 mb-3\">Results Achieved:</h4>\n                      <ul className=\"space-y-2\">\n                        {project.results.map((result, index) => (\n                          <li key={index} className=\"flex items-center text-green-600\">\n                            <span className=\"mr-2\">✓</span>\n                            {result}\n                          </li>\n                        ))}\n                      </ul>\n                    </div>\n\n                    <Link\n                      href={`/case-studies/${project.id}`}\n                      className=\"inline-flex items-center bg-gradient-to-r from-pink-600 to-purple-500 text-white font-bold px-6 py-3 rounded-lg hover:from-pink-700 hover:to-purple-600 transition-all duration-300\"\n                    >\n                      View Full Case Study\n                      <svg className={`w-4 h-4 ${isRTL ? 'mr-2 rotate-180' : 'ml-2'}`} fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                      </svg>\n                    </Link>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Services Offered */}\n      <section className=\"py-20 bg-white\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-4xl mx-auto\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-6\">\n                Our Social Media Services\n              </h2>\n              <p className=\"text-xl text-gray-600\">\n                Comprehensive social media solutions for your business\n              </p>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n              {[\n                { icon: '📝', title: 'Content Creation', description: 'Engaging posts, stories, and visual content' },\n                { icon: '📊', title: 'Strategy Development', description: 'Data-driven social media strategies' },\n                { icon: '👥', title: 'Community Management', description: 'Building and engaging your audience' },\n                { icon: '📈', title: 'Analytics & Reporting', description: 'Detailed performance insights' },\n                { icon: '🎯', title: 'Paid Advertising', description: 'Targeted social media ad campaigns' },\n                { icon: '🤝', title: 'Influencer Marketing', description: 'Collaborations with relevant influencers' }\n              ].map((service, index) => (\n                <div key={index} className=\"text-center p-6 rounded-lg hover:bg-gray-50 transition-colors duration-300\">\n                  <div className=\"text-4xl mb-4\">{service.icon}</div>\n                  <h3 className=\"text-xl font-bold text-gray-900 mb-3\">{service.title}</h3>\n                  <p className=\"text-gray-600\">{service.description}</p>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"bg-gradient-to-r from-pink-600 to-purple-500 text-white py-20\">\n        <div className=\"container mx-auto px-4 text-center\">\n          <h2 className=\"text-3xl md:text-4xl font-bold mb-6\">\n            Ready to Boost Your Social Media Presence?\n          </h2>\n          <p className=\"text-xl mb-8 opacity-90 max-w-2xl mx-auto\">\n            Let's create a social media strategy that drives engagement and grows your business.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Link\n              href=\"/contact\"\n              className=\"bg-white text-purple-600 font-bold px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 transform hover:scale-105\"\n            >\n              Get Social Media Strategy\n            </Link>\n            <Link\n              href=\"/portfolio\"\n              className=\"border-2 border-white text-white font-bold px-8 py-4 rounded-full hover:bg-white hover:text-purple-600 transition-all duration-300\"\n            >\n              View All Projects\n            </Link>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,cAAW,AAAD;IAE/B,MAAM,WAAW;QACf;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;YACV,SAAS;gBAAC;gBAA8B;gBAA4B;aAAkB;YACtF,OAAO;YACP,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;YACV,SAAS;gBAAC;gBAAiC;gBAAgC;aAA8B;YACzG,OAAO;YACP,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;YACV,SAAS;gBAAC;gBAAsB;gBAAqB;aAAsB;YAC3E,OAAO;YACP,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;YACV,SAAS;gBAAC;gBAAsC;gBAA4B;aAAyB;YACrG,OAAO;YACP,UAAU;QACZ;KACD;IAED,qBACE,8OAAC;QAAI,WAAW,CAAC,wBAAwB,EAAE,QAAQ,QAAQ,IAAI;;0BAE7D,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CAAW;;;;;;;;;;;0CAE7B,8OAAC;gCAAG,WAAU;;oCACX,EAAE;oCAAoC;;;;;;;0CAEzC,8OAAC;gCAAE,WAAU;0CAAsC;;;;;;0CAGnD,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;0BAQP,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAGlE,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAKvC,8OAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;wCAAqB,WAAU;;0DAE9B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAiB,QAAQ,KAAK;;;;;;kEAC7C,8OAAC;wDAAK,WAAU;kEACb,QAAQ,QAAQ;;;;;;;;;;;;0DAKrB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAyC,QAAQ,KAAK;;;;;;kEACpE,8OAAC;wDAAE,WAAU;kEAAsB,QAAQ,WAAW;;;;;;kEAEtD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAmC;;;;;;0EACjD,8OAAC;gEAAE,WAAU;0EAAmB,QAAQ,QAAQ;;;;;;;;;;;;kEAGlD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAmC;;;;;;0EACjD,8OAAC;gEAAG,WAAU;0EACX,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAC5B,8OAAC;wEAAe,WAAU;;0FACxB,8OAAC;gFAAK,WAAU;0FAAO;;;;;;4EACtB;;uEAFM;;;;;;;;;;;;;;;;kEAQf,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAM,CAAC,cAAc,EAAE,QAAQ,EAAE,EAAE;wDACnC,WAAU;;4DACX;0EAEC,8OAAC;gEAAI,WAAW,CAAC,QAAQ,EAAE,QAAQ,oBAAoB,QAAQ;gEAAE,MAAK;gEAAO,QAAO;gEAAe,SAAQ;0EACzG,cAAA,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;uCArCnE,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;0BAiD9B,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAGlE,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAKvC,8OAAC;gCAAI,WAAU;0CACZ;oCACC;wCAAE,MAAM;wCAAM,OAAO;wCAAoB,aAAa;oCAA8C;oCACpG;wCAAE,MAAM;wCAAM,OAAO;wCAAwB,aAAa;oCAAsC;oCAChG;wCAAE,MAAM;wCAAM,OAAO;wCAAwB,aAAa;oCAAsC;oCAChG;wCAAE,MAAM;wCAAM,OAAO;wCAAyB,aAAa;oCAAgC;oCAC3F;wCAAE,MAAM;wCAAM,OAAO;wCAAoB,aAAa;oCAAqC;oCAC3F;wCAAE,MAAM;wCAAM,OAAO;wCAAwB,aAAa;oCAA2C;iCACtG,CAAC,GAAG,CAAC,CAAC,SAAS,sBACd,8OAAC;wCAAgB,WAAU;;0DACzB,8OAAC;gDAAI,WAAU;0DAAiB,QAAQ,IAAI;;;;;;0DAC5C,8OAAC;gDAAG,WAAU;0DAAwC,QAAQ,KAAK;;;;;;0DACnE,8OAAC;gDAAE,WAAU;0DAAiB,QAAQ,WAAW;;;;;;;uCAHzC;;;;;;;;;;;;;;;;;;;;;;;;;;0BAYpB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAsC;;;;;;sCAGpD,8OAAC;4BAAE,WAAU;sCAA4C;;;;;;sCAGzD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}]}