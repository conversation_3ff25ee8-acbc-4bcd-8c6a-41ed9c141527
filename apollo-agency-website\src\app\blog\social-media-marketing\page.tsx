'use client';

import { useLanguage } from '@/contexts/LanguageContext';
import Link from 'next/link';

export default function SocialMediaMarketingBlogPage() {
  const { isRTL } = useLanguage();

  const posts = [
    {
      id: 1,
      title: '10 Social Media Marketing Trends to Watch in 2024',
      excerpt: 'Discover the latest trends shaping social media marketing and how to leverage them for your business growth.',
      author: '<PERSON>',
      date: '2024-01-15',
      readTime: '8 min read',
      category: 'Trends',
      image: '📱'
    },
    {
      id: 2,
      title: 'How to Create Engaging Instagram Stories That Convert',
      excerpt: 'Learn the secrets to creating Instagram Stories that capture attention and drive meaningful engagement.',
      author: '<PERSON>',
      date: '2024-01-10',
      readTime: '6 min read',
      category: 'Instagram',
      image: '📸'
    },
    {
      id: 3,
      title: 'The Complete Guide to TikTok Marketing for Businesses',
      excerpt: 'Everything you need to know about marketing your business on TikTok and reaching Gen Z audiences.',
      author: '<PERSON>',
      date: '2024-01-05',
      readTime: '12 min read',
      category: 'TikTok',
      image: '🎵'
    },
    {
      id: 4,
      title: 'LinkedIn Marketing: Building B2B Relationships That Matter',
      excerpt: 'Master LinkedIn marketing strategies to build professional relationships and generate quality leads.',
      author: 'David Chen',
      date: '2023-12-28',
      readTime: '10 min read',
      category: 'LinkedIn',
      image: '💼'
    },
    {
      id: 5,
      title: 'Social Media Analytics: Metrics That Actually Matter',
      excerpt: 'Cut through the noise and focus on the social media metrics that truly impact your business goals.',
      author: 'Lisa Thompson',
      date: '2023-12-20',
      readTime: '7 min read',
      category: 'Analytics',
      image: '📊'
    },
    {
      id: 6,
      title: 'Creating a Social Media Content Calendar That Works',
      excerpt: 'Step-by-step guide to planning and organizing your social media content for maximum impact.',
      author: 'Michael Brown',
      date: '2023-12-15',
      readTime: '9 min read',
      category: 'Strategy',
      image: '📅'
    }
  ];

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : ''}`}>
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-900 via-purple-800 to-pink-600 text-white py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6">
              <span className="text-3xl">📱</span>
            </div>
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Social Media Marketing Blog
            </h1>
            <p className="text-xl md:text-2xl mb-8 opacity-90">
              Expert insights, tips, and strategies for social media marketing success
            </p>
            <Link
              href="/contact"
              className="inline-block bg-gradient-to-r from-blue-500 to-purple-500 text-white font-bold px-8 py-4 rounded-full hover:from-blue-400 hover:to-purple-400 transition-all duration-300 transform hover:scale-105"
            >
              Get Social Media Strategy
            </Link>
          </div>
        </div>
      </section>

      {/* Blog Posts Grid */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Latest Social Media Marketing Articles
              </h2>
              <p className="text-xl text-gray-600">
                Stay updated with the latest trends and strategies
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {posts.map((post) => (
                <article key={post.id} className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300">
                  {/* Post Header */}
                  <div className="bg-gradient-to-r from-blue-100 to-purple-100 p-8 text-center">
                    <div className="text-6xl mb-4">{post.image}</div>
                    <span className="bg-white/80 text-blue-600 px-3 py-1 rounded-full text-sm font-medium">
                      {post.category}
                    </span>
                  </div>

                  {/* Post Content */}
                  <div className="p-6">
                    <h3 className="text-xl font-bold text-gray-900 mb-3 line-clamp-2">
                      {post.title}
                    </h3>
                    <p className="text-gray-600 mb-4 line-clamp-3">
                      {post.excerpt}
                    </p>
                    
                    <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                      <span>{post.author}</span>
                      <span>{post.readTime}</span>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500">{post.date}</span>
                      <Link
                        href={`/blog/social-media-marketing/${post.id}`}
                        className="inline-flex items-center text-blue-600 hover:text-blue-700 font-medium"
                      >
                        Read More
                        <svg className={`w-4 h-4 ${isRTL ? 'mr-1 rotate-180' : 'ml-1'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                      </Link>
                    </div>
                  </div>
                </article>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Categories */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Explore More Topics
              </h2>
              <p className="text-xl text-gray-600">
                Discover insights across different social media platforms
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[
                { name: 'Instagram Marketing', icon: '📸', count: '25 articles' },
                { name: 'Facebook Advertising', icon: '👥', count: '18 articles' },
                { name: 'TikTok Strategy', icon: '🎵', count: '12 articles' },
                { name: 'LinkedIn B2B', icon: '💼', count: '15 articles' },
                { name: 'Twitter Engagement', icon: '🐦', count: '10 articles' },
                { name: 'YouTube Marketing', icon: '📺', count: '8 articles' },
                { name: 'Pinterest Strategy', icon: '📌', count: '6 articles' },
                { name: 'Social Analytics', icon: '📊', count: '20 articles' }
              ].map((category, index) => (
                <Link
                  key={index}
                  href={`/blog/${category.name.toLowerCase().replace(' ', '-')}`}
                  className="text-center p-6 bg-gray-50 rounded-lg hover:bg-blue-50 transition-colors duration-300"
                >
                  <div className="text-4xl mb-3">{category.icon}</div>
                  <h3 className="text-lg font-bold text-gray-900 mb-2">{category.name}</h3>
                  <p className="text-gray-600 text-sm">{category.count}</p>
                </Link>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Newsletter Signup */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Stay Updated with Social Media Trends
            </h2>
            <p className="text-xl text-gray-600 mb-8">
              Get the latest social media marketing insights delivered to your inbox
            </p>
            
            <div className="max-w-md mx-auto">
              <div className="flex gap-4">
                <input
                  type="email"
                  placeholder="Enter your email"
                  className="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <button className="bg-gradient-to-r from-blue-600 to-purple-500 text-white font-bold px-6 py-3 rounded-lg hover:from-blue-700 hover:to-purple-600 transition-all duration-300">
                  Subscribe
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-gradient-to-r from-blue-600 to-purple-500 text-white py-20">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Need Help with Your Social Media Strategy?
          </h2>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Let our experts help you create and execute a social media strategy that drives results.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/contact"
              className="bg-white text-blue-600 font-bold px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 transform hover:scale-105"
            >
              Get Social Media Help
            </Link>
            <Link
              href="/services/social-media"
              className="border-2 border-white text-white font-bold px-8 py-4 rounded-full hover:bg-white hover:text-blue-600 transition-all duration-300"
            >
              View Our Services
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
