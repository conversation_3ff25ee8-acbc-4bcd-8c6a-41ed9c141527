'use client';

import Link from 'next/link';

const ServicesSection = () => {
  const services = [
    {
      icon: "🎯",
      title: "Brand Development",
      description: "We develop unique brands that enhance your digital presence",
      link: "/services/branding"
    },
    {
      icon: "✍️",
      title: "Content Creation",
      description: "We write content that aligns with the products and services offered",
      link: "/services/content-creation"
    },
    {
      icon: "👥",
      title: "Influencer Marketing",
      description: "We ensure successful marketing through influential people",
      link: "/services/influencer-marketing"
    },
    {
      icon: "📱",
      title: "Digital Marketing",
      description: "We launch carefully studied and focused advertising campaigns to achieve your goals accurately",
      link: "/services/digital-marketing"
    },
    {
      icon: "💻",
      title: "Website Development",
      description: "We design and develop websites using the latest programming technologies",
      link: "/services/web-development"
    },
    {
      icon: "🎨",
      title: "Graphic Design",
      description: "We understand and analyze your needs and provide you with the best designs that serve your business",
      link: "/services/graphic-design"
    },
    {
      icon: "🎬",
      title: "Video Production",
      description: "We shoot and create professional advertising videos",
      link: "/services/video-production"
    },
    {
      icon: "🔍",
      title: "SEO Services",
      description: "We improve the appearance of websites on search engines",
      link: "/services/seo"
    }
  ];

  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="flex justify-center mb-6">
            <div className="w-16 h-16 bg-gradient-to-r from-purple-600 to-pink-500 rounded-full flex items-center justify-center">
              <span className="text-2xl">🚀</span>
            </div>
          </div>
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Everything You Need to Launch Your Business in One Place!
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            With Apollo Agency for Marketing Solutions
          </p>
        </div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {services.map((service, index) => (
            <div
              key={index}
              className="bg-white rounded-xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 group"
            >
              {/* Icon */}
              <div className="text-center mb-6">
                <div className="w-20 h-20 bg-gradient-to-r from-purple-100 to-pink-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:from-purple-200 group-hover:to-pink-200 transition-all duration-300">
                  <span className="text-3xl">{service.icon}</span>
                </div>
              </div>

              {/* Content */}
              <div className="text-center">
                <h3 className="text-xl font-bold text-gray-900 mb-4 group-hover:text-purple-600 transition-colors duration-300">
                  {service.title}
                </h3>
                <p className="text-gray-600 mb-6 leading-relaxed">
                  {service.description}
                </p>
                <Link
                  href={service.link}
                  className="inline-flex items-center text-purple-600 hover:text-purple-700 font-semibold transition-colors duration-300"
                >
                  Learn More
                  <svg className="ml-2 w-4 h-4 transform group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </Link>
              </div>
            </div>
          ))}
        </div>

        {/* CTA Section */}
        <div className="text-center mt-16">
          <Link
            href="/services"
            className="inline-block bg-gradient-to-r from-purple-600 to-pink-500 text-white font-bold px-8 py-4 rounded-full text-lg hover:from-purple-700 hover:to-pink-600 transition-all duration-300 transform hover:scale-105 hover:shadow-xl"
          >
            View All Services
          </Link>
        </div>
      </div>
    </section>
  );
};

export default ServicesSection;
