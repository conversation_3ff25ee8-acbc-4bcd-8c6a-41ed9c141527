{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/agency/apollo-agency-website/src/app/case-studies/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useLanguage } from '@/contexts/LanguageContext';\nimport Link from 'next/link';\n\nexport default function CaseStudiesPage() {\n  const { t, isRTL } = useLanguage();\n\n  const caseStudies = [\n    {\n      id: 1,\n      title: 'E-commerce Growth: 300% Revenue Increase',\n      client: 'TechCorp',\n      industry: 'Technology',\n      challenge: 'Low online sales and poor website conversion rates',\n      solution: 'Complete digital marketing overhaul with SEO, PPC, and conversion optimization',\n      results: ['300% increase in revenue', '250% increase in organic traffic', '180% improvement in conversion rate'],\n      image: '🛍️',\n      duration: '6 months'\n    },\n    {\n      id: 2,\n      title: 'Brand Transformation for Healthcare Provider',\n      client: 'HealthPlus',\n      industry: 'Healthcare',\n      challenge: 'Outdated brand image and low patient engagement',\n      solution: 'Complete brand redesign with digital marketing strategy',\n      results: ['400% increase in patient inquiries', '200% growth in social media following', '150% improvement in brand recognition'],\n      image: '🏥',\n      duration: '4 months'\n    },\n    {\n      id: 3,\n      title: 'Local Restaurant Chain Digital Success',\n      client: 'FoodieApp',\n      industry: 'Food & Beverage',\n      challenge: 'Limited online presence and declining foot traffic',\n      solution: 'Local SEO, social media marketing, and online ordering system',\n      results: ['500% increase in online orders', '300% growth in social media engagement', '200% increase in foot traffic'],\n      image: '🍕',\n      duration: '8 months'\n    },\n    {\n      id: 4,\n      title: 'Startup Launch: From Zero to Market Leader',\n      client: 'StartupHub',\n      industry: 'Technology',\n      challenge: 'New company with no market presence',\n      solution: 'Complete brand development and digital marketing launch strategy',\n      results: ['Market leader position in 12 months', '1000% increase in brand awareness', '500% growth in customer base'],\n      image: '🚀',\n      duration: '12 months'\n    }\n  ];\n\n  return (\n    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : ''}`}>\n      {/* Hero Section */}\n      <section className=\"bg-gradient-to-br from-purple-900 via-indigo-800 to-blue-600 text-white py-20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-4xl mx-auto text-center\">\n            <h1 className=\"text-4xl md:text-6xl font-bold mb-6\">\n              {t('header.caseStudies')}\n            </h1>\n            <p className=\"text-xl md:text-2xl mb-8 opacity-90\">\n              Real results from real clients - see how we've helped businesses achieve their goals\n            </p>\n          </div>\n        </div>\n      </section>\n\n      {/* Case Studies Grid */}\n      <section className=\"py-20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"space-y-16\">\n            {caseStudies.map((study, index) => (\n              <div\n                key={study.id}\n                className={`bg-white rounded-xl shadow-lg overflow-hidden ${\n                  index % 2 === 0 ? 'lg:flex-row' : 'lg:flex-row-reverse'\n                } lg:flex`}\n              >\n                {/* Image/Icon Section */}\n                <div className=\"lg:w-1/3 bg-gradient-to-br from-purple-100 to-blue-100 flex items-center justify-center p-12\">\n                  <div className=\"text-center\">\n                    <div className=\"text-8xl mb-4\">{study.image}</div>\n                    <div className=\"text-purple-600 font-semibold\">{study.industry}</div>\n                    <div className=\"text-gray-600 text-sm\">{study.duration}</div>\n                  </div>\n                </div>\n\n                {/* Content Section */}\n                <div className=\"lg:w-2/3 p-8 lg:p-12\">\n                  <div className=\"mb-4\">\n                    <span className=\"bg-purple-100 text-purple-600 px-3 py-1 rounded-full text-sm font-medium\">\n                      {study.client}\n                    </span>\n                  </div>\n                  \n                  <h3 className=\"text-2xl lg:text-3xl font-bold text-gray-900 mb-4\">\n                    {study.title}\n                  </h3>\n\n                  <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-6\">\n                    <div>\n                      <h4 className=\"font-semibold text-gray-900 mb-2\">Challenge</h4>\n                      <p className=\"text-gray-600 text-sm\">{study.challenge}</p>\n                    </div>\n                    <div>\n                      <h4 className=\"font-semibold text-gray-900 mb-2\">Solution</h4>\n                      <p className=\"text-gray-600 text-sm\">{study.solution}</p>\n                    </div>\n                    <div>\n                      <h4 className=\"font-semibold text-gray-900 mb-2\">Results</h4>\n                      <ul className=\"space-y-1\">\n                        {study.results.map((result, resultIndex) => (\n                          <li key={resultIndex} className=\"text-green-600 text-sm flex items-center\">\n                            <span className=\"mr-2\">✓</span>\n                            {result}\n                          </li>\n                        ))}\n                      </ul>\n                    </div>\n                  </div>\n\n                  <Link\n                    href={`/case-studies/${study.id}`}\n                    className=\"inline-flex items-center bg-gradient-to-r from-purple-600 to-blue-500 text-white font-bold px-6 py-3 rounded-lg hover:from-purple-700 hover:to-blue-600 transition-all duration-300\"\n                  >\n                    Read Full Case Study\n                    <svg className={`w-4 h-4 ${isRTL ? 'mr-2 rotate-180' : 'ml-2'}`} fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                    </svg>\n                  </Link>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Stats Section */}\n      <section className=\"py-20 bg-white\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-6\">\n              Our Track Record\n            </h2>\n            <p className=\"text-xl text-gray-600\">\n              Numbers that speak for themselves\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n            <div className=\"text-center\">\n              <div className=\"text-4xl md:text-5xl font-bold text-purple-600 mb-2\">500+</div>\n              <div className=\"text-gray-600\">Successful Projects</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-4xl md:text-5xl font-bold text-purple-600 mb-2\">200+</div>\n              <div className=\"text-gray-600\">Happy Clients</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-4xl md:text-5xl font-bold text-purple-600 mb-2\">300%</div>\n              <div className=\"text-gray-600\">Average ROI Increase</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-4xl md:text-5xl font-bold text-purple-600 mb-2\">98%</div>\n              <div className=\"text-gray-600\">Client Satisfaction</div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"bg-gradient-to-r from-purple-600 to-blue-500 text-white py-20\">\n        <div className=\"container mx-auto px-4 text-center\">\n          <h2 className=\"text-3xl md:text-4xl font-bold mb-6\">\n            Ready to Become Our Next Success Story?\n          </h2>\n          <p className=\"text-xl mb-8 opacity-90 max-w-2xl mx-auto\">\n            Let's discuss how we can help your business achieve similar results.\n          </p>\n          <Link\n            href=\"/contact\"\n            className=\"bg-white text-purple-600 font-bold px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 transform hover:scale-105\"\n          >\n            Start Your Success Story\n          </Link>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,cAAW,AAAD;IAE/B,MAAM,cAAc;QAClB;YACE,IAAI;YACJ,OAAO;YACP,QAAQ;YACR,UAAU;YACV,WAAW;YACX,UAAU;YACV,SAAS;gBAAC;gBAA4B;gBAAoC;aAAsC;YAChH,OAAO;YACP,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,QAAQ;YACR,UAAU;YACV,WAAW;YACX,UAAU;YACV,SAAS;gBAAC;gBAAsC;gBAAyC;aAAwC;YACjI,OAAO;YACP,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,QAAQ;YACR,UAAU;YACV,WAAW;YACX,UAAU;YACV,SAAS;gBAAC;gBAAkC;gBAA0C;aAAgC;YACtH,OAAO;YACP,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,QAAQ;YACR,UAAU;YACV,WAAW;YACX,UAAU;YACV,SAAS;gBAAC;gBAAuC;gBAAqC;aAA+B;YACrH,OAAO;YACP,UAAU;QACZ;KACD;IAED,qBACE,8OAAC;QAAI,WAAW,CAAC,wBAAwB,EAAE,QAAQ,QAAQ,IAAI;;0BAE7D,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CACX,EAAE;;;;;;0CAEL,8OAAC;gCAAE,WAAU;0CAAsC;;;;;;;;;;;;;;;;;;;;;;0BAQzD,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ,YAAY,GAAG,CAAC,CAAC,OAAO,sBACvB,8OAAC;gCAEC,WAAW,CAAC,8CAA8C,EACxD,QAAQ,MAAM,IAAI,gBAAgB,sBACnC,QAAQ,CAAC;;kDAGV,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAiB,MAAM,KAAK;;;;;;8DAC3C,8OAAC;oDAAI,WAAU;8DAAiC,MAAM,QAAQ;;;;;;8DAC9D,8OAAC;oDAAI,WAAU;8DAAyB,MAAM,QAAQ;;;;;;;;;;;;;;;;;kDAK1D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DACb,MAAM,MAAM;;;;;;;;;;;0DAIjB,8OAAC;gDAAG,WAAU;0DACX,MAAM,KAAK;;;;;;0DAGd,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAmC;;;;;;0EACjD,8OAAC;gEAAE,WAAU;0EAAyB,MAAM,SAAS;;;;;;;;;;;;kEAEvD,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAmC;;;;;;0EACjD,8OAAC;gEAAE,WAAU;0EAAyB,MAAM,QAAQ;;;;;;;;;;;;kEAEtD,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAmC;;;;;;0EACjD,8OAAC;gEAAG,WAAU;0EACX,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,4BAC1B,8OAAC;wEAAqB,WAAU;;0FAC9B,8OAAC;gFAAK,WAAU;0FAAO;;;;;;4EACtB;;uEAFM;;;;;;;;;;;;;;;;;;;;;;0DASjB,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,CAAC,cAAc,EAAE,MAAM,EAAE,EAAE;gDACjC,WAAU;;oDACX;kEAEC,8OAAC;wDAAI,WAAW,CAAC,QAAQ,EAAE,QAAQ,oBAAoB,QAAQ;wDAAE,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEACzG,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;+BAtDtE,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;0BAiEvB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAKvC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAsD;;;;;;sDACrE,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;;;;;;;8CAEjC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAsD;;;;;;sDACrE,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;;;;;;;8CAEjC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAsD;;;;;;sDACrE,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;;;;;;;8CAEjC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAsD;;;;;;sDACrE,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOvC,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAsC;;;;;;sCAGpD,8OAAC;4BAAE,WAAU;sCAA4C;;;;;;sCAGzD,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAOX", "debugId": null}}]}