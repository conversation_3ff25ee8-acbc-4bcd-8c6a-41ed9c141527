'use client';

import { useLanguage } from '@/contexts/LanguageContext';
import Link from 'next/link';

export default function SocialMediaPage() {
  const { t, isRTL } = useLanguage();

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : ''}`}>
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-900 via-purple-800 to-pink-600 text-white py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6">
              <span className="text-3xl">📱</span>
            </div>
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              {t('services.socialMedia')}
            </h1>
            <p className="text-xl md:text-2xl mb-8 opacity-90">
              Build your brand presence and engage with your audience across all social platforms
            </p>
            <Link
              href="/contact"
              className="inline-block bg-gradient-to-r from-blue-500 to-purple-500 text-white font-bold px-8 py-4 rounded-full hover:from-blue-400 hover:to-purple-400 transition-all duration-300 transform hover:scale-105"
            >
              Boost Your Social Presence
            </Link>
          </div>
        </div>
      </section>

      {/* Services Overview */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Complete Social Media Management
              </h2>
              <p className="text-xl text-gray-600">
                From strategy to execution, we handle every aspect of your social media presence
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  icon: '📝',
                  title: 'Content Creation',
                  description: 'Engaging posts, stories, and visual content that resonates with your audience.',
                  features: ['Custom graphics', 'Video content', 'Copywriting', 'Brand consistency']
                },
                {
                  icon: '📊',
                  title: 'Strategy Development',
                  description: 'Data-driven social media strategies tailored to your business goals.',
                  features: ['Audience analysis', 'Competitor research', 'Content planning', 'Growth strategy']
                },
                {
                  icon: '👥',
                  title: 'Community Management',
                  description: 'Build and nurture your online community with active engagement.',
                  features: ['Response management', 'Community building', 'Customer service', 'Reputation management']
                },
                {
                  icon: '🎯',
                  title: 'Paid Advertising',
                  description: 'Targeted social media ad campaigns that drive results and ROI.',
                  features: ['Campaign setup', 'Audience targeting', 'Ad optimization', 'Performance tracking']
                },
                {
                  icon: '📈',
                  title: 'Analytics & Reporting',
                  description: 'Detailed insights and reports on your social media performance.',
                  features: ['Performance metrics', 'Growth tracking', 'ROI analysis', 'Monthly reports']
                },
                {
                  icon: '🤝',
                  title: 'Influencer Partnerships',
                  description: 'Connect with relevant influencers to expand your reach.',
                  features: ['Influencer outreach', 'Partnership management', 'Campaign coordination', 'Performance tracking']
                }
              ].map((service, index) => (
                <div key={index} className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300">
                  <div className="text-4xl mb-4">{service.icon}</div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{service.title}</h3>
                  <p className="text-gray-600 mb-4">{service.description}</p>
                  <ul className="space-y-2">
                    {service.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center text-gray-600 text-sm">
                        <span className="text-blue-600 mr-2">✓</span>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Platforms We Cover */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Platforms We Specialize In
              </h2>
              <p className="text-xl text-gray-600">
                Expert management across all major social media platforms
              </p>
            </div>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              {[
                { platform: 'Instagram', icon: '📷', description: 'Visual storytelling and engagement' },
                { platform: 'Facebook', icon: '👥', description: 'Community building and advertising' },
                { platform: 'LinkedIn', icon: '💼', description: 'Professional networking and B2B' },
                { platform: 'TikTok', icon: '🎵', description: 'Viral content and Gen Z reach' },
                { platform: 'Twitter', icon: '🐦', description: 'Real-time engagement and news' },
                { platform: 'YouTube', icon: '📺', description: 'Video content and education' },
                { platform: 'Pinterest', icon: '📌', description: 'Visual discovery and inspiration' },
                { platform: 'Snapchat', icon: '👻', description: 'Ephemeral content and AR' }
              ].map((platform, index) => (
                <div key={index} className="text-center p-6 bg-gray-50 rounded-lg hover:bg-blue-50 transition-colors duration-300">
                  <div className="text-4xl mb-3">{platform.icon}</div>
                  <h3 className="text-lg font-bold text-gray-900 mb-2">{platform.platform}</h3>
                  <p className="text-gray-600 text-sm">{platform.description}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Social Media Process */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Our Social Media Management Process
              </h2>
              <p className="text-xl text-gray-600">
                Strategic approach to building your social media presence
              </p>
            </div>
            
            <div className="space-y-8">
              {[
                { 
                  step: '01', 
                  title: 'Audit & Strategy', 
                  description: 'Analyze current presence and develop comprehensive social media strategy.' 
                },
                { 
                  step: '02', 
                  title: 'Content Planning', 
                  description: 'Create content calendar with engaging posts tailored to each platform.' 
                },
                { 
                  step: '03', 
                  title: 'Content Creation', 
                  description: 'Produce high-quality visuals, videos, and copy that resonates with your audience.' 
                },
                { 
                  step: '04', 
                  title: 'Publishing & Engagement', 
                  description: 'Schedule posts and actively engage with your community in real-time.' 
                },
                { 
                  step: '05', 
                  title: 'Analytics & Optimization', 
                  description: 'Monitor performance and continuously optimize strategy for better results.' 
                }
              ].map((item, index) => (
                <div key={index} className="flex items-start space-x-6">
                  <div className="w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-500 text-white rounded-full flex items-center justify-center text-xl font-bold flex-shrink-0">
                    {item.step}
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-3">{item.title}</h3>
                    <p className="text-gray-600 text-lg">{item.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Success Metrics */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Social Media Success Metrics
            </h2>
            <p className="text-xl text-gray-600 mb-12">
              Results that matter to your business growth
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
              <div className="text-center">
                <div className="text-4xl md:text-5xl font-bold text-blue-600 mb-2">300%</div>
                <div className="text-gray-600">Average Engagement Increase</div>
              </div>
              <div className="text-center">
                <div className="text-4xl md:text-5xl font-bold text-blue-600 mb-2">500K+</div>
                <div className="text-gray-600">Followers Generated</div>
              </div>
              <div className="text-center">
                <div className="text-4xl md:text-5xl font-bold text-blue-600 mb-2">250%</div>
                <div className="text-gray-600">Brand Awareness Boost</div>
              </div>
              <div className="text-center">
                <div className="text-4xl md:text-5xl font-bold text-blue-600 mb-2">95%</div>
                <div className="text-gray-600">Client Satisfaction Rate</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-gradient-to-r from-blue-600 to-purple-500 text-white py-20">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Ready to Dominate Social Media?
          </h2>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Let's build a social media presence that engages your audience and grows your business.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/contact"
              className="bg-white text-blue-600 font-bold px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 transform hover:scale-105"
            >
              Start Social Media Strategy
            </Link>
            <Link
              href="/portfolio/social-media"
              className="border-2 border-white text-white font-bold px-8 py-4 rounded-full hover:bg-white hover:text-blue-600 transition-all duration-300"
            >
              View Social Media Work
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
