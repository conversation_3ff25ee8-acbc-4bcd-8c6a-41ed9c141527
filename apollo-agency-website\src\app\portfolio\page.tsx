'use client';

import { useLanguage } from '@/contexts/LanguageContext';
import Link from 'next/link';

export default function PortfolioPage() {
  const { t, isRTL } = useLanguage();

  const portfolioItems = [
    {
      id: 1,
      title: 'E-commerce Brand Identity',
      category: 'Branding',
      description: 'Complete brand identity design for a modern e-commerce platform',
      image: '🛍️',
      tags: ['Logo Design', 'Brand Guidelines', 'Visual Identity']
    },
    {
      id: 2,
      title: 'Social Media Campaign',
      category: 'Social Media',
      description: 'Comprehensive social media strategy and content creation',
      image: '📱',
      tags: ['Content Creation', 'Strategy', 'Analytics']
    },
    {
      id: 3,
      title: 'SEO Optimization Project',
      category: 'SEO',
      description: 'Complete SEO overhaul resulting in 300% traffic increase',
      image: '📈',
      tags: ['Technical SEO', 'Content Optimization', 'Link Building']
    },
    {
      id: 4,
      title: 'Restaurant Digital Marketing',
      category: 'Digital Marketing',
      description: 'Full digital marketing campaign for restaurant chain',
      image: '🍕',
      tags: ['Google Ads', 'Social Media', 'Local SEO']
    },
    {
      id: 5,
      title: 'Tech Startup Branding',
      category: 'Branding',
      description: 'Modern brand identity for innovative tech startup',
      image: '💻',
      tags: ['Logo Design', 'Website Design', 'Marketing Materials']
    },
    {
      id: 6,
      title: 'Healthcare Website Design',
      category: 'Web Design',
      description: 'User-friendly website design for healthcare provider',
      image: '🏥',
      tags: ['UI/UX Design', 'Responsive Design', 'Accessibility']
    }
  ];

  const categories = ['All', 'Branding', 'Social Media', 'SEO', 'Digital Marketing', 'Web Design'];

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : ''}`}>
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-indigo-900 via-purple-800 to-pink-600 text-white py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              {t('header.portfolio')}
            </h1>
            <p className="text-xl md:text-2xl mb-8 opacity-90">
              {t('portfolio.subtitle')}
            </p>
          </div>
        </div>
      </section>

      {/* Portfolio Filter */}
      <section className="py-12 bg-white">
        <div className="container mx-auto px-4">
          <div className="flex flex-wrap justify-center gap-4">
            {categories.map((category) => (
              <button
                key={category}
                className="px-6 py-2 rounded-full border-2 border-purple-200 text-purple-600 hover:bg-purple-600 hover:text-white transition-all duration-300 font-medium"
              >
                {category}
              </button>
            ))}
          </div>
        </div>
      </section>

      {/* Portfolio Grid */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {portfolioItems.map((item) => (
              <div
                key={item.id}
                className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 overflow-hidden"
              >
                {/* Image/Icon */}
                <div className="h-64 bg-gradient-to-br from-purple-100 to-pink-100 flex items-center justify-center relative">
                  <div className="text-6xl">{item.image}</div>
                  {/* Category Badge */}
                  <div className="absolute top-4 left-4">
                    <span className="bg-white/90 backdrop-blur-sm text-purple-600 px-3 py-1 rounded-full text-sm font-medium">
                      {item.category}
                    </span>
                  </div>
                </div>

                {/* Content */}
                <div className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-3">
                    {item.title}
                  </h3>
                  <p className="text-gray-600 mb-4">
                    {item.description}
                  </p>

                  {/* Tags */}
                  <div className="flex flex-wrap gap-2 mb-4">
                    {item.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="bg-gray-100 text-gray-600 px-3 py-1 rounded-full text-sm"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>

                  {/* View Project Button */}
                  <button className="w-full bg-gradient-to-r from-purple-600 to-pink-500 text-white font-bold py-3 px-6 rounded-lg hover:from-purple-700 hover:to-pink-600 transition-all duration-300 transform hover:scale-105">
                    {t('portfolio.viewProject')}
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-gradient-to-r from-indigo-600 to-purple-500 text-white py-20">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Ready to Start Your Project?
          </h2>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Let's create something amazing together. Contact us to discuss your project.
          </p>
          <Link
            href="/contact"
            className="bg-white text-indigo-600 font-bold px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 transform hover:scale-105"
          >
            Start Your Project
          </Link>
        </div>
      </section>
    </div>
  );
}
