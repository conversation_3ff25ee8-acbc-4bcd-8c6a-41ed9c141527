(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[956],{1436:(e,t,i)=>{Promise.resolve().then(i.bind(i,2918))},2918:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>l});var s=i(5155),a=i(9283),r=i(6874),n=i.n(r);function l(){let{t:e,isRTL:t}=(0,a.o)();return(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50 ".concat(t?"rtl":""),children:[(0,s.jsx)("section",{className:"bg-gradient-to-br from-indigo-900 via-purple-800 to-pink-600 text-white py-20",children:(0,s.jsx)("div",{className:"container mx-auto px-4",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto text-center",children:[(0,s.jsx)("div",{className:"w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,s.jsx)("span",{className:"text-3xl",children:"\uD83C\uDFAF"})}),(0,s.jsxs)("h1",{className:"text-4xl md:text-6xl font-bold mb-6",children:[e("portfolio.categories.brandIdentity")," Portfolio"]}),(0,s.jsx)("p",{className:"text-xl md:text-2xl mb-8 opacity-90",children:"Creating memorable brand identities that connect with your audience"}),(0,s.jsx)(n(),{href:"/contact",className:"inline-block bg-gradient-to-r from-purple-500 to-pink-500 text-white font-bold px-8 py-4 rounded-full hover:from-purple-400 hover:to-pink-400 transition-all duration-300 transform hover:scale-105",children:"Build Your Brand Identity"})]})})}),(0,s.jsx)("section",{className:"py-20",children:(0,s.jsx)("div",{className:"container mx-auto px-4",children:(0,s.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,s.jsxs)("div",{className:"text-center mb-16",children:[(0,s.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-6",children:"Featured Brand Identity Projects"}),(0,s.jsx)("p",{className:"text-xl text-gray-600",children:"Brands we've helped establish and grow"})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[{id:1,title:"TechCorp Complete Rebrand",description:"Full brand identity redesign for technology company",deliverables:["Logo Design","Brand Guidelines","Color Palette","Typography","Business Cards","Letterhead"],results:["300% increase in brand recognition","Improved market positioning","Consistent brand experience"],image:"\uD83C\uDFAF",category:"Complete Rebrand"},{id:2,title:"StartupHub Brand Launch",description:"Brand identity creation for new startup accelerator",deliverables:["Logo Design","Brand Strategy","Visual Identity","Marketing Materials","Website Design"],results:["Strong market entry","Professional credibility","Investor confidence"],image:"\uD83D\uDE80",category:"Brand Launch"},{id:3,title:"HealthPlus Medical Brand",description:"Healthcare brand identity with trust and professionalism focus",deliverables:["Medical Logo","Patient Materials","Signage Design","Digital Assets"],results:["Increased patient trust","Professional appearance","Clear communication"],image:"\uD83C\uDFE5",category:"Healthcare Branding"},{id:4,title:"FoodieApp Restaurant Brand",description:"Modern restaurant brand identity with appetite appeal",deliverables:["Restaurant Logo","Menu Design","Packaging","Interior Graphics"],results:["200% increase in customers","Premium brand perception","Social media buzz"],image:"\uD83C\uDF55",category:"Restaurant Branding"}].map(e=>(0,s.jsxs)("div",{className:"bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300",children:[(0,s.jsxs)("div",{className:"bg-gradient-to-r from-indigo-100 to-purple-100 p-8 text-center",children:[(0,s.jsx)("div",{className:"text-6xl mb-4",children:e.image}),(0,s.jsx)("span",{className:"bg-white/80 text-indigo-600 px-3 py-1 rounded-full text-sm font-medium",children:e.category})]}),(0,s.jsxs)("div",{className:"p-8",children:[(0,s.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-4",children:e.title}),(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:e.description}),(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"Deliverables:"}),(0,s.jsx)("div",{className:"flex flex-wrap gap-2",children:e.deliverables.map((e,t)=>(0,s.jsx)("span",{className:"bg-indigo-100 text-indigo-600 px-3 py-1 rounded-full text-sm",children:e},t))})]}),(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("h4",{className:"font-semibold text-gray-900 mb-3",children:"Results Achieved:"}),(0,s.jsx)("ul",{className:"space-y-2",children:e.results.map((e,t)=>(0,s.jsxs)("li",{className:"flex items-center text-green-600",children:[(0,s.jsx)("span",{className:"mr-2",children:"✓"}),e]},t))})]}),(0,s.jsxs)(n(),{href:"/case-studies/".concat(e.id),className:"inline-flex items-center bg-gradient-to-r from-indigo-600 to-purple-500 text-white font-bold px-6 py-3 rounded-lg hover:from-indigo-700 hover:to-purple-600 transition-all duration-300",children:["View Full Case Study",(0,s.jsx)("svg",{className:"w-4 h-4 ".concat(t?"mr-2 rotate-180":"ml-2"),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})]})]})]},e.id))})]})})}),(0,s.jsx)("section",{className:"py-20 bg-white",children:(0,s.jsx)("div",{className:"container mx-auto px-4",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,s.jsxs)("div",{className:"text-center mb-16",children:[(0,s.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-6",children:"Our Brand Identity Services"}),(0,s.jsx)("p",{className:"text-xl text-gray-600",children:"Complete brand identity solutions from concept to implementation"})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:[{icon:"\uD83C\uDFA8",title:"Logo Design",description:"Unique logos that represent your brand essence"},{icon:"\uD83D\uDCCB",title:"Brand Guidelines",description:"Comprehensive brand usage guidelines"},{icon:"\uD83C\uDFA8",title:"Visual Identity",description:"Color palettes, typography, and visual elements"},{icon:"\uD83D\uDCC4",title:"Stationery Design",description:"Business cards, letterheads, and documents"},{icon:"\uD83D\uDCE6",title:"Packaging Design",description:"Product packaging that reflects your brand"},{icon:"\uD83C\uDF10",title:"Digital Assets",description:"Web graphics and social media templates"}].map((e,t)=>(0,s.jsxs)("div",{className:"text-center p-6 rounded-lg hover:bg-gray-50 transition-colors duration-300",children:[(0,s.jsx)("div",{className:"text-4xl mb-4",children:e.icon}),(0,s.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-3",children:e.title}),(0,s.jsx)("p",{className:"text-gray-600",children:e.description})]},t))})]})})}),(0,s.jsx)("section",{className:"py-20 bg-gray-50",children:(0,s.jsx)("div",{className:"container mx-auto px-4",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,s.jsxs)("div",{className:"text-center mb-16",children:[(0,s.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-6",children:"Our Brand Development Process"}),(0,s.jsx)("p",{className:"text-xl text-gray-600",children:"Strategic approach to building powerful brand identities"})]}),(0,s.jsx)("div",{className:"space-y-8",children:[{step:"01",title:"Brand Discovery",description:"Understanding your business, values, and target audience"},{step:"02",title:"Strategy Development",description:"Creating brand positioning and messaging strategy"},{step:"03",title:"Visual Identity",description:"Designing logo, colors, typography, and visual elements"},{step:"04",title:"Brand Guidelines",description:"Creating comprehensive brand usage guidelines"},{step:"05",title:"Implementation",description:"Applying brand across all touchpoints and materials"}].map((e,t)=>(0,s.jsxs)("div",{className:"flex items-start space-x-6",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-gradient-to-r from-indigo-600 to-purple-500 text-white rounded-full flex items-center justify-center text-xl font-bold flex-shrink-0",children:e.step}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-3",children:e.title}),(0,s.jsx)("p",{className:"text-gray-600 text-lg",children:e.description})]})]},t))})]})})}),(0,s.jsx)("section",{className:"bg-gradient-to-r from-indigo-600 to-purple-500 text-white py-20",children:(0,s.jsxs)("div",{className:"container mx-auto px-4 text-center",children:[(0,s.jsx)("h2",{className:"text-3xl md:text-4xl font-bold mb-6",children:"Ready to Build a Powerful Brand Identity?"}),(0,s.jsx)("p",{className:"text-xl mb-8 opacity-90 max-w-2xl mx-auto",children:"Let's create a brand identity that sets you apart from the competition and connects with your audience."}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,s.jsx)(n(),{href:"/contact",className:"bg-white text-indigo-600 font-bold px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 transform hover:scale-105",children:"Start Your Brand Journey"}),(0,s.jsx)(n(),{href:"/portfolio",className:"border-2 border-white text-white font-bold px-8 py-4 rounded-full hover:bg-white hover:text-indigo-600 transition-all duration-300",children:"View All Projects"})]})]})})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[874,283,441,684,358],()=>t(1436)),_N_E=e.O()}]);