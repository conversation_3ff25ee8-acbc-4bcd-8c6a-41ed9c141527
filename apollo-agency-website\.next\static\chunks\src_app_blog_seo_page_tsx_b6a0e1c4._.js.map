{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/agency/apollo-agency-website/src/app/blog/seo/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useLanguage } from '@/contexts/LanguageContext';\nimport Link from 'next/link';\n\nexport default function SEOBlogPage() {\n  const { t, isRTL } = useLanguage();\n\n  const posts = [\n    {\n      id: 1,\n      title: 'SEO in 2024: What\\'s Changed and What Still Works',\n      excerpt: 'Comprehensive overview of the latest SEO updates and strategies that continue to drive results.',\n      author: 'SEO Expert',\n      date: '2024-01-25',\n      readTime: '11 min read',\n      category: 'SEO Strategy',\n      image: '🔍'\n    },\n    {\n      id: 2,\n      title: 'Technical SEO Checklist: 50 Points for Better Rankings',\n      excerpt: 'Complete technical SEO checklist to ensure your website is optimized for search engines.',\n      author: 'Technical SEO Pro',\n      date: '2024-01-19',\n      readTime: '15 min read',\n      category: 'Technical SEO',\n      image: '⚙️'\n    },\n    {\n      id: 3,\n      title: 'Local SEO: Dominating Google My Business in 2024',\n      excerpt: 'Advanced local SEO strategies to improve your local search visibility and attract nearby customers.',\n      author: 'Local SEO Specialist',\n      date: '2024-01-14',\n      readTime: '9 min read',\n      category: 'Local SEO',\n      image: '📍'\n    },\n    {\n      id: 4,\n      title: 'Content SEO: Creating Content That Ranks and Converts',\n      excerpt: 'Learn how to create SEO-optimized content that ranks well and drives conversions.',\n      author: 'Content SEO Writer',\n      date: '2024-01-09',\n      readTime: '8 min read',\n      category: 'Content SEO',\n      image: '📝'\n    },\n    {\n      id: 5,\n      title: 'Link Building Strategies That Actually Work in 2024',\n      excerpt: 'Effective link building techniques to improve your domain authority and search rankings.',\n      author: 'Link Building Expert',\n      date: '2024-01-04',\n      readTime: '12 min read',\n      category: 'Link Building',\n      image: '🔗'\n    },\n    {\n      id: 6,\n      title: 'SEO Analytics: Measuring What Matters for Rankings',\n      excerpt: 'Essential SEO metrics to track and how to use data to improve your search performance.',\n      author: 'SEO Analyst',\n      date: '2023-12-30',\n      readTime: '10 min read',\n      category: 'SEO Analytics',\n      image: '📊'\n    }\n  ];\n\n  return (\n    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : ''}`}>\n      {/* Hero Section */}\n      <section className=\"bg-gradient-to-br from-green-900 via-teal-800 to-blue-600 text-white py-20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-4xl mx-auto text-center\">\n            <div className=\"w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6\">\n              <span className=\"text-3xl\">🔍</span>\n            </div>\n            <h1 className=\"text-4xl md:text-6xl font-bold mb-6\">\n              SEO Blog\n            </h1>\n            <p className=\"text-xl md:text-2xl mb-8 opacity-90\">\n              Master search engine optimization with expert insights and strategies\n            </p>\n            <Link\n              href=\"/contact\"\n              className=\"inline-block bg-gradient-to-r from-green-500 to-blue-500 text-white font-bold px-8 py-4 rounded-full hover:from-green-400 hover:to-blue-400 transition-all duration-300 transform hover:scale-105\"\n            >\n              Get SEO Help\n            </Link>\n          </div>\n        </div>\n      </section>\n\n      {/* Blog Posts Grid */}\n      <section className=\"py-20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-6xl mx-auto\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-6\">\n                Latest SEO Articles\n              </h2>\n              <p className=\"text-xl text-gray-600\">\n                Expert insights on search engine optimization and ranking strategies\n              </p>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n              {posts.map((post) => (\n                <article key={post.id} className=\"bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300\">\n                  <div className=\"bg-gradient-to-r from-green-100 to-blue-100 p-8 text-center\">\n                    <div className=\"text-6xl mb-4\">{post.image}</div>\n                    <span className=\"bg-white/80 text-green-600 px-3 py-1 rounded-full text-sm font-medium\">\n                      {post.category}\n                    </span>\n                  </div>\n\n                  <div className=\"p-6\">\n                    <h3 className=\"text-xl font-bold text-gray-900 mb-3 line-clamp-2\">\n                      {post.title}\n                    </h3>\n                    <p className=\"text-gray-600 mb-4 line-clamp-3\">\n                      {post.excerpt}\n                    </p>\n                    \n                    <div className=\"flex items-center justify-between text-sm text-gray-500 mb-4\">\n                      <span>{post.author}</span>\n                      <span>{post.readTime}</span>\n                    </div>\n                    \n                    <div className=\"flex items-center justify-between\">\n                      <span className=\"text-sm text-gray-500\">{post.date}</span>\n                      <Link\n                        href={`/blog/seo/${post.id}`}\n                        className=\"inline-flex items-center text-green-600 hover:text-green-700 font-medium\"\n                      >\n                        Read More\n                        <svg className={`w-4 h-4 ${isRTL ? 'mr-1 rotate-180' : 'ml-1'}`} fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                        </svg>\n                      </Link>\n                    </div>\n                  </div>\n                </article>\n              ))}\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* SEO Categories */}\n      <section className=\"py-20 bg-white\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-4xl mx-auto\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-6\">\n                SEO Topics\n              </h2>\n              <p className=\"text-xl text-gray-600\">\n                Explore different aspects of search engine optimization\n              </p>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n              {[\n                { name: 'Technical SEO', icon: '⚙️', count: '15 articles' },\n                { name: 'Local SEO', icon: '📍', count: '12 articles' },\n                { name: 'Content SEO', icon: '📝', count: '20 articles' },\n                { name: 'Link Building', icon: '🔗', count: '10 articles' },\n                { name: 'SEO Analytics', icon: '📊', count: '8 articles' },\n                { name: 'Mobile SEO', icon: '📱', count: '6 articles' },\n                { name: 'E-commerce SEO', icon: '🛒', count: '9 articles' },\n                { name: 'SEO Tools', icon: '🛠️', count: '7 articles' }\n              ].map((category, index) => (\n                <div\n                  key={index}\n                  className=\"text-center p-6 bg-gray-50 rounded-lg hover:bg-green-50 transition-colors duration-300\"\n                >\n                  <div className=\"text-4xl mb-3\">{category.icon}</div>\n                  <h3 className=\"text-lg font-bold text-gray-900 mb-2\">{category.name}</h3>\n                  <p className=\"text-gray-600 text-sm\">{category.count}</p>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"bg-gradient-to-r from-green-600 to-blue-500 text-white py-20\">\n        <div className=\"container mx-auto px-4 text-center\">\n          <h2 className=\"text-3xl md:text-4xl font-bold mb-6\">\n            Ready to Improve Your Search Rankings?\n          </h2>\n          <p className=\"text-xl mb-8 opacity-90 max-w-2xl mx-auto\">\n            Let our SEO experts help you rank higher and drive more organic traffic to your website.\n          </p>\n          <Link\n            href=\"/contact\"\n            className=\"bg-white text-green-600 font-bold px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 transform hover:scale-105\"\n          >\n            Get SEO Consultation\n          </Link>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;IAE/B,MAAM,QAAQ;QACZ;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,QAAQ;YACR,MAAM;YACN,UAAU;YACV,UAAU;YACV,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,QAAQ;YACR,MAAM;YACN,UAAU;YACV,UAAU;YACV,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,QAAQ;YACR,MAAM;YACN,UAAU;YACV,UAAU;YACV,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,QAAQ;YACR,MAAM;YACN,UAAU;YACV,UAAU;YACV,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,QAAQ;YACR,MAAM;YACN,UAAU;YACV,UAAU;YACV,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,QAAQ;YACR,MAAM;YACN,UAAU;YACV,UAAU;YACV,OAAO;QACT;KACD;IAED,qBACE,6LAAC;QAAI,WAAW,CAAC,wBAAwB,EAAE,QAAQ,QAAQ,IAAI;;0BAE7D,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAAW;;;;;;;;;;;0CAE7B,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAGpD,6LAAC;gCAAE,WAAU;0CAAsC;;;;;;0CAGnD,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;0BAQP,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAGlE,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAKvC,6LAAC;gCAAI,WAAU;0CACZ,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC;wCAAsB,WAAU;;0DAC/B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAiB,KAAK,KAAK;;;;;;kEAC1C,6LAAC;wDAAK,WAAU;kEACb,KAAK,QAAQ;;;;;;;;;;;;0DAIlB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEACX,KAAK,KAAK;;;;;;kEAEb,6LAAC;wDAAE,WAAU;kEACV,KAAK,OAAO;;;;;;kEAGf,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EAAM,KAAK,MAAM;;;;;;0EAClB,6LAAC;0EAAM,KAAK,QAAQ;;;;;;;;;;;;kEAGtB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAyB,KAAK,IAAI;;;;;;0EAClD,6LAAC,+JAAA,CAAA,UAAI;gEACH,MAAM,CAAC,UAAU,EAAE,KAAK,EAAE,EAAE;gEAC5B,WAAU;;oEACX;kFAEC,6LAAC;wEAAI,WAAW,CAAC,QAAQ,EAAE,QAAQ,oBAAoB,QAAQ;wEAAE,MAAK;wEAAO,QAAO;wEAAe,SAAQ;kFACzG,cAAA,6LAAC;4EAAK,eAAc;4EAAQ,gBAAe;4EAAQ,aAAa;4EAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uCA7BjE,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;0BA0C/B,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAGlE,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAKvC,6LAAC;gCAAI,WAAU;0CACZ;oCACC;wCAAE,MAAM;wCAAiB,MAAM;wCAAM,OAAO;oCAAc;oCAC1D;wCAAE,MAAM;wCAAa,MAAM;wCAAM,OAAO;oCAAc;oCACtD;wCAAE,MAAM;wCAAe,MAAM;wCAAM,OAAO;oCAAc;oCACxD;wCAAE,MAAM;wCAAiB,MAAM;wCAAM,OAAO;oCAAc;oCAC1D;wCAAE,MAAM;wCAAiB,MAAM;wCAAM,OAAO;oCAAa;oCACzD;wCAAE,MAAM;wCAAc,MAAM;wCAAM,OAAO;oCAAa;oCACtD;wCAAE,MAAM;wCAAkB,MAAM;wCAAM,OAAO;oCAAa;oCAC1D;wCAAE,MAAM;wCAAa,MAAM;wCAAO,OAAO;oCAAa;iCACvD,CAAC,GAAG,CAAC,CAAC,UAAU,sBACf,6LAAC;wCAEC,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;0DAAiB,SAAS,IAAI;;;;;;0DAC7C,6LAAC;gDAAG,WAAU;0DAAwC,SAAS,IAAI;;;;;;0DACnE,6LAAC;gDAAE,WAAU;0DAAyB,SAAS,KAAK;;;;;;;uCAL/C;;;;;;;;;;;;;;;;;;;;;;;;;;0BAcjB,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAsC;;;;;;sCAGpD,6LAAC;4BAAE,WAAU;sCAA4C;;;;;;sCAGzD,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAOX;GA5MwB;;QACD,sIAAA,CAAA,cAAW;;;KADV", "debugId": null}}]}