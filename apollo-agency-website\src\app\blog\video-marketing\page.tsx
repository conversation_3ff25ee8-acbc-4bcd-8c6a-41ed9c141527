'use client';

import { useLanguage } from '@/contexts/LanguageContext';
import Link from 'next/link';

export default function VideoMarketingBlogPage() {
  const { t, isRTL } = useLanguage();

  const posts = [
    {
      id: 1,
      title: 'YouTube Marketing Strategy: From Zero to 100K Subscribers',
      excerpt: 'Complete guide to building a successful YouTube channel and growing your subscriber base organically.',
      author: '<PERSON>',
      date: '2024-01-20',
      readTime: '15 min read',
      category: 'YouTube',
      image: '📺'
    },
    {
      id: 2,
      title: 'TikTok Video Marketing: Going Viral in 2024',
      excerpt: 'Learn the secrets to creating TikTok videos that capture attention and drive engagement.',
      author: '<PERSON>',
      date: '2024-01-14',
      readTime: '8 min read',
      category: 'TikTok',
      image: '🎵'
    },
    {
      id: 3,
      title: 'Instagram Reels vs. Stories: Which Format Drives More Engagement?',
      excerpt: 'Compare Instagram video formats and learn when to use Reels vs Stories for maximum impact.',
      author: '<PERSON>',
      date: '2024-01-09',
      readTime: '6 min read',
      category: 'Instagram',
      image: '📱'
    },
    {
      id: 4,
      title: 'Live Streaming for Business: Best Practices and Platforms',
      excerpt: 'Master live streaming to connect with your audience in real-time and build authentic relationships.',
      author: 'Rachel Green',
      date: '2024-01-04',
      readTime: '10 min read',
      category: 'Live Streaming',
      image: '🔴'
    },
    {
      id: 5,
      title: 'Video SEO: Optimizing Your Videos for Search Discovery',
      excerpt: 'Essential techniques to make your videos discoverable on YouTube, Google, and other platforms.',
      author: 'Tom Wilson',
      date: '2023-12-28',
      readTime: '9 min read',
      category: 'Video SEO',
      image: '🔍'
    },
    {
      id: 6,
      title: 'Creating Compelling Video Content on a Budget',
      excerpt: 'Professional video marketing strategies that don\'t require expensive equipment or large budgets.',
      author: 'Lisa Chang',
      date: '2023-12-22',
      readTime: '7 min read',
      category: 'Budget Video',
      image: '💡'
    }
  ];

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : ''}`}>
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-red-900 via-pink-800 to-purple-600 text-white py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6">
              <span className="text-3xl">🎬</span>
            </div>
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Video Marketing Blog
            </h1>
            <p className="text-xl md:text-2xl mb-8 opacity-90">
              Master video marketing across all platforms and formats
            </p>
            <Link
              href="/contact"
              className="inline-block bg-gradient-to-r from-red-500 to-pink-500 text-white font-bold px-8 py-4 rounded-full hover:from-red-400 hover:to-pink-400 transition-all duration-300 transform hover:scale-105"
            >
              Get Video Marketing Help
            </Link>
          </div>
        </div>
      </section>

      {/* Blog Posts Grid */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Latest Video Marketing Articles
              </h2>
              <p className="text-xl text-gray-600">
                Expert insights on video content and marketing strategies
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {posts.map((post) => (
                <article key={post.id} className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300">
                  <div className="bg-gradient-to-r from-red-100 to-pink-100 p-8 text-center">
                    <div className="text-6xl mb-4">{post.image}</div>
                    <span className="bg-white/80 text-red-600 px-3 py-1 rounded-full text-sm font-medium">
                      {post.category}
                    </span>
                  </div>

                  <div className="p-6">
                    <h3 className="text-xl font-bold text-gray-900 mb-3 line-clamp-2">
                      {post.title}
                    </h3>
                    <p className="text-gray-600 mb-4 line-clamp-3">
                      {post.excerpt}
                    </p>
                    
                    <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                      <span>{post.author}</span>
                      <span>{post.readTime}</span>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500">{post.date}</span>
                      <Link
                        href={`/blog/video-marketing/${post.id}`}
                        className="inline-flex items-center text-red-600 hover:text-red-700 font-medium"
                      >
                        Read More
                        <svg className={`w-4 h-4 ${isRTL ? 'mr-1 rotate-180' : 'ml-1'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                      </Link>
                    </div>
                  </div>
                </article>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-gradient-to-r from-red-600 to-pink-500 text-white py-20">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Ready to Create Engaging Video Content?
          </h2>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Let our video marketing experts help you create content that captivates and converts.
          </p>
          <Link
            href="/contact"
            className="bg-white text-red-600 font-bold px-8 py-4 rounded-full hover:bg-gray-100 transition-all duration-300 transform hover:scale-105"
          >
            Start Video Marketing
          </Link>
        </div>
      </section>
    </div>
  );
}
